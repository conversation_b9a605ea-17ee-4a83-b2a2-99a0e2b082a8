import CommonModal from "@/components/CommonModal";
import { useAllGame } from "@/components/games/AllGameProvider";
import HintContent from "@/components/hint/HintContent";
import EventBus from "@/utils/EventBus";
import { Button } from "antd";
import { memo } from "react";
import { useNavigate } from "react-router-dom";
import { useJump } from "../context/useJumpContext";

const PlayNowModal: React.FC<{ handleCancel: () => void; showModal: boolean }> = memo(
  ({ showModal, handleCancel }) => {
    const handleClose = () => {};
    const { allConfigData } = useAllGame();
    const navigate = useNavigate();
    const { showHint } = useJump() || {};

    const handGame = () => {
      const preGame = localStorage.getItem("preGame");
      if (preGame) {
        navigate(preGame);
        handleCancel();
      } else {
        navigate("/");
        handleCancel();
      }
      EventBus.emit("closeDepositBrModal");
    };

    return (
      <>
        <CommonModal
          width={window.screen.width > 1024 ? "20%" : "90%"}
          footer={null}
          open={showModal}
          onCancel={handleCancel}
          afterClose={handleClose}
          maskClosable={false}
        >
          <div className="text-primary-text flex flex-col w-full  py-4 px-2 bg-[#1B1E23] relative rounded-[16px] overflow-hidden">
            {showHint && <HintContent popupId={showHint} />}
            <span className="mb-[14px] ml-5 font-semibold text-[14px]/[20px]" style={{ zIndex: 10 }}>
              {allConfigData?.gamePopList?.[0]?.title}
            </span>
            <div className="flex flex-col " style={{ zIndex: 2 }}>
              <div className="w-full relative h-[116px]">
                <img
                  src={allConfigData?.gamePopList?.[0]?.icon}
                  alt=""
                  className="object-cover absolute"
                  style={{ zIndex: 3, top: "50%", left: "50%", transform: "translate(-50%, -50%)"}}
                />
              </div>
              <span
                className="mt-[10px] flex items-center justify-center" 
                style={{ zIndex: 10 }}
                dangerouslySetInnerHTML={{ __html: allConfigData?.gamePopList?.[0]?.desc }}
              ></span>
              <div className="mt-[17px] flex items-center justify-around w-full " style={{ zIndex: 10 }}>
                <Button className="btn !h-[36px] !w-full" onClick={handGame}>
                  {allConfigData?.gamePopList?.[0]?.text}
                </Button>
              </div>
            </div>
          </div>
        </CommonModal>
      </>
    );
  }
);

export default PlayNowModal;
