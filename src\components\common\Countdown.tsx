import React, { useState, useEffect } from 'react';

interface CountdownProps {
  targetTimestamp: number; // 目标时间戳，单位是毫秒
}

const Countdown: React.FC<CountdownProps> = ({ targetTimestamp }) => {
  const [time, setTime] = useState<string>('--');

  useEffect(() => {
    if (typeof targetTimestamp !== 'number' || targetTimestamp <= 0) {
      setTime('--');
      return;
    }

    const intervalId = setInterval(() => {
      const now = Date.now(); // 当前时间戳
      const remainingTime = targetTimestamp - now;

      if (remainingTime <= 0) {
        clearInterval(intervalId);
        setTime('00:00:00');
        return;
      }

      const seconds = Math.floor(remainingTime / 1000) % 60;
      const minutes = Math.floor(remainingTime / 60000) % 60;
      const hours = Math.floor(remainingTime / 3600000);

      setTime(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
    }, 1000);

    return () => clearInterval(intervalId);
  }, [targetTimestamp]);

  return <div>{time}</div>;
};

export default Countdown;