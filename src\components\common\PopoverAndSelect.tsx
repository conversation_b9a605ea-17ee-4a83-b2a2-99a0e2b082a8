import { DatePicker } from "antd";
import { Popover } from "antd";
import { useEffect, useRef, useState } from "react";

const PopoverAndSelect: React.FC = ({ datas, handleChangeDays }) => {
  const currencyDiv = useRef(null);
  const [open, setOpen] = useState(false);
  const [dateOpen, setDateOpen] = useState(false);
  const [currencyWidth, setCurrencyWidth] = useState("0px");
  const [content, setContent] = useState([]);
  const [selectedValue, setSelectedValue] = useState(datas[0]);
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
  };
  const handleDateOpenChange = (newOpen: boolean) => {
    if (!setDateOpen) {
      setOpen(true);
    }
  };

  function handlerCalendarChange(value, dateString) {
    console.log(value, dateString);
  }

  function handlerClick(item) {
    setSelectedValue(item);
    setOpen(false);
    handleChangeDays(item.value);
  }

  useEffect(() => {
    let newContent = [];

    if (datas && datas.length > 0) {
      newContent = datas.map((item, index) => (
        <div
          key={index}
          className="justify-center text-sub-text-light-gray flex items-center cursor-pointer"
        >
          <div className="hover:bg-[--profile-button-bg-color] w-[96%] h-[96%] space-y-2 flex">
            <span className="ml-3 m-1 rounded-md w-full" onClick={() => handlerClick(item)}>
              {item.label}
            </span>
          </div>
        </div>
      ));
    }

    newContent.push(
      <div key="datePicker2" className="w-full !placeholder:overflow-hidden">
        <DatePicker.RangePicker
          placeholder={["Custom", "Custom"]}
          style={{
            color: "var(--profile-joind-text-color)",
            border: "none"
          }}
          allowClear={false}
          showTime={{ showNow: false }}
          onCalendarChange={handlerCalendarChange}
          format={"YYYY-MM-DD"}
        />
      </div>
    );

    setContent(newContent);

    if (currencyDiv.current) {
      setCurrencyWidth(`${currencyDiv.current.offsetWidth}px`);
    }
  }, [datas]);
  return (
    <>
      <Popover
        open={open}
        onOpenChange={handleOpenChange}
        content={content}
        placement="bottom"
        trigger="click"
        styles={{
          body: {
            width: currencyWidth,
            borderRadius: "6px",
            padding: "0px",
            borderWidth: "1px",
            border: "0px",
            overflow: "hidden",

            backgroundColor: "var(--main-bg-color)"
          }
        }}
      >
        <div
          ref={currencyDiv}
          className="w-full px-3 rounded-md text-sub-text-light-gray items-center justify-center flex bg-[--profile-button-bg-color]"
        >
          {selectedValue.label}
        </div>
      </Popover>
    </>
  );
};
export default PopoverAndSelect;
