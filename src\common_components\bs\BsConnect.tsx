import { usePersonInfoUtils } from "../usePersonInfoUtils";

import { memo } from "react";
import { FormattedMessage } from "react-intl";

import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { PlayerInfo } from "@/types/common";

import copy from "copy-to-clipboard";

const BsConnect = memo(() => {
  const { personValue } = usePersonInfoUtils() as { personValue: PlayerInfo };
  const { Toast, intl } = useToastUtils();
  function copyText(text: string) {
    copy(text);
    Toast(intl.formatMessage({ id: "copy_successfully" }));
  }

  return (
    <div className={`h-full bg-[#151930] flex items-center justify-center flex-col  mb-[200px]`}>
      <span style={{ fontFamily: "Inter" }} className="text-white text-[20px] font-semibold w-full">
        <FormattedMessage id="please_contact_your_agent_to_deposit" />
      </span>
      <div className="w-full flex h-[45px] px-[14px] mt-[16px] items-center bg-[#22263C] text-white">
        <span className="flex-1 flex items-center gap-x-1 truncate">
          <img src={personValue?.currencyTraderData?.head} className="w-[36px] h-[36px]" />
          <span className="text-pxs font-medium">
            {personValue?.currencyTraderData?.name}
          </span>
        </span>
        <span className="flex-1 flex items-center gap-x-1 truncate pr-2">
          <img
            onClick={() => window.open(personValue?.currencyTraderData?.mediaLink, "_blank")}
            src={personValue?.currencyTraderData?.mediaIcon}
            className="w-[20px] h-[20px] cursor-pointer flex-shrink-0"
          />
          <span
            onClick={() => window.open(personValue?.currencyTraderData?.mediaLink, "_blank")}
            className=" underline-current text-[#3686FF] text-pxs font-medium truncate underline"
          >
            {personValue?.currencyTraderData?.contactDetails}
          </span>
          <span className="flex-shrink-0 ml-auto">
            <iconpark-icon
              name="copy"
              color="#D4D4D5"
              size="16"
              onClick={() => copyText(personValue?.currencyTraderData?.mediaLink)}
            ></iconpark-icon>
          </span>
        </span>
      </div>
    </div>
  );
});

export default BsConnect;
