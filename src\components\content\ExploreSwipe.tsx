// import IconBanner from "@/assets/content/icon-banner-title.svg";
import { useJump } from "@/common_components/context/useJumpContext";
import { BackStageMessage } from "@/protos/BackStage";
import { setActivitySource } from "@/store/popup";
import { Image } from "antd";
import { memo, useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
// import { useActivated, useDeactivated } from "react-route-cache";
import { useLocation, useNavigate } from "react-router-dom";
import "swiper/css"; // 引入 Swiper 的 CSS 文件
import "swiper/css/autoplay"; // 引入 autoplay 的 CSS
import { Autoplay, Pagination } from "swiper/modules"; // 引入 autoplay 模块
import { Swiper, SwiperSlide } from "swiper/react";
import { useScreenWidth } from "../../utils/MobileDetectionContext";
import { useAllGame } from "../games/AllGameProvider";
import { useUtils } from "../useUtils";
import SwipeTopControl from "./SwipeTopControl";
import { useActivated, useDeactivated } from "react-route-cache";

const ExploreSwipe: React.FC = memo(({ mobielheight = "150", modal = 1 }) => {
  console.log("ExploreSwipe执行了============================");
  const { screenWidth } = useScreenWidth();
  const [bannerData, setBannerData] = useState<BackStageMessage.BannerInfo[] | null>(null);
  const { getCurrentLangData, intl } = useUtils();
  const swiperRef = useRef(null);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const { allConfigData } = useAllGame() as {
    allConfigData: BackStageMessage.ResConfigDataMessage;
  };
  useActivated(() => {
    if (swiperRef.current) {
      swiperRef.current?.autoplay?.start(); // 启动自动轮播
      swiperRef.current?.update(); // 更新 Swiper
    }
  });

  useDeactivated(() => {
    swiperRef.current?.autoplay?.stop(); // 停止自动轮播
  });

  const { handleJump, setDepostiSource } = useJump() || {};
  const [isFirstPage, setIsFirstPage] = useState(false);
  const [isLastPage, setIsLastPage] = useState(false);
  const next = () => {
    swiperRef.current?.slideNext();
  };
  const prev = () => {
    swiperRef.current?.slideNext();
  };

  useEffect(() => {
    const currentLangu = getCurrentLangData().paramCode;
    if (allConfigData && allConfigData.bannerList) {
      console.log("首页banner", allConfigData?.bannerList);
      setBannerData(
        allConfigData?.bannerList
          .filter((item) => item.status == 1)
          .filter((item) => item.index == 2 && item.language == currentLangu)
      );
    }
  }, [allConfigData]);

  const handleCustomer = (banner: BackStageMessage.IBannerInfo, e: React.MouseEvent) => {
    if (banner.isJump > 0) {
      const jumpType = banner?.jumpType;
      const url = banner?.externalLinks;
      const popupLinks = banner?.popupLinks;
      const innerLinks = banner?.innerLinks;
      console.log("banner点击弹框", jumpType, url, popupLinks);
      e.stopPropagation();
      const popupInfo = BackStageMessage.PopupInfo.create({
        jumpType: jumpType,
        popupLinks: popupLinks,
        externalLinks: url,
        popupType: 1,
        innerLinks: innerLinks,
        notLoginJump: banner.notLoginJump,
        firstRechargeAmount: banner?.firstRechargeAmount || 0,
        firstRechargeCurrencyId: banner?.firstRechargeCurrencyId
      });
      const jumpWhere = popupLinks;
      if (innerLinks) {
        setDepostiSource(205);
      } else if (jumpWhere === 14) {
        // 直充
        popupInfo.depositSource = 304;
      } else {
        // 充值弹框
        popupInfo.depositSource = 112;
      }

      if (location.pathname == "/") {
        dispatch(setActivitySource(1));
      } else {
        dispatch(setActivitySource(8));
      }
      handleJump(popupInfo, navigate);
    }
  };

  // 根据屏幕宽度决定类名
  const containerClass =
    screenWidth >= 1024
      ? "mt-[12px] relative w-full flex justify-center items-center"
      : ` relative min-h-[${mobielheight}px] w-full`;

  return (
    <>
      {bannerData?.length > 0 && (
        <div className="flex flex-col w-full relative select-none ">
          {screenWidth > 1024 && (
            <SwipeTopControl
              isSvg={true}
              isFirstPage={isFirstPage}
              isLastPage={isLastPage}
              IconBanner={
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 40 40"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g filter="url(#filter0_d_4596_2403)">
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M9.44881 13.1151C9.41529 13.2858 9.38182 13.4563 9.31982 13.6202L9.32064 14.5669C9.39051 14.6379 9.39681 14.7284 9.40296 14.8167C9.4052 14.8488 9.40742 14.8807 9.41268 14.9112C9.80714 17.1859 11.7819 18.8656 14.0944 18.8755C14.7924 18.8786 15.4906 18.8782 16.1888 18.8779H16.1892C16.9985 18.8776 17.8077 18.8772 18.6167 18.8821C18.8345 18.8829 18.8977 18.8245 18.8961 18.6043C18.8877 17.4653 18.8884 16.3263 18.8891 15.1869C18.8893 14.8712 18.8895 14.5555 18.8895 14.2398C18.8895 13.8971 18.8566 13.5585 18.7934 13.2232C18.343 10.836 16.1784 9.00749 13.4518 9.38305C11.5197 9.64931 10.2205 10.7858 9.56636 12.6406C9.51155 12.7955 9.48016 12.9554 9.44881 13.1151ZM23.0919 18.8767H23.0919H23.0919C23.296 18.8771 23.5002 18.8774 23.7043 18.8774H23.706C23.9155 18.8774 24.125 18.878 24.3346 18.8786H24.3348H24.3349H24.3349H24.335C24.8086 18.88 25.2824 18.8815 25.7563 18.8757C27.5881 18.8527 29.0246 18.0696 29.9819 16.5205C30.687 15.3807 30.8473 14.1086 30.4791 12.8102C29.7995 10.4163 27.5248 8.99871 25.0759 9.40549C22.8571 9.77447 21.1913 11.6555 21.1215 13.9582C21.0912 14.9484 21.0967 15.9395 21.1022 16.9306V16.9307V16.9308V16.9308V16.9309C21.1054 17.5001 21.1086 18.0692 21.105 18.6382C21.1034 18.8462 21.1782 18.8831 21.3647 18.8815C21.9404 18.8748 22.516 18.8758 23.0917 18.8767H23.0919ZM9.31967 25.3526C9.40575 25.2324 9.42319 25.0932 9.44052 24.9548C9.44784 24.8963 9.45514 24.838 9.46759 24.7814C9.85465 23.0121 11.4086 21.5149 13.1944 21.1993C13.5658 21.1335 13.9389 21.0999 14.3169 21.1007C15.746 21.104 17.1751 21.1056 18.6033 21.0974C18.8121 21.0966 18.8893 21.1377 18.8885 21.3669C18.8836 22.1122 18.8844 22.8574 18.8852 23.6025V23.6029C18.886 24.348 18.8869 25.093 18.8819 25.8383C18.8754 26.785 18.642 27.6782 18.1078 28.4663C17.2984 29.6604 16.1873 30.4033 14.7426 30.6112C14.7283 30.6133 14.7131 30.6133 14.6978 30.6133C14.6527 30.6134 14.6069 30.6135 14.5799 30.6662H13.6858C13.5945 30.6076 13.4925 30.5984 13.3901 30.5893C13.3377 30.5846 13.2851 30.5799 13.2338 30.5685C11.3256 30.1419 10.0765 29.0079 9.50703 27.1301C9.47514 27.0254 9.4571 26.9175 9.43908 26.8097C9.41298 26.6536 9.38691 26.4977 9.31885 26.3519L9.31967 25.3526ZM25.1215 30.5832C25.2086 30.5894 25.2974 30.5957 25.3658 30.6671L25.3666 30.6688H26.2607C26.3823 30.6174 26.5107 30.6022 26.6394 30.5871C26.7246 30.577 26.81 30.567 26.8935 30.5463C27.6224 30.3647 28.2823 30.0705 28.8699 29.5775C29.6563 28.9184 30.2192 28.1328 30.4847 27.1409C30.5332 26.9584 30.5595 26.7686 30.5792 26.5804C30.589 26.4875 30.6121 26.4136 30.6803 26.3536V25.4595C30.6389 25.4385 30.6347 25.4014 30.6303 25.3629C30.6298 25.3579 30.6292 25.3528 30.6285 25.3477C30.2899 22.8347 28.3226 21.1098 25.7857 21.104C25.272 21.1029 24.7581 21.1034 24.2441 21.1039C23.3031 21.1048 22.362 21.1057 21.4212 21.0966C21.1566 21.0942 21.1089 21.178 21.1106 21.4221C21.1182 22.4108 21.1167 23.3998 21.1152 24.389C21.1146 24.7935 21.1139 25.198 21.1139 25.6025C21.1139 25.8564 21.1229 26.1087 21.1492 26.3626C21.3653 28.4763 22.93 30.1782 25.0239 30.5726C25.0554 30.5785 25.0883 30.5808 25.1215 30.5832Z"
                      fill="url(#paint0_linear_4596_2403)"
                    />
                  </g>
                  <defs>
                    <filter
                      id="filter0_d_4596_2403"
                      x="3.31885"
                      y="3.33398"
                      width="33.3616"
                      height="33.3348"
                      filterUnits="userSpaceOnUse"
                      colorInterpolationFilters="sRGB"
                    >
                      <feFlood floodOpacity="0" result="BackgroundImageFix" />
                      <feColorMatrix
                        in="SourceAlpha"
                        type="matrix"
                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                        result="hardAlpha"
                      />
                      <feOffset />
                      <feGaussianBlur stdDeviation="3" />
                      <feComposite in2="hardAlpha" operator="out" />
                      <feColorMatrix
                        type="matrix"
                        values="0 0 0 0 0.301961 0 0 0 0 0.87451 0 0 0 0 0.32549 0 0 0 0.69 0"
                      />
                      <feBlend
                        mode="normal"
                        in2="BackgroundImageFix"
                        result="effect1_dropShadow_4596_2403"
                      />
                      <feBlend
                        mode="normal"
                        in="SourceGraphic"
                        in2="effect1_dropShadow_4596_2403"
                        result="shape"
                      />
                    </filter>
                    <linearGradient
                      id="paint0_linear_4596_2403"
                      x1="19.9996"
                      y1="9.33398"
                      x2="19.9996"
                      y2="30.6688"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor={"var(--main-theme-color)"} />
                      <stop offset="1" stopColor={"var(--main-theme-color)"} />
                    </linearGradient>
                  </defs>
                </svg>
              }
              prev={prev}
              next={next}
              text={intl.formatMessage({ id: "Explore Wingame" })}
              showAllGames={false}
            />
          )}

          <div className={containerClass}>
            {bannerData?.length > 0 && (
              <Swiper
                spaceBetween={10} // 幻灯片之间的间隔
                slidesPerView={1} // 自动计算显示个数
                direction={"horizontal"} // 水平方向
                loop={true}
                autoplay={{
                  delay: 2000
                }}
                speed={600}
                modules={[Autoplay, Pagination]}
                onSwiper={(swiper) => (swiperRef.current = swiper)}
                className="w-full"
                breakpoints={{
                  1024: {
                    slidesPerView: modal == 1 ? 3 : 1,
                    spaceBetween: 10
                  }
                }}
                pagination={
                  bannerData?.length > 1
                    ? {
                        clickable: true,
                        renderBullet: (index, className) => {
                          return `<div class='mobile-custom-dot ${className}'></div>`;
                        }
                      }
                    : false
                }
              >
                {bannerData?.map((banner, index) => (
                  <SwiperSlide key={banner.bannerId}>
                    <div
                      onClick={(e) => handleCustomer(banner, e)}
                      className={`${
                        banner.isJump > 0 && "cursor-pointer"
                      } rounded-lg grid grid-cols-1 aspect-[2.43]`}
                      style={{ contentVisibility: "auto" }}
                    >
                      <Image
                        preview={false}
                        sizes="(max-width: 480px) 400px"
                        loading="eager"
                        src={banner.fileUrl}
                        width={`100%`}
                        height={"100%"}
                        className={` rounded-lg object `}
                        style={{background: "linear-gradient(90deg, #20255A 7.7%, #0E1641 101.22%)"}}
                      />

                      <div className="absolute z-10 h-full w-[200px] top-0 px-6">
                        <div className="relative h-[24px] rounded-sm top-4 flex  items-center">
                          <span className="text-primary-text text-lg font-semibold ">
                            {banner.title}
                          </span>
                        </div>
                        <div className="mt-1 relative h-[24px]  rounded-sm top-4 flex items-center">
                          <span className=" font-bold ">{banner.subtitle}</span>
                        </div>
                        {banner.button == 1 && (
                          <div className="relative h-[24px] w-[65%] top-[40%] flex justify-center items-center">
                            <button
                              onClick={(e) =>
                                handleCustomer(
                                  banner?.jumpType,
                                  banner?.externalLinks,
                                  banner?.popupLinks,
                                  banner?.innerLinks,
                                  e
                                )
                              }
                              className="btn w-full"
                            >
                              {banner.buttonWord}
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            )}
          </div>
        </div>
      )}
    </>
  );
});

export default ExploreSwipe;
