import copyIcon from "@/assets/copy.svg";
import googleAuth_1Icon from "@/assets/googleAuth/googleAuth_1.svg";
import googleAuth_2Icon from "@/assets/googleAuth/googleAuth_2.svg";
import CommonModal from "@/components/CommonModal";
import HintContent from "@/components/hint/HintContent";
import ScreenSpin from "@/components/ScreenSpin2";
import { useUtils } from "@/components/useUtils";
import { HallMessage } from "@/protos/Hall";
import { enable2FAData, get2FAVerificationCode } from "@/service/hall";
import { useScreenWidth } from "@/utils/MobileDetectionContext";
import { Button, Input } from "antd";
import { useEffect, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useJump } from "../context/useJumpContext";
import { usePersonInfoUtils } from "../usePersonInfoUtils";
import copy from "copy-to-clipboard";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import EventBus from "@/utils/EventBus";
import { ModalType } from "@/utils/enums";
import { AnimatePresence, motion } from "framer-motion";

const TwoStepBrazilVerifi: React.FC = ({ showModal, handleCancel }) => {
  if (!showModal) return null;
  const [values, setValues] = useState(["", "", "", "", "", ""]);
  const { screenWidth } = useScreenWidth()  ;
  const [twofaData, setTwofaData] = useState<any>({});
  const { hanldeMask } = useUtils();
  const { intl, Toast, checkResponse } = useToastUtils();
  const { updateInfo } = usePersonInfoUtils();
  const [qrLoading, setQrLoading] = useState(true);
  const [verificationCode, setVerificationCode] = useState();
  const { show2FA_showGoogleTip, setShow2FA_showGoogleTip, showHint } = useJump() || {};

  function handleClose() {
    setValues(["", "", "", "", "", ""]);
    setShow2FA_showGoogleTip(false)
    console.log('关闭twostep');
    
  }

  useEffect(() => {
    const fetchData = async () => {
      const res = (await enable2FAData({
        msgID: 400033
      })) as HallMessage.ResEnable2FADataMessage;
      console.log("获取到的2步验证数据为：", res);
      if (checkResponse(res)) {
        setTwofaData(res);
      }
    };
    if (showModal) {
      fetchData();
    }
  }, [showModal]);

  const input0Ref = useRef();
  const input1Ref = useRef();
  const input2Ref = useRef();
  const input3Ref = useRef();
  const input4Ref = useRef();
  const input5Ref = useRef();
  const captchaRefs = useRef([input0Ref, input1Ref, input2Ref, input3Ref, input4Ref, input5Ref]);
  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace") {
      console.log("退格键按下", index);
      // 如果按下了退格键且不是第一个输入框，则聚焦到前一个输入框
      e.preventDefault();
      const newValues = [...values];
      newValues[index] = "";
      console.log("newValues", newValues);

      setValues(newValues);
      if (index > 0) {
        captchaRefs.current[index - 1].current.focus();
      }
    }
  };

  function handlerClick(e, index) {
    console.log(e.target.value, index);
    const newValues = [...values];
    newValues[index] = e.target.value;
    setValues(newValues);
    if (index < 5) {
      captchaRefs.current[index + 1].current.focus();
    }
  }

  async function handlerEnable() {
    if (!verificationCode || verificationCode.length < 6) {
      Toast(intl.formatMessage({ id: "please_input___code" }), "error");
      return;
    }
    try {
      hanldeMask(true);
      const res = await get2FAVerificationCode({
        msgID: 400035,
        verificationCode: verificationCode
      });
      console.log("返回2FA Verification Code数据为：", res);
      if (checkResponse(res)) {
        Toast(intl.formatMessage({ id: "success" }));
        updateInfo({enable2FA: true})
        if (show2FA_showGoogleTip) {
          EventBus.emit("showModal", ModalType.showGoogleAuthCodeModal);
        }
        handleCancel();
      }
    } finally {
      hanldeMask(false);
    }
  }

  const loadQr = () => {
    setQrLoading(false);
  };

  const handleCodeValue = (value: string) => {
    console.log("得到输入", value);

    if (/^\d*$/.test(value)) {
      setVerificationCode(value); // 更新状态
    }
  };

  const handleCopy = async (text: string) => {
    copy(twofaData?.secretKey);
    Toast(intl.formatMessage({id:"copy_successfully"}));
  };

  const handleMyCancel = () => {
    setShow2FA_showGoogleTip(false)
    handleCancel();
  }

  return (
    <>
      <AnimatePresence>
      {showModal && (
        <div className="fixed inset-0 z-[1001] w-full  flex justify-center items-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-[2px] w-[100%] flex justify-between items-center"
          onClick={(e) => {
            e.stopPropagation();
            handleCancel();
          }}
        />
        <div className="relative w-[90%]">
          <motion.div
            initial={{ opacity: 0, y: "100%" }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: "100%" }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="flex flex-col rounded-2xl w-[100%] max-h-[95dvh] overflow-y-auto"
            style={{
              borderRadius: '12px',
              background: 'linear-gradient(219deg, #202130 6.45%, #2B2A43 93.63%)',
              boxShadow: '0px 4px 4px 0px rgba(8, 1, 52, 0.25), 0px 0px 2px 0px #5158A5 inset, 0px 0px 3px 0px #000 inset',
            }}
          >
        <div className="p-4 rounded-tl-2xl rounded-tr-2xl text-lg/6 font-medium flex justify-between items-center">
          <span className="text-white">
            <FormattedMessage id="google_authenticator" />
          </span>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none" onClick={() => handleMyCancel()}>
            <path d="M5 5.5L19 19.5" stroke="#E8E9E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M5 19.5L19 5.5" stroke="#E8E9E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div className="flex flex-col w-full px-5 pb-5 min-h-550px overflow-y-auto select-none gap-y-[14px] relative">
          {showHint && <HintContent popupId={showHint} />}
          <div className="flex flex-col py-4 gap-y-[8px]">
            <div className="w-[100%] flex items-center gap-y-[14px] gap-x-[8px]">
              <div className="bg-[white] rounded-md max-w-[160px] p-[3.242px]">
                {/* <ScreenSpin loading={qrLoading} /> */}
                {/* <iframe
                  src={twofaData?.qrCode}
                  className="w-[107px] h-[107px] flex items-center justify-center overflow-hidden"
                  scrolling="no"
                  frameBorder="0"
                  onLoad={() => loadQr()}
                /> */}
                <img src={twofaData?.qrCode} alt="" width={107} height={107} className="w-[160px] object-cover"/>
              </div>
              <div className="flex flex-col gap-y-[8px] lg:pr-10">
                <span className="text-[#D4D4D5] text-[14px]">
                  <FormattedMessage id="google_authenticator_desc" />
                </span>
                <span className="text-[#FAD900] text-[12px] font-medium">
                  <FormattedMessage id="install_google_authenticator" />
                </span>
                <div className="flex items-center gap-x-[14px]">
                  <Button
                    onClick={() =>
                      window.open(
                        "https://apps.apple.com/us/app/google-authenticator/id388497605",
                        "_blank"
                      )
                    }
                    className="flex items-center gap-x-1 btn !text-[#FFF] !text-pxs !font-medium flex-1"
                  >
                    <img src={googleAuth_1Icon} alt="" />
                    <span>IOS</span>
                  </Button>
                  <Button
                    onClick={() =>
                      window.open(
                        "https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2",
                        "_blank"
                      )
                    }
                    className="flex items-center gap-x-1 btn !text-[#FFF] !text-pxs !font-medium flex-1"
                  >
                    <img src={googleAuth_2Icon} alt="" />
                    <span>Google Play</span>
                  </Button>
                </div>
              </div>
              
            </div>
            {/* <div
              className=" self-start flex  text-sm "
              style={{ color: "rgba(255, 255, 255, 0.80)" }}
            >
              <span>
                <FormattedMessage id="account" />
              </span>
            </div>
            <div className=" w-full ">
              <div className="h-[40px] !bg-[#151423] border-none rounded-[12px] flex items-center text-white px-[12px]">
                123 * 456 * 689 = 88
              </div>
            </div> */}
            <div
              className=" self-start flex  text-sm "
              style={{ color: "rgba(255, 255, 255, 0.80)" }}
            >
              <span>
                <FormattedMessage id="secret_key" />
              </span>
            </div>
            <div className=" w-full ">
              <Input
                readOnly
                value={twofaData?.secretKey}
                className="h-[40px] !bg-[#151423] border-none rounded-[12px] flex"
                suffix={
                  <img
                    onClick={() => handleCopy(twofaData?.secretKey)}
                    src={copyIcon}
                    alt="secretKeyIcon"
                    className="w-5 h-5 cursor-pointer text-sm"
                  />
                }
              />
            </div>
            <div
              className=" self-start flex text-[11px]/[14px]"
              style={{ color: "rgba(255, 255, 255, 0.80)" }}
            >
              <FormattedMessage id="google_auth_code_input" />
            </div>
            <div>
              <span
                className="text-[--wallet-title-text-color] text-sm"
                style={{ color: "rgba(255, 255, 255, 0.80)" }}
              >
                <FormattedMessage id="verify_code" />
              </span>
            </div>
            <div className=" w-full ">
              <Input
                value={verificationCode}
                placeholder={intl.formatMessage({ id: "enter_the_6-digit_code" })}
                className="h-[40px] !bg-[#151423] border-none rounded-[12px] flex"
                onChange={(e) => handleCodeValue(e.target.value)}
                maxLength={6}
              />
            </div>
          </div>
          <div className="flex flex-col">
            <Button
               disabled={!verificationCode}
              onClick={handlerEnable}
              className={`!text-base !font-semibold button_shadow_common_ w-full border-none rounded-[100px] !h-[44px] ${verificationCode ? '' : ' opacity-50 '}`}
            >
              <FormattedMessage id="submit" />
            </Button>
          </div>
          {show2FA_showGoogleTip && (
            <div
              onClick={() => {
                handleCancel();
                setShow2FA_showGoogleTip(false);
                EventBus.emit("showModal", ModalType.showOtpModal);
              }}
              className=" cursor-pointer w-full items-center justify-end text-[#3EE749] text-end text-sm font-medium mt-[12px]"
            >
              <span>
                <FormattedMessage id="otp_withdraw" />
              </span>
            </div>
          )}
        </div>
        </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
    </>
  );
};

export default TwoStepBrazilVerifi;
