import { setIsIOSPwa } from "@/store/iosPwa";
import { isInAppBrowser, isIOS, openInChrome } from "@/utils/deviceDetect";
import { createContext, useCallback, useContext, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { usePoint } from "../usePoint";

const DeferredContext = createContext();

const DeferredProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
  // deferredPrompt
}) => {
  const [deferredPromptEvent, setDeferredPromptEvent] = useState<
    (Event & { prompt?: () => void }) | null
  >(null);
  const dispatch = useDispatch()
  const {pwaPoint} = usePoint()

  useEffect(() => {
    const isInstalled = localStorage.getItem("pwa_install");
    if (!isInstalled) {
      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

      return () => {
        window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      };
    }
  }, []);

  const handleBeforeInstallPrompt = (event: Event & { prompt?: () => void }) => {
    console.log("handleBeforeInstallPrompt方法被调用 ---- beforeinstallprompt", event);
    // 阻止默认的安装提示
    event.preventDefault();
    // 保存事件以便在用户点击按钮时调用
    setDeferredPromptEvent(event);
    console.log("用户可以安装 PWA---App", event);
  };

  const installPwa = useCallback(async () => {
    if (isIOS()) {

      if (isInAppBrowser()) {
        openInChrome()
      } else {
        dispatch(setIsIOSPwa(true));
      }

      return;
    }

    if (!deferredPromptEvent) {
      openInChrome();
      return;
    }


    const isInstalled = localStorage.getItem("pwa_install");
    if (isInstalled) {
      window.open("/", "_blank");
      return;
    }
    if (deferredPromptEvent && deferredPromptEvent.prompt) {
      deferredPromptEvent.prompt();
      const { outcome } = await deferredPromptEvent.userChoice;
      if (outcome === "accepted") {
        localStorage.setItem("pwa_install", "true");
        // installedPwaReq();
        pwaPoint("2");
      } else {
        pwaPoint("5");
        window.location.reload();
        console.log("User dismissed the install prompt");
      }
    } else {
      console.log("beforeinstallprompt event is not available.");
    }
  }, [deferredPromptEvent]);

  return (
    <DeferredContext.Provider
      value={{
        deferredPromptEvent,
        setDeferredPromptEvent,
        installPwa,
      }}
    >
      {children}
    </DeferredContext.Provider>
  );
};

export const useDeferred = () => useContext(DeferredContext);
export default DeferredProvider;
