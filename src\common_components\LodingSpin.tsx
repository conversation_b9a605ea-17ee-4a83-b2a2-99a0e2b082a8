import { infinity } from 'ldrs';
import { FC } from 'react';
infinity.register()

const LodingSpin: FC = () => {
  console.log('');
  
  return (
    <div className={`${'w-[100vw] h-[100vh] fixed z-[9999] top-0 left-0 bg-black flex justify-center items-center overflow-hidden'}`}>
      
      <l-infinity
        size={99}
        stroke="4"
        stroke-length="0.15"
        bg-opacity="0.1"
        speed="1.3"
        color={'var(--main-theme-color)'}
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)', // Adjusted to center the spinner
          // transform: 'translateX(-50%)'
          visibility: 'visible',
          zIndex: 9999, // Ensure the spinner is on top of other elements
        }}
      ></l-infinity>
    </div>
    
  )
}

export default LodingSpin
