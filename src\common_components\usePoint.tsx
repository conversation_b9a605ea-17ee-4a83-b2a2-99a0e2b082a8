import md5Util from "crypto-js/md5";
import { useCallback } from "react";
import { browserName, browserVersion } from "react-device-detect";
import { useStore } from "react-redux";

export function usePoint() {
  const BASE_URL = (import.meta as any).env.VITE_PLATFORM_URLS;
  const store = useStore();

  function md5(str: string) {
    return md5Util(str).toString();
  }

  const activityPoint = useCallback(
    async (activityType: string, activityUniqueId: string) => {
      const activitySource = store?.getState()?.popupReducer?.activitySource;
      console.log("调用活动埋点", activitySource, activityType, activityUniqueId);
      const personValue = store?.getState()?.systemReducer?.personInfo;
      const time = Date.now();
      const al = localStorage.getItem("al") || "";

      try {
        const response = await fetch(BASE_URL + "/gateway/promotion/activity", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            eventType: activitySource,
            sign: md5(time + "wgs_activity_check"),
            site: window.location.hostname,
            activityType: activityType,
            activityUniqueId: activityUniqueId,
            channelId: personValue?.channelId || al,
            time: time,
            playerId: personValue?.playerId || "",
            vipLevel: personValue?.vipLevel || 0
          })
        });

        if (response.ok) {
          const data = await response.json();
          if (data.code === 0) {
            console.log("活动埋点返回数据", data, data.code);
          }
          return data;
        } else {
          throw new Error("Network response was not ok");
        }
      } catch (error) {
        console.log("活动埋点失败", error);
      }
    },
    []
  );

  const pwaPoint = useCallback(async (type: string) => {
    console.log("调用pwa埋点", type);

    try {
      const response = await fetch(BASE_URL + "/gateway/promotion/pwa", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          eventType: type,
          sign: md5("wgs_pwa_check"),
          site: window.location.hostname,
          browserName: browserName + "-" + browserVersion
        })
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        throw new Error("Network response was not ok");
      }
    } catch (error) {
      console.log("pwa埋点失败", error);
    }
  }, []);


  function generateSignString(params: Record<string, any>): string {
    // 步骤1：收集所有非空参数
    const nonEmptyParams = Object.entries(params)
      .filter(([_, value]) => {
        // 过滤掉值为null、undefined、空字符串的参数
        return value !== null && value !== undefined && value !== '';
      });
  
    // 步骤2：根据参数名的ASCII码排序
    const sortedParams = nonEmptyParams.sort(([keyA], [keyB]) => {
      return keyA.localeCompare(keyB);
    });
  
    // 步骤3：使用URL键值对格式拼接字符串
    const stringA = sortedParams
      .map(([key, value]) => {
        // 处理对象中的双引号，将其转换为&quot;
        let processedValue = value;
        if (typeof value === 'string') {
          processedValue = value.replace(/"/g, '&quot;');
        } else if (typeof value === 'object' && value !== null) {
          processedValue = JSON.stringify(value).replace(/"/g, '&quot;');
        }
        return `${key}=${processedValue}`;
      })
      .join('&');
    
    console.log("md5前数据", stringA + "&key=wgs_deposit_key");
    
    const sign = md5(stringA + "&key=wgs_deposit_key").toUpperCase();
    return sign;
  }

  const doDeposit = useCallback(async (data: {}) => {
    console.log("调用后台充值", data);

      const params = {
      agentId: data?.agentId,
      orderId: String(data?.orderInfo?.orderId),
      totalBetTimes: data?.totalBetTimes,
      business_no: data?.orderInfo?.businessNo,
      channel: data.channel,
      paymentSource: data.paymentSource,
      payEndTime: data?.orderInfo?.payEndTime,
      type: data?.orderInfo?.type,
      mediaId: data?.mediaId,
      rechargeChannel: data.rechargeChannel,
      number: data.number,
      site: data.site,
      rechargeAccount: data.rechargeAccount,
      amounts: data?.orderInfo?.amounts,
      createTime: data?.orderInfo?.createTime,
      paymentMethod: data?.paymentMethod,
      currencyId: data?.orderInfo?.currencyId,
      region: data?.region,
      channelId: data?.channelId,
      playerId: data?.orderInfo?.playerId,
      status: data?.orderInfo?.status,
      playerName: data?.playerName,
      adId: data?.adId
    }
    
    params.signature = generateSignString(params)

    try {
      const response = await fetch(BASE_URL + "/gateway/deposit/pay", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(params)
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        throw new Error("Network response was not ok");
      }
    } catch (error) {
      console.log("pwa埋点失败", error);
    }
  }, []);

  //eventType 1-打开充值页 2-关闭充值页 3-打开二维码 4-关闭二维码 5-充值成功的
  //pageType 1-充值页  2-弹窗 3-直充
  const rechargePoint = useCallback(async (type: number,source: number, amount = 0, orderId = "") => {
    console.log("调用pwa埋点", type);
    const personValue = store?.getState()?.systemReducer?.personInfo;
    const al = localStorage.getItem("al") || "";

    try {
      const time = Date.now();
      const response = await fetch(BASE_URL + "/gateway/promotion/recharge", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          eventType: type,
          sign: md5(time + "wgs_recharge_check"),
          site: window.location.hostname,
          channelId: personValue?.channelId || al,
          browserName: browserName + "-" + browserVersion,
          time: time,
          playerId : personValue?.playerId || "",
          amount: amount || 0,
          orderId: orderId,
          pageType: source || 0
        })
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        throw new Error("Network response was not ok");
      }
    } catch (error) {
      console.log("pwa埋点失败", error);
    }
  }, []);


  const errorMessagePoint = useCallback(async (message: string, pathName:string, hostname:string) => {
    const personValue = store?.getState()?.systemReducer?.personInfo;
    try {
      const response = await fetch(BASE_URL + "/gateway/web/info", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          errorMessage: message,
          time: Date.now(),
          playerId: personValue?.playerId || '',
          browserName: browserName + "-" + browserVersion,
          pathName: pathName,
          hostname: hostname
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.code === 0) {
          console.log("pwa埋点返回数据", data, data.code);
        }
        return data;
      } else {
        throw new Error("Network response was not ok");
      }
    } catch (error) {
      console.log("pwa埋点失败", error);
    }
  }, [BASE_URL]);

  const interFaceErrorPoint = useCallback(async (type: string, msg: string) => {
    const personValue = store?.getState()?.systemReducer?.personInfo;
    console.log("接口错误埋点", personValue, type, msg);
      const time = Date.now();
      try {
        const response = await fetch(BASE_URL + "/gateway/promotion/errorMsg", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            type: type,
            sign: md5(time + "wgs_error_msg_check"),
            site: window.location.hostname,
            browserName: browserName + "-" + browserVersion,
            time: time,
            playerId: personValue?.playerId || "",
            msg: msg
          })
        });

        if (response.ok) {
          const data = await response.json();
          if (data.code === 0) {
            console.log("接口错误埋点", data, data.code);
          }
          return data;
        } else {
          throw new Error("Network response was not ok");
        }
      } catch (error) {
        console.log("接口错误埋点", error);
      }
  }, [BASE_URL]);

  return { activityPoint, pwaPoint, doDeposit, rechargePoint, errorMessagePoint , interFaceErrorPoint};
}
