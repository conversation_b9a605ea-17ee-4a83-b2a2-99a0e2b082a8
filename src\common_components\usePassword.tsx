import { useMemo } from "react";
import { FormattedMessage, useIntl } from "react-intl";

const usePassword = (pwdValue: string) => {
    const type = 2
    const intl = useIntl()

    const authInfo = useMemo(() => {
        let popoverContent = null
        let errorText = null
        // let pattern = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]+$/
        let pattern = /^[a-zA-Z\d]{8,16}$/;

        let haveUpper = false 
        let haveDigit = false 
        let isLengthVilidate = false
        let noSpecialCharacters = true

        if (!pwdValue) {
            haveUpper = false
            haveDigit = false
            isLengthVilidate = false
        }
        if (pwdValue && pwdValue.length > 0) {
            haveUpper = type === 3 ? /^(?=.*[a-z])(?=.*[A-Z])/.test(pwdValue) : /^(?=.*[a-z])/.test(pwdValue)
            haveDigit = /^(?=.*[A-Za-z])(?=.*\d)/.test(pwdValue)
            isLengthVilidate = pwdValue.length >= 8 && pwdValue.length <= 16
            noSpecialCharacters = !/\s/.test(pwdValue)

            // if (!haveUpper && type === 3 ) {
            //     errorText = intl.formatMessage({id: "content_upper_character"})
            // }  
            // if (!haveDigit) {
            //     errorText = intl.formatMessage({id: "content_digit"})
            // }
            if (!isLengthVilidate) {
                errorText = intl.formatMessage({id: "content_minmum"})
            }
            if (!noSpecialCharacters) {
                errorText = intl.formatMessage({id: "no_special_characters"})
            }
        }

        if (type === 1) {
            popoverContent = (
                <div className="flex flex-col justify-start items-start lg:w-full max-w-[60vw] h-full ">
                    <div className={`${isLengthVilidate ? 'text-[#06B666]' : 'text-[#98B1D8]'}`}>
                        {isLengthVilidate ? '✓' : '×'} <FormattedMessage id="content_minmum" />
                    </div>
                    <div className={`${haveDigit ? 'text-[#06B666]' : 'text-[#98B1D8]'}`}>
                        {haveDigit ? '✓' : '×'} <FormattedMessage id="content_digit" />
                    </div>
                </div>
            )
            pattern = /^\d+$/
        }
        if (type === 2) {
            
            popoverContent = (
                <div className="flex flex-col justify-start items-start lg:w-full max-w-[60vw] h-full ">
                    <div className={`${isLengthVilidate ? 'text-[#06B666]' : 'text-[#98B1D8]'}`}>
                        {isLengthVilidate ? '✓' : '×'} <FormattedMessage id="content_minmum" />
                    </div>
                    {/* <div className={`${haveUpper ? 'text-[#06B666]' : 'text-[#98B1D8]'}`}>
                        {haveUpper ? '✓' : '×'} <FormattedMessage id="contains_character" />
                    </div> */}
                    {/* <div className={`${haveDigit ? 'text-[#06B666]' : 'text-[#98B1D8]'}`}>
                        {haveDigit ? '✓' : '×'} <FormattedMessage id="content_digit" />
                    </div> */}
                    <div className={`${noSpecialCharacters ? 'text-[#06B666]' : 'text-[#98B1D8]'}`}>
                        {noSpecialCharacters ? '✓' : '×'} <FormattedMessage id="no_special_characters" />
                    </div>
                </div>
            )
        }
        if (type === 3) {
            popoverContent = (
                <div className="flex flex-col justify-start items-start lg:w-full max-w-[60vw] h-full ">
                    <div className={`${isLengthVilidate ? 'text-[#06B666]' : 'text-[#98B1D8]'}`}>
                        {isLengthVilidate ? '✓' : '×'} <FormattedMessage id="content_minmum" />
                    </div>
                    <div className={`${haveUpper ? 'text-[#06B666]' : 'text-[#98B1D8]'}`}>
                        {haveUpper ? '✓' : '×'} <FormattedMessage id="content_upper_character" />
                    </div>
                    <div className={`${haveDigit ? 'text-[#06B666]' : 'text-[#98B1D8]'}`}>
                        {haveDigit ? '✓' : '×'} <FormattedMessage id="content_digit" />
                    </div>
                </div>
            )
            pattern = /(?=(.*[a-z]))(?=(.*[A-Z]))(?=(.*\d))[a-zA-Z\d]/
        }

        return {pattern, popoverContent , errorText}
    }, [pwdValue])

    const {pattern, popoverContent, errorText} = authInfo

    return { pattern, popoverContent , errorText}

}

export default usePassword;