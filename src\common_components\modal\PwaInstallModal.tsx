import bannerIcon from "@/assets/pwaInstallModal/banner.webp";
import { setShowLiveSupport } from "@/store";
import { Popup } from "antd-mobile";
import { memo, useEffect, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useDispatch, useSelector } from "react-redux";
import { useDeferred } from "../context/useDeferredContext";
import kfIcon from "@/assets/pwaInstallModal/kf.webp";
import { TemplateType } from "@/utils/enums";


const PwaInstallModal: React.FC<{ handleCancel: () => void; showModal: boolean }> = memo(
  ({ showModal, handleCancel }) => {
    const websitId = useSelector((state: any) => state.systemReducer.websitId);
    const handleClose = () => {};
    const [siteName, setSiteName] = useState('')
    const dispatch = useDispatch();
    const handleShowLive = () => {
      dispatch(setShowLiveSupport());
    };
    const { installPwa } = useDeferred();

    useEffect(() => {
      if (websitId === 3) {
        setSiteName('Wingame')
      } else if (websitId === 4){
        setSiteName('Luck7')
      } else if (websitId === 5) {
        setSiteName('Luck365')
      } 
    }, [websitId])
    
    return (
      <>
        <Popup
          visible={showModal}
          onMaskClick={handleCancel}
          onClose={handleClose}
          mask={true}
          bodyStyle={{
            height: "72vh",
            borderColor: "rgba(68, 66, 73, 1)",
            borderStyle: "solid",
            borderWidth: "1px",
            borderRadius: "16px 16px 0px 0px",
            background:
              "linear-gradient(180deg, rgba(37, 36, 43, 0.96) 8.18%, rgba(19, 18, 23, 0.96) 100%)",
            backdropFilter: "blur(2.5px)"
          }}
        >
          <div className="bg-[#2B3147] flex flex-col text-primary-text h-full relative">
            <div
              onClick={handleCancel}
              className=" flex items-center justify-center absolute top-[9px] right-[12px] w-[24px] h-[24px] bg-white/5 backdrop-blur-[1.5px] rounded-lg"
            >
              <iconpark-icon name="close" color="white" size="26px"></iconpark-icon>
            </div>
            <img src={bannerIcon} alt="" style={{ borderRadius: "16px 16px 0px 0px" }} />
            <div className="flex flex-col text-[18px]/5 items-center -translate-y-8 px-[25px] pb-[20px]">
              <span className="font-semibold">
                <FormattedMessage id="pwa_install_desc_1" />
              </span>
              <div className="mt-[10px] text-sm text-center">
                <FormattedMessage
                  id="pwa_install_desc_2"
                  values={{
                    siteName: (
                      <span className="text-[#39E744] text-sm font-semibold">
                        {siteName}
                      </span>
                    )
                  }}
                />
              </div>
              <div className="mt-[13px] w-full rounded-lg bg-[#20263C] px-[9px] h-[50px] items-center flex gap-x-[14px]">
                {(websitId == 4 || websitId == 7) ? (
                  <img src="/luck7/image64.png" alt="" className="w-[40px]" />
                ) : (websitId == TemplateType.brazilId) ? (
                  <img src="/image692.png" alt="" className="w-[35px]" />
                ) : (
                  <img src="/image64.png" alt="" className="w-[40px]" />
                )}
                <span className="text-sm font-semibold">{window.location.hostname}</span>
              </div>
              <div className="mt-[18px] w-full rounded-lg items-center flex justify-between">
                  <img src={kfIcon} alt="" onClick={handleShowLive} className="h-[32px] w-[32px]"/>
                <button className="mobile_btn !w-[224px] !h-[36px] !font-semibold text-[--account-div-text-color]" onClick={installPwa}>
                  <FormattedMessage id="refresh" />
                </button>
              </div>
              <div className="mt-[13px] flex flex-col text-[#78828A] font-medium text-sm w-full" style={{fontFamily: 'Inter'}}>
                <span className="font-semibold">
                  <FormattedMessage id="pwa_install_desc_3" />
                </span>
                <div className="flex flex-col mt-[14px]">
                  <span>
                    <FormattedMessage id="pwa_install_desc_4" />
                  </span>
                  <span>
                    <FormattedMessage id="pwa_install_desc_5" />
                  </span>
                  <span>
                    <FormattedMessage id="pwa_install_desc_6" />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </Popup>
      </>
    );
  }
);

export default PwaInstallModal;
