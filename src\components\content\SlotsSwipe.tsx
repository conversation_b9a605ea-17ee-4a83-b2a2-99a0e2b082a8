import { ProtoMessage } from "@/protos/common";
import { Image, Skeleton } from "antd";
import React, { useCallback, useEffect, useMemo, useRef, useState, useLayoutEffect } from "react";
import { FormattedMessage } from "react-intl";
import { useLocation } from "react-router-dom";
import "swiper/css"; // 引入 Swiper 的 CSS 文件
import "swiper/css/autoplay"; // 引入 autoplay 的 CSS
import "swiper/css/grid"; // 引入 autoplay 的 CSS
import { Grid } from "swiper/modules"; // 引入 autoplay 模块
import { Swiper, SwiperSlide } from "swiper/react";
import { useScreenWidth } from "../../utils/MobileDetectionContext";
import { useUtils } from "../useUtils";
import SwipeTopControl from "./SwipeTopControl";
import { useGame } from "@/hooks/game/useGame";
import { GameChannel } from '@/utils/enums';

interface SlotsSwipeProps {
  infinite?: boolean;
  sctionId?: string;
  datas: ProtoMessage.GameApiInfo[];
  title?: string;
  showText?: boolean;
  hoveOp?: boolean;
  bannerImg?: string;
  showAllGames?: boolean;
  loading?: boolean;
  autoPlay?: boolean;
  viewAll?: boolean;
  gameType?: number;
  source?: boolean;
  useControl?: boolean;
  useSlider?: boolean;
  backFun?: () => void;
  rowCount?: number;
}

const GameCard = React.memo(
  ({
    item,
    providerHeight,
    providerWidth,
    showText,
    hoveOp,
    isLogin,
    gameType,
    screenWidth,
    handleEnterGame,
    handleOpt,
    formatNumberQfw,
    modeal
  }) => {
    const [isHover, setIsHover] = useState(false);

    return (
      <div
        className={`${modeal == 1 ? "mt-[12px]" : "mt-[8px]"}`}
        style={{
          height: `${providerHeight}px`,
          width: `${providerWidth}px`
        }}
      >
        <div className="flex relative flex-col items-center">
          <div
            onClick={() => handleEnterGame(item)}
            className={`relative group flex ${
              screenWidth <= 1024
                ? ""
                : showText && "hover:-translate-y-2 transition-all duration-300"
            } cursor-pointer`}
            style={{
              height: `${providerHeight}px`,
              width: `${providerWidth}px`
            }}
            onMouseEnter={() => setIsHover(true)}
            onMouseLeave={() => setIsHover(false)}
          >
            <Image
              placeholder={
                <Skeleton.Image
                  active
                  className="bg-gradient-to-r from-[#1e2023] to-[#0a0b0c]"
                  style={{
                    width: `${providerWidth}px`,
                    height: `${providerHeight}px`
                  }}
                />
              }
              alt={item.platformGameId || "Game image"}
              preview={false}
              loading="lazy"
              decoding="async"
              src={item.fileUrl}
              style={{ scale: {} }}
              width={`${providerWidth}px`}
              height={`${providerHeight}px`}
              className={`rounded-lg ${!showText && "bg-[--game-provider-bg-color]"} ${
                screenWidth <= 1024 ? "" : hoveOp && "group-hover:opacity-40"
              }`}
            />
            {screenWidth > 1024 && showText && (
              <div
                className={`${isHover ? "block" : "hidden"} 
                          absolute w-[94%] h-full select-none justify-center flex
                          flex-col items-center text-[#FFFFFF] gap-y-5`}
              >
                <div className="flex flex-col items-center">
                  <FormattedMessage id="EFFECTIVE RTP" />
                  <div className="text-xs">
                    {formatNumberQfw(Number(item.rtp || 0) * 100, 2, 2)}%
                  </div>
                </div>
                {isLogin && (
                  <div onClick={(event) => handleOpt(1, item.gameId, event, item.favorites)}>
                    {item.favorites ? (
                      <svg
                        width="30"
                        height="30"
                        viewBox="0 0 30 30"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M15 27C16.3 27 28 20.3024 28 10.9257C28 3.67562 19.1112 -0.1178 15 6.23654C10.8817 -0.128695 2 3.66906 2 10.9257C2 20.3024 13.7 27 15 27Z"
                          fill={"var(--main-theme-color)"}
                        />
                      </svg>
                    ) : (
                      <svg
                        width="30"
                        height="30"
                        viewBox="0 0 30 30"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M15 27C16.3 27 28 20.3024 28 10.9257C28 3.67562 19.1112 -0.1178 15 6.23654C10.8817 -0.128695 2 3.66906 2 10.9257C2 20.3024 13.7 27 15 27Z"
                          stroke={"var(--main-theme-color)"}
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    )}
                  </div>
                )}
              </div>
            )}
            {screenWidth <= 1024 && isLogin && gameType != GameChannel.Casino_GameProviders && (
              <div
                onClick={(event) => handleOpt(1, item.gameId, event, item.favorites)}
                className="flex-shrink-0 backdrop-blur-[4px] bg-[#00000066] absolute top-0 left-0 w-[26px] h-[16px] rounded-br-lg rounded-tl-lg flex items-center justify-center"
              >
                <div>
                  <svg
                    width="13"
                    height="12"
                    viewBox="0 0 30 30"
                    fill={item.favorites ? "var(--game-collection-bg-color)" : "none"}
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M15 27C16.3 27 28 20.3024 28 10.9257C28 3.67562 19.1112 -0.1178 15 6.23654C10.8817 -0.128695 2 3.66906 2 10.9257C2 20.3024 13.7 27 15 27Z"
                      stroke={item.favorites ? "var(--game-collection-bg-color)" : "white"}
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
);

const SlotsSwipe: React.FC<SlotsSwipeProps> = React.memo(
  ({
    rowCount,
    gameType = -1,
    source,
    useControl = true,
    useSlider = true,
    backFun,
    infinite = false,
    sctionId,
    datas,
    title,
    showText = true,
    hoveOp = true,
    bannerImg,
    showAllGames = true,
    loading = false,
    autoPlay = false,
    viewAll = true,
    modal = 1,
    total
  }) => {
    const swiperRef = useRef(null);
    const { screenWidth } = useScreenWidth();
    const { formatNumberQfw, isLogin } = useUtils();
    const location = useLocation();
    const [providerHeight, setProviderHeight] = useState(screenWidth <= 1024 ? 145 : 200);
    const [providerWidth, setProviderWidth] = useState(screenWidth <= 1024 ? 110 : 150);
    const [isFirstPage, setIsFirstPage] = useState(true);
    const [isLastPage, setIsLastPage] = useState(false);
    const [currentDatas, setCurrentDatas] = useState<ProtoMessage.GameApiInfo[]>([]);
    const divRef = useRef<HTMLDivElement>(null);
    const { handleEnterGame, handleOpt } = useGame(sctionId, gameType);
    const resizeObserverRef = useRef<ResizeObserver | null>(null);

    // 计算尺寸的函数
    const calculateDimensions = useCallback(() => {
      if (screenWidth <= 1024) {
        if (modal == 1) {
          setProviderHeight(145);
          setProviderWidth(110);
        } else {
          setProviderHeight(113);
          setProviderWidth(85);
        }

        if (gameType == GameChannel.Casino_GameProviders) {
          setProviderHeight(29);
          setProviderWidth(75);
        }
      } else {
        setProviderHeight(200);
        setProviderWidth(150);
        if (gameType == GameChannel.Casino_GameProviders) {
          setProviderHeight(58);
        }

        if (!source && location.pathname.includes("gameList")) {
          if (gameType == GameChannel.Casino_GameProviders) {
            setProviderHeight(88);
            setProviderWidth(184);
          }
        }
      }
    }, [screenWidth, source, gameType, location.pathname, modal, GameChannel]);

    // 监听数据变化
    useEffect(() => {
      setCurrentDatas(datas || []);
    }, [datas]);

    // 监听容器尺寸变化
    useLayoutEffect(() => {
      if (!divRef.current) return;

      calculateDimensions();

      const handleResize = () => {
        calculateDimensions();
      };

      // 创建 ResizeObserver
      if (!resizeObserverRef.current) {
        resizeObserverRef.current = new ResizeObserver(handleResize);
        resizeObserverRef.current.observe(divRef.current);
      }

      // 组件卸载时清理
      return () => {
        if (resizeObserverRef.current) {
          resizeObserverRef.current.disconnect();
          resizeObserverRef.current = null;
        }
      };
    }, [calculateDimensions]);

    // 监听屏幕宽度变化
    useEffect(() => {
      calculateDimensions();
    }, [screenWidth, calculateDimensions]);

    // 监听数据加载完成
    useEffect(() => {
      if (currentDatas.length > 0) {
        calculateDimensions();
      }
    }, [currentDatas.length, calculateDimensions]);

    const processedData = useMemo(() => {
      if (!datas || datas.length === 0) return [];

      if (
        screenWidth <= 1024 &&
        !location.pathname.includes("gameList") &&
        gameType != GameChannel.Casino_GameProviders
      ) {
        const newDatas = [];
        const newDatas1 = [];
        const newDatas2 = [];
        const itemsPerRow = screenWidth > 1024 ? 7 : 3;

        datas.forEach((item, index) => {
          if (index % 2 === 0) {
            newDatas1.push(item);
          } else {
            newDatas2.push(item);
          }

          if (newDatas1.length === itemsPerRow) {
            newDatas.push(...newDatas1);
            newDatas1.length = 0;
          }

          if (newDatas2.length === itemsPerRow) {
            newDatas.push(...newDatas2);
            newDatas2.length = 0;
          }
        });

        if (newDatas1.length > 0) {
          newDatas.push(...newDatas1);
        }

        if (newDatas2.length > 0) {
          newDatas.push(...newDatas2);
        }

        return newDatas;
      }

      return datas;
    }, [datas, screenWidth, location.pathname, gameType]);

    useEffect(() => {
      setCurrentDatas(processedData);
    }, [processedData]);

    const next = useCallback(() => {
      if (!swiperRef.current) return;

      swiperRef.current?.slideNext();
      setIsLastPage(swiperRef.current?.isEnd);
      setIsFirstPage(swiperRef.current?.isBeginning);
    }, []);

    const prev = useCallback(() => {
      if (!swiperRef.current) return;

      swiperRef.current?.slidePrev();
      setIsFirstPage(swiperRef.current?.isBeginning);
      setIsLastPage(swiperRef.current?.isEnd);
    }, []);

    const handleGame = useCallback((item: ProtoMessage.GameApiInfo) => {
      handleEnterGame(item)
      if (backFun) {
        backFun()
      }
    }, [backFun])

    const slides = useMemo(() => {
      if (!currentDatas || currentDatas.length === 0) {
        return null;
      }

      return currentDatas.map((item, index) => (
        <SwiperSlide
          key={`${item.platformGameId || ""}-${item.gameId}-${index}`}
          style={{ width: "auto" }}
        >
          <GameCard
            item={item}
            providerHeight={providerHeight}
            providerWidth={providerWidth}
            showText={showText}
            hoveOp={hoveOp}
            isLogin={isLogin}
            gameType={gameType}
            screenWidth={screenWidth}
            handleEnterGame={handleGame}
            handleOpt={handleOpt}
            formatNumberQfw={formatNumberQfw}
            modal={modal}
          />
        </SwiperSlide>
      ));
    }, [
      currentDatas,
      providerHeight,
      providerWidth,
      showText,
      hoveOp,
      isLogin,
      gameType,
      screenWidth,
      handleEnterGame,
      handleOpt,
      formatNumberQfw,
    ]);

    return (
      <>
        {useControl && currentDatas?.length > 0 && (
          <SwipeTopControl
            isFirstPage={isFirstPage}
            isLastPage={isLastPage}
            viewAll={viewAll}
            sctionId={sctionId}
            IconBanner={bannerImg}
            prev={prev}
            next={next}
            text={title}
            showAllGames={showAllGames}
            modal={modal}
            total={total}
          />
        )}

        {useSlider && currentDatas && currentDatas.length > 0 && (
          <div className="w-full" ref={divRef}>
            <Swiper
              spaceBetween={modal == 1 ? 10 : 6}
              slidesPerView={modal == 2 ? 4 : "auto"}
              slidesPerGroup={modal == 2 ? 4 : screenWidth > 1024 ? 7 : 3}
              direction="horizontal"
              loop={false}
              grid={{
                rows:
                  modal == 2
                    ? 2
                    : screenWidth > 1024 || gameType == GameChannel.Casino_GameProviders
                    ? 1
                    : 2,
                fill: "row"
              }}
              modules={[Grid]}
              onSwiper={(swiper) => (swiperRef.current = swiper)}
            >
              {slides}
            </Swiper>
          </div>
        )}

        {!useSlider && slides}
      </>
    );
  }
);

export default SlotsSwipe;
