import copyIcon from "@/assets/copy.svg";
import googleAuth_1Icon from "@/assets/googleAuth/googleAuth_1.svg";
import googleAuth_2Icon from "@/assets/googleAuth/googleAuth_2.svg";
import CommonModal from "@/components/CommonModal";
import HintContent from "@/components/hint/HintContent";
import ScreenSpin from "@/components/ScreenSpin2";
import { useUtils } from "@/components/useUtils";
import { HallMessage } from "@/protos/Hall";
import { enable2FAData, get2FAVerificationCode } from "@/service/hall";
import { useScreenWidth } from "@/utils/MobileDetectionContext";
import { Button, Input } from "antd";
import { useEffect, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useJump } from "../context/useJumpContext";
import { usePersonInfoUtils } from "../usePersonInfoUtils";
import copy from "copy-to-clipboard";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import EventBus from "@/utils/EventBus";
import { ModalType } from "@/utils/enums";

const TwoStepVerifi: React.FC = ({ showModal, handleCancel }) => {
  if (!showModal) return null;
  const [values, setValues] = useState(["", "", "", "", "", ""]);
  const { screenWidth } = useScreenWidth()  ;
  const [twofaData, setTwofaData] = useState<any>({});
  const { hanldeMask } = useUtils();
  const { intl, Toast, checkResponse } = useToastUtils();
  const { updateInfo } = usePersonInfoUtils();
  const [qrLoading, setQrLoading] = useState(true);
  const [verificationCode, setVerificationCode] = useState();
  const { show2FA_showGoogleTip, setShow2FA_showGoogleTip, showHint } = useJump() || {};

  function handleClose() {
    setValues(["", "", "", "", "", ""]);
    setShow2FA_showGoogleTip(false)
    console.log('关闭twostep');
    
  }

  useEffect(() => {
    const fetchData = async () => {
      const res = (await enable2FAData({
        msgID: 400033
      })) as HallMessage.ResEnable2FADataMessage;
      console.log("获取到的2步验证数据为：", res);
      if (checkResponse(res)) {
        setTwofaData(res);
      }
    };
    if (showModal) {
      fetchData();
    }
  }, [showModal]);

  const input0Ref = useRef();
  const input1Ref = useRef();
  const input2Ref = useRef();
  const input3Ref = useRef();
  const input4Ref = useRef();
  const input5Ref = useRef();
  const captchaRefs = useRef([input0Ref, input1Ref, input2Ref, input3Ref, input4Ref, input5Ref]);
  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace") {
      console.log("退格键按下", index);
      // 如果按下了退格键且不是第一个输入框，则聚焦到前一个输入框
      e.preventDefault();
      const newValues = [...values];
      newValues[index] = "";
      console.log("newValues", newValues);

      setValues(newValues);
      if (index > 0) {
        captchaRefs.current[index - 1].current.focus();
      }
    }
  };

  function handlerClick(e, index) {
    console.log(e.target.value, index);
    const newValues = [...values];
    newValues[index] = e.target.value;
    setValues(newValues);
    if (index < 5) {
      captchaRefs.current[index + 1].current.focus();
    }
  }

  async function handlerEnable() {
    if (!verificationCode || verificationCode.length < 6) {
      Toast(intl.formatMessage({ id: "please_input___code" }), "error");
      return;
    }
    try {
      hanldeMask(true);
      const res = await get2FAVerificationCode({
        msgID: 400035,
        verificationCode: verificationCode
      });
      console.log("返回2FA Verification Code数据为：", res);
      if (checkResponse(res)) {
        Toast(intl.formatMessage({ id: "success" }));
        updateInfo({enable2FA: true})
        if (show2FA_showGoogleTip) {
          EventBus.emit("showModal", ModalType.showGoogleAuthCodeModal);
        }
        handleCancel();
      }
    } finally {
      hanldeMask(false);
    }
  }

  const loadQr = () => {
    setQrLoading(false);
  };

  const handleCodeValue = (value: string) => {
    console.log("得到输入", value);

    if (/^\d*$/.test(value)) {
      setVerificationCode(value); // 更新状态
    }
  };

  const handleCopy = async (text: string) => {
    copy(twofaData?.secretKey);
    Toast(intl.formatMessage({id:"copy_successfully"}));
  };

  const handleMyCancel = () => {
    setShow2FA_showGoogleTip(false)
    handleCancel();
  }

  return (
    <>
      <CommonModal
        width={screenWidth > 1024 ? "30%" : "100%"}
        footer={null}
        open={showModal}
        onCancel={handleMyCancel}
        afterClose={handleClose}
        maskClosable={false}
        
        title={
          <div className="bg-[--header-bg-color-basic] p-4 rounded-tl-2xl rounded-tr-2xl text-lg/6 font-medium">
            <span className="text-white">
              <FormattedMessage id="google_authenticator" />
            </span>
          </div>
        }
      >
        <div className="flex flex-col w-full px-5 pb-5 min-h-550px overflow-y-auto select-none gap-y-1 relative">
          {showHint && <HintContent popupId={showHint} />}
          <div className="flex flex-col py-4 gap-y-[8px]">
            <div className="w-full flex lg:flex-row flex-col items-center lg:gap-x-8 gap-y-[8px] ">
              <div className="bg-[white] rounded-md flex relative items-center justify-center  p-2">
                <ScreenSpin loading={qrLoading} />
                <img
                  src={twofaData?.qrCode}
                  className="w-[200px] h-[200px]"
                  onLoad={() => loadQr()}
                />
              </div>
              <div className="flex flex-col gap-y-[8px] lg:pr-10">
                <span className="text-[#78828A] text-sm">
                  <FormattedMessage id="google_authenticator_desc" />
                </span>
                <span className="text-[#40E74B] text-pxs font-medium">
                  <FormattedMessage id="install_google_authenticator" />
                </span>
                <div className="flex items-center gap-x-[14px]">
                  <Button
                    onClick={() =>
                      window.open(
                        "https://apps.apple.com/us/app/google-authenticator/id388497605",
                        "_blank"
                      )
                    }
                    className="flex items-center gap-x-1 btn  !text-pxs !font-medium flex-1"
                  >
                    <img src={googleAuth_1Icon} alt="" />
                    <span>IOS</span>
                  </Button>
                  <Button
                    onClick={() =>
                      window.open(
                        "https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2",
                        "_blank"
                      )
                    }
                    className="flex items-center gap-x-1 btn  !text-pxs !font-medium flex-1"
                  >
                    <img src={googleAuth_2Icon} alt="" />
                    <span>Google Play</span>
                  </Button>
                </div>
              </div>
            </div>

            <div
              className=" self-start flex  text-sm "
              style={{ color: "rgba(255, 255, 255, 0.80)" }}
            >
              <span>
                <FormattedMessage id="secret_key" />
              </span>
            </div>
            <div className=" w-full ">
              <Input
                readOnly
                value={twofaData?.secretKey}
                className="h-[40px]"
                suffix={
                  <img
                    onClick={() => handleCopy(twofaData?.secretKey)}
                    src={copyIcon}
                    alt="secretKeyIcon"
                    className="w-5 h-5 cursor-pointer text-sm"
                  />
                }
              />
            </div>
            <div
              className=" self-start flex text-[11px]/[14px]"
              style={{ color: "rgba(255, 255, 255, 0.80)" }}
            >
              <FormattedMessage id="google_auth_code_input" />
            </div>
            <div>
              <span
                className="text-[--wallet-title-text-color] text-sm"
                style={{ color: "rgba(255, 255, 255, 0.80)" }}
              >
                <FormattedMessage id="verify_code" />
              </span>
            </div>
            <div className=" w-full ">
              <Input
                value={verificationCode}
                placeholder={intl.formatMessage({ id: "enter_the_6-digit_code" })}
                className="h-[40px]"
                onChange={(e) => handleCodeValue(e.target.value)}
                maxLength={6}
              />
            </div>
          </div>
          {/* <div className="grid grid-cols-6 gap-2 mt-4">
                        <input value={values[0]} onChange={(e) => { handlerClick(e, 0) }} onKeyDown={(e) => handleKeyDown(e, 0)} maxLength={1} ref={input0Ref} className="bg-black w-[56px] h-[56px] text-white rounded-md justify-center items-center flex text-center ">
                        </input>
                        <input value={values[1]} onChange={(e) => { handlerClick(e, 1) }} onKeyDown={(e) => handleKeyDown(e, 1)} maxLength={1} ref={input1Ref} className="bg-black w-[56px] h-[56px] text-white rounded-md justify-center items-center flex text-center ">
                        </input>
                        <input value={values[2]} onChange={(e) => { handlerClick(e, 2) }} onKeyDown={(e) => handleKeyDown(e, 2)} maxLength={1} ref={input2Ref} className="bg-black w-[56px] h-[56px] text-white rounded-md justify-center items-center flex text-center ">
                        </input>
                        <input value={values[3]} onChange={(e) => { handlerClick(e, 3) }} onKeyDown={(e) => handleKeyDown(e, 3)} maxLength={1} ref={input3Ref} className="bg-black w-[56px] h-[56px] text-white rounded-md justify-center items-center flex text-center ">
                        </input>
                        <input value={values[4]} onChange={(e) => { handlerClick(e, 4) }} onKeyDown={(e) => handleKeyDown(e, 4)} maxLength={1} ref={input4Ref} className="bg-black w-[56px] h-[56px] text-white rounded-md justify-center items-center flex text-center ">
                        </input>
                        <input value={values[5]} onChange={(e) => { handlerClick(e, 5) }} onKeyDown={(e) => handleKeyDown(e, 5)} maxLength={1} ref={input5Ref} className="bg-black w-[56px] h-[56px] text-white rounded-md justify-center items-center flex text-center ">
                        </input>
                    </div> */}
          <div className="flex flex-col">
            <Button
               disabled={!verificationCode}
              onClick={handlerEnable}
              className={`!text-base !font-semibold custom_button_shadow btn w-full !h-[54px] ${verificationCode ? '' : ' opacity-50 '}`}
            >
              <FormattedMessage id="submit" />
            </Button>
          </div>
          {show2FA_showGoogleTip && (
            <div
              onClick={() => {
                handleCancel();
                setShow2FA_showGoogleTip(false);
                EventBus.emit("showModal", ModalType.showOtpModal);
              }}
              className=" cursor-pointer w-full items-center justify-end text-[#3EE749] text-end text-sm font-medium mt-[12px]"
            >
              <span>
                <FormattedMessage id="otp_withdraw" />
              </span>
            </div>
          )}
        </div>
      </CommonModal>
    </>
  );
};

export default TwoStepVerifi;
