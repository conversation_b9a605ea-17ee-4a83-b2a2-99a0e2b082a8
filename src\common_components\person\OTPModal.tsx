import CommonModal from "@/components/CommonModal";
import { useConfigData } from "@/components/games/providers/ProjectConfigProvider";
import PhoneCountryArea from "@/components/phoneInput/PhoneCountryArea";
import { useUtils } from "@/components/useUtils";
import useSendEmailCode from "@/hooks/useSendEmailCode";
import useSendPhoneCode from "@/hooks/useSendPhoneCode";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { ProtoMessage } from "@/protos/common";
import { HallMessage } from "@/protos/Hall";
import { bindAccount, verifyAccount } from "@/service/hall";
import { verifyCode } from "@/service/user";
import { setIschangeUserInfo } from "@/store";
import { FuntionOpenMapping, ModalType, ThirdParty } from '@/utils/enums';
import EventBus from "@/utils/EventBus";
import { useScreenWidth } from "@/utils/MobileDetectionContext";
import { Button, Input, Radio } from "antd";
import { useEffect, useState } from "react";
import { FormattedMessage } from "react-intl";
import { CountryData } from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { useDispatch } from "react-redux";
import { useJump } from "../context/useJumpContext";
import { usePerson } from "../context/usePersonInfoContext";
import { usePersonInfoUtils } from "../usePersonInfoUtils";

const OTPModal: React.FC<{
  showModal: boolean;
  handleCancel: () => void;
}> = ({ showModal, handleCancel }) => {
  if (!showModal) return null;
  const { screenWidth } = useScreenWidth();
  const { hanldeMask, isSingleArea } = useUtils();
  const { intl, Toast, checkResponse } = useToastUtils();
  const { initRegion } = usePerson() || {};
  const [verifyCodeValue, setVerifyCodeValue] = useState("");
  const placeHolder = intl.formatMessage({ id: "please_input___code" });
  const { functionSwitchIds } = useConfigData() as { functionSwitchIds: number[] };
  const { sendEmailCode, sendCode: emailSendCode, countdown: emailCountdown } = useSendEmailCode();
  const { sendPhoneCode, sendCode: phoneSendCode, countdown: phoneCountdown } = useSendPhoneCode();

  const { setWidthdrwaGoogleAuthSuccess, setWidthdrwaGoogleAuthSuccess_virtual, setWidthdrwaType, setShow2FA_showGoogleTip, widthdrwaType, 
    withdrawCheckSuccessType, setWithdrawCheckSuccessType
   } =
    useJump() || {} 

  const { personValue } = usePersonInfoUtils();
  const [radioValue, setRadioValue] = useState(1);
  const dispatch = useDispatch();
  const [canEdit, setCanEdit] = useState(false);
  const [areaCode, setAreaCode] = useState<string | undefined>();
  const [phoneValue, setPhoneValue] = useState("");
  const [email, setEmail] = useState();

  const checkEmailReg = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Toast(intl.formatMessage({ id: "email_format___incorrect" }), "error");
      return false;
    }
    return true;
  };

  const handlePhoneSendCode = async (type: number) => {
    if ((canEdit && !phoneValue) || (!canEdit && !personValue?.phone)) {
      Toast(intl.formatMessage({ id: "phone_is_required" }), "warning");
      return;
    }
    //验证码类型 1.reset、2.bind、3.unbind 4.change 5.add
    await sendPhoneCode(canEdit ? phoneValue : personValue?.phone, canEdit ? areaCode : personValue?.areaCode, type)
  };

  function handleSendCodeByType(type: number) {
    console.log("handleSendCodeByType", radioValue);

    if (radioValue === ThirdParty.Email) {
      handleEmailSendCode(type);
    } else if (radioValue === ThirdParty.Phone) {
      handlePhoneSendCode(type);
    }
  }

  async function handleEmailSendCode(type: number) {
    if ((canEdit && !email) || (!canEdit && !personValue?.email)) {
      Toast(intl.formatMessage({ id: "email_is_required" }), "warning");
      return;
    }
    await sendEmailCode(canEdit ? email : personValue?.email, type)
  }

  function handleRadioChange(e: any) {
    console.log("radio checked", e.target.value);
    setRadioValue(e.target.value);
  }

  function getOptions() {
    if (canEdit) {
      return (
        <Radio.Group onChange={(e) => handleRadioChange(e)} value={radioValue}>
          {functionSwitchIds.includes(FuntionOpenMapping.Email_Login) && (
            <Radio value={ThirdParty.Email} className="text-sub-text">
              <FormattedMessage id="email" />
            </Radio>
          )}

          {functionSwitchIds.includes(FuntionOpenMapping.Phone_Login) && (
            <Radio value={ThirdParty.Phone} className="text-sub-text">
              <FormattedMessage id="phone" />
            </Radio>
          )}
        </Radio.Group>
      );
    } else {
      return (
        <Radio.Group
          onChange={(e) => handleRadioChange(e)}
          value={
            radioValue ? radioValue : 
            personValue?.accountBind?.includes(ThirdParty.Email)
              ? ThirdParty.Email
              : ThirdParty.Phone
          }
        >
          {personValue?.accountBind?.includes(ThirdParty.Email) && (
            <Radio value={ThirdParty.Email} className="text-sub-text">
              <FormattedMessage id="email" />
            </Radio>
          )}
          {personValue?.phone && (
            <Radio value={ThirdParty.Phone} className="text-sub-text">
              <FormattedMessage id="phone" />
            </Radio>
          )}
        </Radio.Group>
      );
    }
  }

  useEffect(() => {
    console.log("初始化单选", personValue);

    if (!personValue?.accountBind?.includes(ThirdParty.Email) && !personValue?.phone) {
      setCanEdit(true);
    }
    if (personValue?.accountBind?.includes(ThirdParty.Email)) {
      setRadioValue(ThirdParty.Email);
    } else if (personValue?.phone) {
      setRadioValue(ThirdParty.Phone);
    } else if (functionSwitchIds.includes(FuntionOpenMapping.Email_Login)) {
      setRadioValue(ThirdParty.Email);
    } else if (functionSwitchIds.includes(FuntionOpenMapping.Phone_Login)) {
      setRadioValue(ThirdParty.Phone);
    }
  }, [personValue]);

  function handlePhoneChange(
    value: string,
    data: CountryData
  ) {
    setAreaCode(data.dialCode);
    console.log("手机号", data.dialCode, value.replace(data.dialCode, ""));
    setPhoneValue(value.replace(data.dialCode, ""));
  }

  const handleSubmit = async () => {
    // 如果能修改、则调用verify接口, 否则调用ReqVerifyAccount接口
    if (canEdit) {
      if (radioValue === ThirdParty.Email) {
        bindEmail();
      } else if (radioValue === ThirdParty.Phone) {
        bindPhone();
      }
    } else {
      if (radioValue === ThirdParty.Email) {
        checkEmail();
      } else if (radioValue === ThirdParty.Phone) {
        checkPhone();
      }
    }
  };

  async function bindEmail() {
    try {
      hanldeMask(true);
      console.log("确认邮箱", email);
      if (!verifyCodeValue) {
        Toast(intl.formatMessage({ id: "please_input___code" }), "error");
        return;
      }
      const res = (await bindAccount(
        HallMessage.ReqBindAccountMessage.create({
          msgID: ProtoMessage.MID.ReqBindAccount,
          account: email,
          verifyCode: verifyCodeValue,
          threeParty: 1
        })
      )) as HallMessage.ResBindAccountMessage;
      console.log("请求验证邮箱返回", res);
      if (checkResponse(res)) {
        Toast(intl.formatMessage({ id: "success" }));
        dispatch(setIschangeUserInfo(true));
        handleCancel();
      }
    } finally {
      hanldeMask(false);
    }
  }

  async function bindPhone() {
    try {
      hanldeMask(true);
      if (!verifyCodeValue) {
        Toast(intl.formatMessage({ id: "please_input___code" }), "error");
        return;
      }
      console.log(
        "请求绑定手机号",
        personValue?.phone ? personValue.phone : phoneValue,
        personValue?.areaCode ? personValue.areaCode : areaCode
      );

      const res = await bindAccount(
        HallMessage.ReqBindAccountMessage.create({
          msgID: ProtoMessage.MID.ReqBindAccount,
          account: phoneValue,
          areaCode: areaCode,
          verifyCode: verifyCodeValue,
          threeParty: 6
        })
      );
      console.log("请求绑定手机号返回", res);
      if (checkResponse(res)) {
        Toast(intl.formatMessage({ id: "success" }));
        dispatch(setIschangeUserInfo(true));
        handleCancel();
      }
    } finally {
      hanldeMask(false);
    }
  }

  async function checkEmail() {
    try {
      hanldeMask(true);
      if (!verifyCodeValue) {
        Toast(intl.formatMessage({ id: "please_input___code" }), "error");
        return;
      }
      const res = (await verifyAccount(
        HallMessage.ReqVerifyAccountMessage.create({
          msgID: ProtoMessage.MID.ReqVerifyAccount,
          account: personValue?.email,
          verifyCode: verifyCodeValue,
          codeType: 6,
          threeParty: 1
        })
      )) as HallMessage.ResVerifyAccountMessage;
      console.log("请求验证邮箱返回", res);
      if (checkResponse(res)) {
        if (widthdrwaType == 2) {
          setWidthdrwaGoogleAuthSuccess_virtual(true)
        } else {
          setWidthdrwaGoogleAuthSuccess(true);
        }
        handleCancel();
      } else {
        setWidthdrwaType(0)
      }
    } finally {
      hanldeMask(false);
    }
  }

  async function checkPhone() {
    try {
      hanldeMask(true);
      console.log("确认手机号", verifyCode);

      if (!verifyCodeValue) {
        Toast(intl.formatMessage({ id: "please_input___code" }), "error");
        return;
      }
      const res = (await verifyAccount(
        HallMessage.ReqVerifyAccountMessage.create({
          msgID: ProtoMessage.MID.ReqVerifyAccount,
          account: personValue?.phone,
          verifyCode: verifyCodeValue,
          areaCode: personValue?.areaCode,
          codeType: 6,
          threeParty: 6
        })
      )) as HallMessage.ResVerifyAccountMessage;
      console.log("请求验证手机返回", res);
      debugger
      if (checkResponse(res)) {
        if (widthdrwaType === 3) {
          setWithdrawCheckSuccessType(3)
        } else if (widthdrwaType == 2) {
          setWidthdrwaGoogleAuthSuccess_virtual(true)
        } else {
          setWidthdrwaGoogleAuthSuccess(true);
        }
        handleCancel();
      }
    } finally {
      hanldeMask(false);
    }
  }

  return (
    <CommonModal
      width={screenWidth > 1024 ? "25%" : "100%"}
      footer={null}
      open={showModal}
      onCancel={handleCancel}
      maskClosable={false}
      title={
        <div className="bg-[--header-bg-color-basic] p-4 rounded-tl-2xl rounded-tr-2xl text-lg/6 font-medium">
          <span className="text-white">
            {canEdit ? <FormattedMessage id="bind" /> : <FormattedMessage id="enter_otp" />}
          </span>
        </div>
      }
    >
      <div className="flex flex-col w-full p-5 select-none h-full">
        <div className="flex flex-col space-y-7 w-full">
          <div className="flex flex-col gap-y-2">
            <div className="flex flex-col w-[60%] gap-y-2">{getOptions()}</div>
            <div className="flex justify-between ">
              <div className="flex w-[100%]">
                {canEdit ? (
                  radioValue === ThirdParty.Email ? (
                    <Input
                      onChange={(event) => setEmail(event.target.value)}
                      className="rounded-lg border  h-[52px] flex "
                      suffix={
                        <button
                          disabled={emailSendCode}
                          onClick={() => handleSendCodeByType(2)}
                          className=" gap-x-1 cursor-pointer flex bg-[--send-code-div-bg-color] text-[--send-code-text-color] items-center justify-center rounded-md h-[35px] px-2"
                        >
                          {emailSendCode ? (
                            ""
                          ) : (
                            <span>
                              <FormattedMessage id="send_code" />
                            </span>
                          )}
                          {emailSendCode && <div className=" font-bold ">{emailCountdown}s</div>}
                          <span></span>
                        </button>
                      }
                    />
                  ) : (
                    <div className="flex relative items-center w-full">
                      <PhoneCountryArea text={intl.formatMessage({ id: "enter_your_account" })} 
                        handlePhoneChange={handlePhoneChange} />
                      <div
                        onClick={() => handleSendCodeByType(2)}
                        className=" text-primary-text space-x-2 flex absolute right-3 "
                      >
                        <button
                          disabled={phoneSendCode}
                          className=" gap-x-1 cursor-pointer flex bg-[--send-code-div-bg-color] text-[--send-code-text-color] items-center justify-center rounded-md h-[35px] pl-2 pr-3"
                        >
                          <span>
                            <FormattedMessage id="send_code" />
                          </span>
                        </button>
                        {phoneSendCode && (
                          <div className=" font-bold flex items-center justify-center">
                            {phoneCountdown}s
                          </div>
                        )}
                      </div>
                    </div>
                  )
                ) : (
                  <Input
                    readOnly
                    value={
                      radioValue === ThirdParty.Email
                        ? personValue?.email
                        : "+" + personValue?.areaCode + " " + personValue?.phone
                    }
                    className="rounded-lg border  h-[52px] flex "
                    suffix={
                      <button
                        disabled={phoneSendCode}
                        onClick={() => handleSendCodeByType(6)}
                        className=" gap-x-1 cursor-pointer flex bg-[--send-code-div-bg-color] text-[--send-code-text-color] items-center justify-center rounded-md h-[35px] px-2"
                      >
                        {phoneSendCode ? (
                          ""
                        ) : (
                          <span>
                            <FormattedMessage id="send_code" />
                          </span>
                        )}
                        {phoneSendCode && <div className=" font-bold">{phoneCountdown}s</div>}
                        <span></span>
                      </button>
                    }
                  />
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-col space-y-2">
            <span className="text-sub-text-light-gray ">
              <FormattedMessage id="verify_code" />
            </span>
            <Input
              onChange={(event) => setVerifyCodeValue(event.target.value)}
              placeholder={placeHolder}
              className="h-[52px] flex rounded-lg border "
            />
          </div>
        </div>
        <div className="flex mt-auto">
          <Button
            disabled={!verifyCodeValue}
            onClick={() => handleSubmit()}
            className={`custom_button_shadow !h-[54px] !text-base btn w-full mt-8 ${
              verifyCodeValue ? "" : "opacity-50 cursor-not-allowed"
            }`}
          >
            <FormattedMessage id="submit" />
          </Button>
        </div>
        <div className="mt-[10px] w-full flex items-center justify-end">
          {personValue?.enable2FA ? (
            <div
              className="text-[#3EE749] test-sm font-medium cursor-pointer"
              onClick={() => {
                handleCancel();
                EventBus.emit("showModal", ModalType.showGoogleAuthCodeModal);
              }}
            >
              <FormattedMessage id="google_authenticator" />
            </div>
          ) : (
            <div
              className="text-[#3EE749] test-sm font-medium cursor-pointer"
              onClick={() => {
                handleCancel();
                setShow2FA_showGoogleTip(true);
                EventBus.emit("showModal", ModalType.show2FAModal);
              }}
            >
              <FormattedMessage id="google_authenticator" />
            </div>
          )}
        </div>
      </div>
    </CommonModal>
  );
};

export default OTPModal;
