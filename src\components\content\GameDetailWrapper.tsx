import TempThreeGameDetail from '@/templateThree/pages/TempThreeGameDetail';
import { useParams } from 'react-router-dom';

const GameDetailWrapper = () => {
  const { gameId, gameName, platformId, platformName, sectionId } = useParams<{ gameId: string, gameName: string, platformId: string, 
    platformName: string, sectionId: string }>();

  return (
    <TempThreeGameDetail key={gameId} gameId={gameId} gameName={gameName} platformId ={platformId} brand={platformName} sectionId={sectionId} />
  );
};

export default GameDetailWrapper;