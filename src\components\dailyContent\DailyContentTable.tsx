import emptyIcon from '@/assets/noData.svg'
import vip_level_1 from '@/assets/vip/vip_level_detail_1.svg'
import vip_level_2 from '@/assets/vip/vip_level_detail_2.svg'
import vip_level_3 from '@/assets/vip/vip_level_detail_3.svg'
import { useCurrency } from '@/common_components/useCurrency'
import { useConversionUtils } from '@/hooks/utils/useConversionUtils'
import { useToastUtils } from '@/hooks/utils/useToastUtils'
import { ActivityMessage } from '@/protos/Activity'
import { BackStageMessage } from '@/protos/BackStage'
import { ConfigProvider, Table, TableProps } from "antd"
import { useEffect, useState } from 'react'
import { FormattedMessage } from "react-intl"
import { useAllGame } from '../games/AllGameProvider'
import { useConfigData } from '../games/providers/ProjectConfigProvider'
import { useUtils } from '../useUtils'

const DailyContentTable: React.FC<{
    datas: ActivityMessage.IDailyRankInfo[],
    loading: boolean
}> = ({ datas, loading = false }) => {
    const [headData, setHeadData] = useState<BackStageMessage.HeadInfo[] | []>([])
    const { allConfigData } = useAllGame() as {
        allConfigData: ResConfigDataMessage,
    }
    const { showCurrencyId } = useConfigData();
    const {viewFiat} = useCurrency()
    const { cronMap, formatNumberQfw, hidePersonName } = useUtils()
    const { intl } = useToastUtils();
    const { converterPlus } = useConversionUtils();
    const [showDatas, setShowDatas] = useState<ActivityMessage.IDailyRankInfo[] | []>([])
    useEffect(() => {
        if (datas) {
            setShowDatas(datas.map((data, index) => {
                data.key = index
                return data
            })
            )
        }
    }, [datas])

    useEffect(() => {
        if (allConfigData && allConfigData.headList) {
            setHeadData(allConfigData.headList)
        }
    }, [allConfigData])


    const vipLevelImg = {
        0: vip_level_1,
        1: vip_level_2,
        2: vip_level_3
    }
    // 定义列
    const columns: TableProps<DataType>['columns'] = [
        {
            title: <span className='lg:text-sm/4 text-[10px]/[20px] font-medium'>#</span>,
            dataIndex: "rank",
            key: "rank",
            align: 'left',
            render: (_, record: ActivityMessage.IDailyRankInfo, index) => (
                <div className='flex items-center justify-start'>
                    {record.ranking < 4 ? <img src={vipLevelImg[record.ranking - 1]} className='lg:w-7 w-6' alt=""/> : record.ranking + 'th'}
                </div>
            ),
            className: 'text-sub-text'
        },
        {
            title: <span className='lg:text-sm/4 text-[10px]/[20px] font-medium'>{intl.formatMessage({ id: 'player' })}</span>,
            dataIndex: 'player',
            key: 'player',
            className: 'text-sub-text-light-gray  ',

            align: 'center',
            render: (_, record: ActivityMessage.IDailyRankInfo) => (
                <div className={`  text-[#FFFFFF] w-full lg:text-sm font-medium text-[10px]/[15px] items-center justify-center flex gap-x-2`}>
                    <img src={headData.find(head => head.headId == record.headId)?.fileUrl} alt="" className='w-[20px] h-[20px]' />
                    <span>{hidePersonName(record.name)}</span>
                </div>
            ),
        },
        {
            title: <span className='lg:text-sm/4 text-[10px]/[20px] font-medium'>{intl.formatMessage({ id: 'wagered' })}</span>,
            dataIndex: 'wagered',
            key: 'wagered',
            className: ' text-sub-text-light-gray  ',

            align: 'center',
            render: (_, record: ActivityMessage.IDailyRankInfo) => (
                <div className={`  gap-x-[2px] w-full text-sub-text-green lg:text-sm flex font-medium lg:justify-center justify-start items-center text-[10px]/[15px]`}>
                    <span>{cronMap[viewFiat]?.symbol} </span>
                    <span>{converterPlus(record.wagered || 0, showCurrencyId, viewFiat, allConfigData?.exchangeRateList)} </span>
                </div>
            ),
        },
        {
            title: <span className='lg:text-sm/4 text-[10px]/[20px] font-medium'>{intl.formatMessage({ id: 'prize' })}</span>,
            dataIndex: 'prize',
            key: 'prize',
            className: ' text-sub-text ',

            align: 'right',
            render: (_, record: ActivityMessage.IDailyRankInfo) => (
                <div className={`  gap-x-[2px] w-full text-sub-text-green lg:text-sm font-medium flex justify-end items-center text-[10px]/[15px]`}>
                    <span>{cronMap[viewFiat]?.symbol} </span>
                    <span>{converterPlus(record.prize || 0, showCurrencyId, viewFiat, allConfigData?.exchangeRateList)}</span>
                    
                    <span className='text-sub-text-light-gray'>({formatNumberQfw((record.prizeRate || 0) * 100, 2)}%)</span>
                </div>
            ),
        }
    ];

    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            lineWidth: 0, // 表格线条宽度
                            // headerSplitColor: 'var(--button-bg-color)', // 表头分割线颜色
                            // headerBg: 'var(--button-bg-color)', // 表头背景颜色
                        }
                    }
                }}
            >
                <Table
                    locale={{
                        emptyText:
                            (
                                <div className="min-h-[260px] flex flex-col items-center justify-center">
                                    <span>
                                        <img src={emptyIcon} className="w-[200px]" alt=""/>
                                    </span>
                                    <span className="text-[--node_data_text_color] text-lg ">
                                        <FormattedMessage id="no_data" />
                                    </span>
                                </div>
                            )
                    }}
                    loading={loading}
                    pagination={false}
                    size='small'
                    columns={columns}
                    dataSource={showDatas}
                    className='min-h-[300px]'
                // className=' overflow-hidden'
                // rowClassName={(record, index) =>
                //     index % 2 === 0 ? "" : "bg-content-second-gray"
                // }
                />
            </ConfigProvider>
        </>
    )
}

export default DailyContentTable