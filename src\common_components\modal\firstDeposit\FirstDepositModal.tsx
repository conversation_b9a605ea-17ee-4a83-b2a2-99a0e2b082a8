import first_4Icon from "@/assets/firstDeposit/4.webp";
import CommonModal from "@/components/CommonModal";
import HintContent from "@/components/hint/HintContent";
import { useUtils } from "@/components/useUtils";
import { BillingMessage } from "@/protos/Billing";
import { Button } from "antd";
import { memo, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useJump } from "../../context/useJumpContext";
import FirstDepositContent from "./FirstDepositContent";

const FirstDepositModal: React.FC<{ handleCancel: () => void; showModal: boolean }> = memo(
  ({ showModal, handleCancel }) => {
    const handleClose = () => {};

    const { hanldeMask } = useUtils()
    const { showHint } = useJump() || {};
    useState<BillingMessage.IResCreateRechargeOrderMessage>(null);
    const childrenRef = useRef();

    const newStyles = {
      header: { backgroundColor: "var(--header-bg-color-basic)", borderradius: "16px" },
      content: {
        backgroundColor: "var(--content-bg-color-basic)",
        padding: 0,
        height: "auto",
        top: 70
      }
    };

    const signIn = () => {
      if (childrenRef.current) {
        hanldeMask(true)
        childrenRef.current.handleSignIn();
      }
    };
    // 如果没有打开则返回空
    if (!showModal) {
      return;
    }

    return (
      <>
        <CommonModal
          width={"330px"}
          footer={null}
          open={showModal}
          onCancel={handleCancel}
          afterClose={handleClose}
          maskClosable={false}
          styles={newStyles}
        >
          <img src={first_4Icon} alt="" className=" absolute -top-[31%] " style={{ zIndex: 1 }} />
          <div
            className="text-primary-text flex flex-col w-full bg-[#1B1E23] rounded-[16px] relative h-[477px]"
            style={{ backdropFilter: "blur(30px)", zIndex: 1 }}
          >
            {showHint && <HintContent popupId={showHint} />}
            {/* <ScreenSpin loading={rechargeLoading} /> */}
            <div className="w-full flex items-center justify-between py-[14px] px-[22px] h-[52px] ">
              <span
                className="text-[18px] font-medium"
                style={{ letterSpacing: "-0.27px", fontFamily: "Inter" }}
              >
                <FormattedMessage id="check_in" />
              </span>
            </div>
            <div
              className="mt-[10px] px-[17px] w-full relative"
              style={{ height: "calc(100% - 124px)" }}
            >
              <div
                className=" absolute -top-[20px] left-[50%] -translate-x-[50%]"
                style={{ zIndex: 2 }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="324"
                  height="190"
                  viewBox="0 0 324 190"
                  fill="none"
                >
                  <g filter="url(#filter0_f_4428_67690)">
                    <ellipse cx="165" cy="72" rx="65" ry="18" fill="#999C39" />
                  </g>
                  <defs>
                    <filter
                      id="filter0_f_4428_67690"
                      x="0"
                      y="-46"
                      width="330"
                      height="236"
                      filterUnits="userSpaceOnUse"
                      colorInterpolationFilters="sRGB"
                    >
                      <feFlood floodOpacity="0" result="BackgroundImageFix" />
                      <feBlend
                        mode="normal"
                        in="SourceGraphic"
                        in2="BackgroundImageFix"
                        result="shape"
                      />
                      <feGaussianBlur
                        stdDeviation="50"
                        result="effect1_foregroundBlur_4428_67690"
                      />
                    </filter>
                  </defs>
                </svg>
              </div>
              <FirstDepositContent childrenRef={childrenRef} callback={handleCancel}/>
            </div>

            <div
              className="h-[62px] fixed bottom-0 left-0 w-full flex items-center justify-center bg-[#1B1E23] 
              px-[22px] rounded-bl-[16px] rounded-br-[16px]"
            >
              <Button
                onClick={signIn}
                className="text-[18px] font-semibold text-black w-full h-[36px] rounded-lg bg-[--main-theme-color]"
              >
                <FormattedMessage id="check_in" />
              </Button>
            </div>
          </div>
        </CommonModal>
      </>
    );
  }
);

export default memo(FirstDepositModal);
