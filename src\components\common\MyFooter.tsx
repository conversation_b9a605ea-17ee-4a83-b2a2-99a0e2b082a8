
import footerIcon from '@/assets/content/footer.svg';
import mobileFooterIcon from "@/assets/content/mobileFooter.svg";
import { Divider } from 'antd';
import { memo, useEffect, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { useSelector } from 'react-redux';
import { useScreenWidth } from '../../utils/MobileDetectionContext';
import AcceptedNewWorks from '../content/AcceptedNewWorks';
import ContentJump from '../content/ContentJump';
import MobileCrypto from '../content/MobileCrypto';
import SlotsSwipe from '../content/SlotsSwipe';
import TransformList_bc from '../content/TransformList_bc';
import { useGameData } from '../games/providers/GameDataProvider';

interface FooterShow {
    showProviderList: boolean,
    showtransfromlist: boolean,
    gameId: string,
    channel: number
}

const MyFooter: React.FC<FooterShow> = ({ sectionId, showProviderList = false, showtransfromlist = false, gameId, channel = 1 }) => {
    const { screenWidth } = useScreenWidth() 
    const { allGameLoading, allGameMapping, providerSubChannel, allGameChannelMapping } = useGameData()
    const Logo = useSelector((state: any) => state.systemReducer.pcLogo)

    const [providerList, setProviderList] = useState<PageList[]>([])
    useEffect((() => {
        if (allGameLoading) return;
        if (allGameMapping) {
            if (sectionId) {
                setProviderList(allGameMapping[sectionId])
            } else {
                if (providerSubChannel[channel]) {
                    setProviderList(allGameMapping[providerSubChannel[channel]])
                }
            }
        }
    }), [allGameLoading, allGameMapping])

    return (
        <div className='flex flex-col gap-y-[12px]'>
            {
                showProviderList && allGameChannelMapping && allGameChannelMapping[providerSubChannel[channel]] && providerList?.GameProviders?.length > 0 && (
                    <SlotsSwipe gameType={allGameChannelMapping[providerSubChannel[channel]].subChannelType} source='footer' showText={false} viewAll={true} infinite={false} autoPlay={false}
                        sctionId={providerSubChannel[channel]} datas={providerList?.GameProviders}
                        title={allGameChannelMapping[providerSubChannel[channel]]?.subChannelName} 
                        bannerImg={allGameChannelMapping[providerSubChannel[channel]]?.channelHomeIcon1}/>
                )
            }
            {
                showtransfromlist && (
                    <TransformList_bc gameId={gameId} />
                )
            }
            {
                screenWidth <= 1024 && (
                    <>
                        <AcceptedNewWorks />
                        <MobileCrypto />
                    </>
                )
            }

            {
                screenWidth > 1024 && (
                    <>
                        <AcceptedNewWorks />
                        <Divider className='w-full border-[#3A3B3D]' />
                        <ContentJump />
                        <Divider className='w-full border-[#3A3B3D]' />
                    </>

                )
            }

            <div className="flex sm:flex-row flex-col relative w-full items-center sm:items-start mb-10"
            >


                <div className="flex w-full lg:pb-12 items-center lg:justify-between justify-center">
                    {
                        screenWidth > 1024 && (
                            <div className='flex flex-col lg:w-[60%]'>
                                <div className='flex gap-x-2 items-center cursor-pointer w-[149px] h-[49px]'
                                    onClick={() => navigate('/')}>
                                    <img src={Logo} alt=''/>
                                    {/* <span className='text-xl uppercase font-bold'><FormattedMessage id='win_game' /></span> */}
                                </div>
                                <div className='mt-4  antialiased  text-sub-text select-none'>
                                    <FormattedMessage id="wingame_is___mind_1" />
                                </div>
                                <div className='mt-4  antialiased text-sub-text select-none'>
                                    <FormattedMessage id="wingame_is___mind_2" />
                                </div>
                                <div className='mt-4 antialiased text-sub-text select-none'>
                                    <FormattedMessage id="wingame_is___mind_3" />
                                </div>
                            </div>
                        )
                    }

                    <div className="flex items-center  ">
                        {
                            screenWidth > 1024 && (
                                <>
                                    {/* <img src={Logo3} alt="btn_cd" className="w-[261px] h-[86px]" />
                                <img src={Logo2} alt="btn_cd" className="w-[114px] h-[130px]" /> */}
                                    <img src={footerIcon} alt="" />
                                </>
                            )
                        }
                        {
                            screenWidth <= 1024 && (
                                <img src={mobileFooterIcon} alt="" />
                            )
                        }
                    </div>


                </div>

            </div>
        </div>

    )
}

export default memo(MyFooter);