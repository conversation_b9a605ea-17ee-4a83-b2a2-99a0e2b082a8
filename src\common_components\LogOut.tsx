import { setIsLoginOut } from "@/store";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

import { useHanldeErrorCode } from "@/components/useHanldeErrorCode";

const LogOut: React.FC = () => {
  const dispatch = useDispatch();
  const isLoginOut = useSelector((state: any) => state.systemReducer.isLoginOut);
  const { handlerLogOut } = useHanldeErrorCode()

  useEffect(() => {
    console.log("获取到用户退出推送", isLoginOut);

    if (isLoginOut) {
      handlerLogOut();
      dispatch(setIsLoginOut(false));
    }
  }, [isLoginOut]);

  return <></>;
};

export default LogOut;
