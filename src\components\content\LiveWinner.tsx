import { useJump } from "@/common_components/context/useJumpContext";
import { useCurrency } from "@/common_components/useCurrency";
import { usePersonInfoUtils } from "@/common_components/usePersonInfoUtils";
import { BackStageMessage } from "@/protos/BackStage";
import { ProtoMessage } from "@/protos/common";
import { HallMessage } from "@/protos/Hall";
import { getRecentBigWinsData } from "@/service/hall";
import { Image, Skeleton } from "antd";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
// import { useActivated, useDeactivated } from "react-route-cache";
import { useNavigate } from "react-router-dom";
import "swiper/css"; // 引入 Swiper 的 CSS 文件
import "swiper/css/autoplay"; // 引入 autoplay 的 CSS
import { Autoplay } from "swiper/modules"; // 引入 autoplay 模块
import { Swiper, SwiperSlide } from "swiper/react";
import { useAllGame } from "../games/AllGameProvider";
import { useConfigData } from "../games/providers/ProjectConfigProvider";
import { useUtils } from "../useUtils";
import { useConversionUtils } from "@/hooks/utils/useConversionUtils";
import { PopuTriggerType } from '@/utils/enums';
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import EventBus from "@/utils/EventBus";
import { ModalType } from "@/utils/enums";
import { FormattedMessage } from "react-intl";
import reward_icon from '@/assets/fixedTemplateBrazil/home/<USER>'

const LiveWinner: React.FC = memo(() => {
  const { cronMap, isLogin, hidePersonName } = useUtils();
  const { intl, Toast } = useToastUtils();
  const { converterPlus } = useConversionUtils();
  const [bigWinList, setBigWinList] = useState<HallMessage.IBigWinsInfo[]>();
  const { allConfigData } = useAllGame() as {
    allConfigData: BackStageMessage.ResConfigDataMessage;
  };
  const { showCurrencyId } = useConfigData();
  const { fiatSymbol, viewFiat } = useCurrency();

  const { personValue } = usePersonInfoUtils();
  const { refreshPopupList } = useJump() || {};

  const [loading, setLoading] = useState(false);
  const userHost = useSelector((state: any) => state.systemReducer.hallHost);
  const navigate = useNavigate();
  console.log("LiveWinner执行了=======================");

  useEffect(() => {
    getDatas();
  },[])

  useEffect(() => {
    getDatas();
  },[])

  const getDatas = useCallback(async () => {
    try {
      setLoading(true);
      console.log("调用getRecentBigWinsData");
      const recentDatas = (await getRecentBigWinsData(
        HallMessage.ReqRecentBigWinsDataMessage.create({
          msgID: ProtoMessage.MID.ReqRecentBigWinsData,
          date: 1, //1.1d 2.3d 3.7d
          recentType: 0 //0.all 102.slots 103.liveCasino 101.原创
        })
      )) as HallMessage.ResRecentBigWinsDataMessage;
      if (recentDatas.error > 0) {
        Toast(
          intl.formatMessage({ id: "error_message_" + recentDatas.error }),
          "error",
          recentDatas.error
        );
      }
      console.log("获取到recent big wins数据", recentDatas.bigWinsList);
      setBigWinList(recentDatas.bigWinsList);
    } finally {
      setLoading(false);
    }
  }, [userHost]);

  const gotoGame = async (item: HallMessage.IBigWinsInfo) => {
    if (!isLogin) {
      EventBus.emit("showModal", ModalType.showLoginModal)
      return;
    }
    const cItem =
      personValue &&
      (personValue.cItem.find(
        (item) => item.currencyId === showCurrencyId
      ) as ProtoMessage.IDCurrencyItem);
    console.log("进入游戏:", personValue, showCurrencyId, cItem);
    const havePopu = await refreshPopupList(PopuTriggerType.ENTER_GAME, cItem);
    console.log("进入游戏--是否有弹窗", havePopu);
    if (!havePopu || havePopu.length === 0) {
      console.log("跳转游戏", item);
      navigate(`/gameDetail/${item.gameId}`);
    }
  };

  const getDataList = () => {
    if (bigWinList && bigWinList.length > 0) {
      return bigWinList.map((item, index) => (
        <SwiperSlide
          key={`${item.gameId}-${index}`}
          className="flex flex-col justify-center items-center select-none h-[114px] w-full"
          style={{ width: "auto" }} // 设置宽度为 auto
        >
          <div
            onClick={() => gotoGame(item)}
            className="flex justify-center items-end cursor-pointer h-[96px] w-[84px] rounded-[8px]"
            style={{
              backgroundImage: `url(${item.gameIcon})`, backgroundSize: 'cover', backgroundRepeat: 'no-repeat',
              backdropFilter: 'blur(0.38181817531585693px)'
            }}
          >

            <div className="w-[76px] h-[38px] mb-[4px] rounded-[4px] flex flex-col justify-center items-center bg-[#1b1e23] gap-y-[3px]" style={{
              background: 'rgba(3, 20, 8, 0.75)'
            }}>
              <div className="flex mt-1 items-center justify-center space-x-1">
                <img src={cronMap[item.currencyId]?.icon} className="w-[12px] h-[12px] " alt="" />
                <span className="text-[10px] w-[40px] truncate" title={hidePersonName(item.playerName)}>
                  {hidePersonName(item.playerName)}
                </span>
              </div>
              <div className="text-[12px] text-[#FAD900] flex font-semibold items-center justify-center truncate">
                {fiatSymbol}{" "}
                {converterPlus(
                  item.amount || 0,
                  item.currencyId,
                  viewFiat,
                  allConfigData?.exchangeRateList
                )}
              </div>
            </div>
          </div>
        </SwiperSlide>
      ));
    }
    return null;
  };

  const swiperRef = useRef(null);

  return (
    <>
      <div className="flex flex-col w-full gap-y-4">
        <div className=" py-2 px-[5px] flex flex-col w-full relative rounded-[12px] lg:h-[142px] gap-y-[8px]">
          <div className="flex items-center gap-x-2">
            <img src={reward_icon} alt="" />
            <span className="text-[18px] font-bold text-white">
              <FormattedMessage id="recentwards" />
            </span>
          </div>
          <div className="w-full justify-center items-center lg:h-[142px]">
            {bigWinList && bigWinList.length > 0 && (
              <Swiper
                spaceBetween={10} // 幻灯片之间的间隔
                slidesPerView={"auto"} // 自动计算显示个数
                direction="horizontal" // 水平方向
                loop={true}
                autoplay={{
                  delay: 2000
                }}
                modules={[Autoplay]}
                onSwiper={(swiper) => (swiperRef.current = swiper)}
              >
                {getDataList()}
              </Swiper>
            )}
          </div>
        </div>
      </div>
    </>
  );
});

export default LiveWinner;
