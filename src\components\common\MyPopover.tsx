import { Popover } from "antd";
import React, { useEffect, useState } from "react";

const MyPopover: React.FC = ({
  currencyDiv,
  popoverContent,
  open,
  setOpen,
  showTitleImg = true,
  children,
  source = 1
}) => {
  const [currencyWidth, setCurrencyWidth] = useState("0px");
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    setShowArrowDir(newOpen);
  };
  const [showArrowDir, setShowArrowDir] = useState(false);
  function handlerPopoverClick(value: boolean) {
    setShowArrowDir(value);
  }
  useEffect(() => {
    if (currencyDiv.current) {
      setCurrencyWidth(currencyDiv.current.offsetWidth + "px");
    }
    if (!open) {
      setShowArrowDir(false);
    }
  }, [open]);

  return (
    <Popover
      open={open}
      onOpenChange={handleOpenChange}
      onClick={() => handlerPopoverClick(!showArrowDir)}
      color={`${
        source === 1 ? "var(--menu-hover-bg-color)" : "var(--select-style-seconde-bg-color)"
      }`}
      placement={"bottomLeft"}
      content={popoverContent}
      trigger="click"
      arrow={false}
      // overlayClassName={`custom-popover`}
      styles={{
        body: {
          width: currencyWidth,
          borderRadius: "8px",
          padding: "0px",
          // borderWidth: '1px',
          // borderColor: 'var(--div-border-color)',
          maxHeight: "330px",
          overflow: "auto",
          marginTop: "2px"
        }
      }}
    >
      {children}
    </Popover>
  );
};

export default MyPopover;
