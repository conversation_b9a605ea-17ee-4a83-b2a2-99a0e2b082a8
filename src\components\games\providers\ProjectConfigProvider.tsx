import { useQueryConfigData } from "@/hooks/queryHooks/useQueryConfigData";
import { setCronMap, setFiatSymbol, setGoogleInfoRedux, setViewFiat } from "@/store";
import { FuntionOpenMapping } from '@/utils/enums';
import { createContext, useContext, useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";

const ConfigContext = createContext(null);
export const ProjectConfigProvider = ({ children, config }) => {
    const dispatch = useDispatch();
    const [TelegramRobotId, setTelegramRobotId] = useState<string | null>();

    const { data: vipClubList } = useQueryConfigData(6);
    const { data: maintainNoticeInfo } = useQueryConfigData(0, 1000 * 60 * 60 * 24);

    // 处理配置数据
    useEffect(() => {
        if (config) {
            const itemList = []
            if (config.cItemList) {
                itemList.push(...config.cItemList)
            }
            if (config.uItemList) {
                itemList.push(...config.uItemList)
            }
            if (itemList.length > 0) {
                dispatch(setCronMap(JSON.parse(JSON.stringify(itemList))));
            }
            if (config.showCurrencyId) {
                dispatch(setViewFiat(config.showCurrencyId));
                config.cItemList.find((item) => {
                    if (item.currencyId === config.showCurrencyId) {
                        dispatch(setFiatSymbol(item.symbol));
                    }
                });
            }
            const functionIds = config.functionId;

            if (
              config.threePartyLoginList &&
              functionIds.includes(FuntionOpenMapping.Three_Party_Login)
            ) {
              const googleInfo = config.threePartyLoginList.find(
                (item) => item.threePartyType === 1
              );
              const telegramInfo = config.threePartyLoginList.find(
                (item) => item.threePartyType === 2
              );
              if (googleInfo) {
                const info = {
                  client_id: googleInfo.extend_1,
                  client_secret: googleInfo.extend_2,
                  redirect_uri: `${window.location.protocol}//${window.location.host}/authCode`,
                  authUrl: "https://accounts.google.com/o/oauth2/v2/auth",
                  tokenUrl: "https://oauth2.googleapis.com/token",
                  scope: "openid email profile"
                };
                dispatch(setGoogleInfoRedux(JSON.parse(JSON.stringify(info))));
              }
              console.log("三方登录信息", telegramInfo);
              
              if (telegramInfo) {
                setTelegramRobotId(telegramInfo.extend_1);
              }
            }

            document.title = config?.webSiteData?.title;
        }
    }, [config]);

    const value = useMemo(() => ({
        helpcenterData: config?.helpCenterInfo,
        webSiteData: config?.webSiteData,
        showCurrencyId: config?.showCurrencyId,
        vipClubList:vipClubList?.vipClubList,
        maintainNoticeInfo:maintainNoticeInfo?.maintainNoticeInfo,
        functionSwitchIds: config?.functionId,
        TelegramRobotId: TelegramRobotId
    }), [config, vipClubList, maintainNoticeInfo,TelegramRobotId]);

    return (
        <ConfigContext.Provider value={value}>
            {children}
        </ConfigContext.Provider>
    );
}

export const useConfigData = () => useContext(ConfigContext);