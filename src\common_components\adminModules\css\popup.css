.left-in-exit {
    transform: translateX(-100%);
    opacity: 1;
  }
  
  .left-in-exit-done {
    transform: translateX(-100%);
    opacity: 0;
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }

  .left-in-enter {
    transform: translateX(-100%);
    opacity: 0;
  }
  
  .left-in-enter-done {
    transform: translateX(0%);
    opacity: 1;
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }
  
  .right-in-enter {
    transform: translateX(100px);
    opacity: 0;
  }
  
  .right-in-enter-done {
    transform: translateX(0%);
    opacity: 1;
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }
  
  .right-in-exit {
    transform: translateX(0%);
    opacity: 1;
  }
  
  .right-in-exit-done {
    transform: translateX(100px);
    opacity: 0;
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }


  .fade-out-enter {
    opacity: 1;
    transform: scale(1);
  }
  
  .fade-out-enter-done {
    opacity: 0;
    visibility: hidden;
    transform: scale(0.5);
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
  }
  
  .fade-out-exit {
    opacity: 0;
    transform: scale(0.3);
  }
  
  .fade-out-exit-done {
    opacity: 1;
    transform: scale(1);
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }
  
  .long-div-fade-enter {
    opacity: 0;
    width: 0px;
  }
  
  .long-div-fade-enter-done {
    opacity: 1;
    width: 279px;
    visibility: visible;
    transition: width 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }
  
  .long-div-fade-exit {
    opacity: 1;
    width: 279px;
  }
  
  .long-div-fade-exit-done {
    opacity: 0;
    width: 0px;
    visibility: hidden;
    transition: width 0.3s ease-in-out, opacity 0.3s ease-in-out, visibility 0s 0.3s;
  }
  
  .pop_long_left_div {
    border-radius: 0px 40px 40px 0px;
    border-top: 1px solid #666371;
    border-bottom: 1px solid #666371;
    border-left: 1px solid #666371;
    background: linear-gradient(180deg, rgba(64, 64, 74, 0.66) 0%, rgba(23, 22, 28, 0.66) 100%);
    backdrop-filter: blur(3.6500000953674316px);
  }

  .pop_long_right_div {
    border-radius: 40px 0px 0px 40px;
    border-top: 1px solid #666371;
    border-bottom: 1px solid #666371;
    border-right: 1px solid #666371;
    background: linear-gradient(180deg, rgba(64, 64, 74, 0.66) 0%, rgba(23, 22, 28, 0.66) 100%);
    backdrop-filter: blur(3.6500000953674316px);
  }