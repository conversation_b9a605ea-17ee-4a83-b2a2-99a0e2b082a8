import CommonModal from "@/components/CommonModal";
import { useUtils } from "@/components/useUtils";
import { ProtoMessage } from "@/protos/common";
import { HallMessage } from "@/protos/Hall";
import { get2FAVerificationCode } from "@/service/hall";
import { useScreenWidth } from "@/utils/MobileDetectionContext";
import { Button } from "antd";
import { Input } from "antd";
import { useState } from "react";
import { FormattedMessage } from "react-intl";
import { useJump } from "../context/useJumpContext";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import EventBus from "@/utils/EventBus";
import { ModalType } from "@/utils/enums";

const GoogleAuthCodeModal: React.FC = ({ showModal, handleCancel }) => {
    if (!showModal) return null
    const { screenWidth } = useScreenWidth() 
    const {setWidthdrwaGoogleAuthSuccess,  setWidthdrwaGoogleAuthSuccess_virtual, setShow2FA_showGoogleTip, widthdrwaType, setWidthdrwaType} = useJump()  as {
        setWidthdrwaGoogleAuthSuccess: () => void
    }
    const [code, setCode] = useState('')
    const { hanldeMask } = useUtils()
    const { intl, Toast, checkResponse } = useToastUtils();

    const handleClose = () => {
        setShow2FA_showGoogleTip(false)
    }

    async function handlerEnable() {
        if (!code || code.length < 6) {
            Toast(intl.formatMessage({ id: "please_input___code" }), 'error')
            return
        }
        try {
            hanldeMask(true)
            const res = await get2FAVerificationCode(
                HallMessage.Req2FAVerificationCodeMessage.create({
                    msgID: ProtoMessage.MID.Req2FAVerificationCode,
                    verificationCode: code,
                })
            )
            console.log('返回2FA Verification Code数据为：', res);
            if (checkResponse(res)) {
                if (widthdrwaType == 2) {
                    setWidthdrwaGoogleAuthSuccess_virtual(true)
                } else {
                    setWidthdrwaGoogleAuthSuccess(true)
                }
                handleCancel()
            }
        } finally {
            hanldeMask(false)
        }
    }

    const handleCodeValue = (value: string) => {
        console.log('得到输入',value);
        
        if (/^\d*$/.test(value)) {
            setCode(value); // 更新状态
          }
    }

    const handleShowOtpModal = () => {
        handleCancel()
        EventBus.emit("showModal", ModalType.showOtpModal)
    }

    return (
        <>
            <CommonModal 
                width={screenWidth > 1024 ? '30%' : '100%'}
                footer={null}
                open={showModal}
                onCancel={handleCancel}
                afterClose={handleClose}
                maskClosable={false}
                title={
                    <div className="bg-[--header-bg-color-basic] p-4 rounded-tl-2xl rounded-tr-2xl text-lg/6 font-medium">
                      <span className="text-white">
                        <FormattedMessage id="google_auth_code" />
                      </span>
                    </div>
                  }
                >
                <div className="flex flex-col w-full p-5 min-h-550px overflow-y-auto select-none space-y-1">
                    <div className="flex flex-col space-y-2">
                            <span className="text-sub-text-light-gray font-medium"><FormattedMessage id="authentication_code" /></span>
                            <Input maxLength={6} className="h-[52px]" value={code}
                                onChange={(event) => handleCodeValue(event.target.value)} placeholder={intl.formatMessage({ id: "enter_the_6-digit_code" })}/>
                        </div>
                    <div className="flex flex-col">
                        <Button onClick={handlerEnable} className={`!text-base !font-semibold custom_button_shadow !h-[54px] btn w-full mt-8`} >
                            <FormattedMessage id="submit" />
                        </Button>
                    </div>
                    <div onClick={() => handleShowOtpModal()} className="w-full text-[#3EE749] text-sm font-medium flex items-center justify-end pt-1 cursor-pointer">
                        <span>
                            <FormattedMessage id="otp_withdraw" />
                        </span>
                    </div>
                </div>
            </CommonModal>
        </>

    )
}

export default GoogleAuthCodeModal