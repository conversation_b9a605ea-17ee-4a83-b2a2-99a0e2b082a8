import CommonModal from "@/components/CommonModal";
import { useAllGame } from "@/components/games/AllGameProvider";
import HintContent from "@/components/hint/HintContent";
import DepositZyModel from "@/components/personInfo/DepositZyModel";
import ScreenSpin from "@/components/ScreenSpin2";
import { useUtils } from "@/components/useUtils";
import { BillingMessage } from "@/protos/Billing";
import { Button } from "antd";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useJump } from "../context/useJumpContext";
import { usePoint } from "../usePoint";
import { useCreateOrder } from "./directRecharge/useCreateOrder";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { useIntl } from "react-intl";
import EventBus from "@/utils/EventBus";
import { ModalType } from "@/utils/enums";

const LimitTimeRecharge: React.FC<{ handleCancel: () => void; showModal: boolean }> = memo(
  ({ showModal, handleCancel }) => {
    const handleClose = () => {};
    const intl = useIntl();
    const { Toast } = useToastUtils();
    const { allConfigData } = useAllGame();
    const { handleJump } = useJump() || {};
    const navigate = useNavigate();
    const { createOrder, onlyCrpto, rechargeLoading } = useCreateOrder();
    const { isLogin, hanldeMask } = useUtils();
    const [oepnDepsitIfreme, setOpenDepsitIfreme] = useState(false);
    const [depositOrderInfo, setDepositOrderInfo] =
      useState<BillingMessage.IResCreateRechargeOrderMessage>(null);
    const {
      setIsLimitRecharge,
      showHint
    } = useJump() || {};
    useState<BillingMessage.IResCreateRechargeOrderMessage>(null);
    const { activityPoint, errorMessagePoint } = usePoint();
    const timer = useRef(null);

    useEffect(() => {
      return () => {
        if (timer.current) {
          clearTimeout(timer.current);
        }
      };
    }, []);

    const createMyOrder = useCallback(
      async (amount, currencyId) => {
        try {
          if (!isLogin) {
            handleCancel();
            EventBus.emit("showModal", ModalType.showLoginModal)
            return;
          }
          if (onlyCrpto.current) {
            EventBus.emit("showModal", ModalType.showWalletModal)
            return;
          }
          hanldeMask(true)
          const res = await createOrder(amount, 302, location.pathname, currencyId, false);
          console.debug("创建订单成功", res);
          if (res.payAddress) {
            setDepositOrderInfo(res);
            setOpenDepsitIfreme(true);
          }
          setIsLimitRecharge(true);
        }  catch (error) {
          const errorMessage = JSON.stringify(error, Object.getOwnPropertyNames(error));
          errorMessagePoint(errorMessage, location.pathname, window.location.host)
          if (error.code === "ECONNABORTED") {
            Toast(intl.formatMessage({ id: "cheannel_exception" }), "error");
          }else if (error.message === "Network Error" || error.message.includes("ERR_NETWORK")) {
            Toast(intl.formatMessage({ id: "newwork_error" }), "error");
          }
        } finally {
          hanldeMask(false)
        }
      },
      [isLogin]
    );

    useEffect(() => {
      if (showModal) {
        activityPoint(4000, 0);
      }
    }, [showModal]);

    return (
      <>
        <DepositZyModel
          showModal={oepnDepsitIfreme}
          info={depositOrderInfo}
          handleCancel={() => setOpenDepsitIfreme(false)}
          rechargeType={3}
        ></DepositZyModel>
        <CommonModal
          width={window.screen.width > 1024 ? "20%" : "90%"}
          footer={null}
          open={showModal}
          onCancel={handleCancel}
          afterClose={handleClose}
          maskClosable={false}
        >
          {rechargeLoading ? (
            <div className="text-primary-text flex flex-col w-full h-[250px]  py-4 px-2 bg-[#1B1E23] relative rounded-[16px] overflow-hidden">
              <ScreenSpin loading={rechargeLoading} />
            </div>
          ) : (
            <div
              className="text-primary-text flex flex-col w-full  py-4 px-2 bg-[#1B1E23] relative rounded-[16px] overflow-hidden "
              style={{ zIndex: 3 }}
            >
              {showHint && <HintContent popupId={showHint} />}
              <span
                className="mb-[14px] ml-5 font-semibold text-[14px]/[20px] relative"
                style={{ zIndex: 10 }}
              >
                {allConfigData?.firstChargePopList?.[0]?.title}
              </span>
              <div className="flex flex-col ">
                <div className="w-full relative h-[116px]">
                  <img
                    src={allConfigData?.firstChargePopList?.[0]?.icon}
                    alt=""
                    className="object-cover absolute"
                    style={{
                      zIndex: -2,
                      top: "50%",
                      left: "50%",
                      transform: "translate(-50%, -50%)"
                    }}
                  />
                </div>
                <span
                  className="mt-[10px] flex items-center justify-center"
                  style={{ zIndex: 10 }}
                  dangerouslySetInnerHTML={{ __html: allConfigData?.firstChargePopList?.[0]?.desc }}
                ></span>
                <div className="mt-[17px] flex items-center justify-around w-full ">
                  <Button
                    className="btn !h-[36px] !w-[148px]"
                    onClick={() =>
                      createMyOrder(allConfigData?.firstChargePopList?.[0]?.amount || 0, allConfigData?.firstChargePopList?.[0]?.currencyId)
                    }
                  >
                    {allConfigData?.firstChargePopList?.[0]?.buttonText}
                  </Button>
                  <Button
                    className="bg-[#25282C] rounded-lg !h-[36px] !w-[148px]"
                    onClick={() => {
                      handleCancel();
                      handleJump(allConfigData?.firstChargePopList?.[0]?.button1, navigate);
                    }}
                  >
                    {allConfigData?.firstChargePopList?.[0]?.buttonText1}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CommonModal>
      </>
    );
  }
);

export default LimitTimeRecharge;
