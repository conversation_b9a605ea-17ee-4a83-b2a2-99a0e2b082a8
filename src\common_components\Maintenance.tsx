import { useQueryConfigData } from "@/hooks/queryHooks/useQueryConfigData";
import { ProtoMessage } from "@/protos/common";
import { setIsLoginOut, setIsMainTanceLogin, setShowLiveSupport } from "@/store";
import TempThreeLoginOrSignUp from "@/templateThree/components/common/login/TempThreeLoginOrSignUp";
import { getIsLogin } from "@/utils/commonUtil";
import { safeTrackClick } from "@/utils/manualTrack";
import { Button, Statistic } from "antd";
import { useEffect, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import CustomerService from "./adminModules/CustomerService";
import LogOut from "./LogOut";

const Maintenance: React.FC = () => {
  const dispatch = useDispatch();
  const [maintenanceInfo, setMaintenanceInfo] = useState<ProtoMessage.MaintainNoticeInfo>();
  const webBulletinInfo = useSelector(
    (state) => state.systemReducer.webBulletinInfo
  ) as ProtoMessage.MaintainNoticeInfo;

  const { data: maintainNoticeInfo } = useQueryConfigData(0, 1000 * 60 * 60 * 24);
  const navigate = useNavigate();
  const location = useLocation()
  useEffect(() => {
    if (webBulletinInfo || maintainNoticeInfo?.maintainNoticeInfo) {
      setMaintenanceInfo(webBulletinInfo || maintainNoticeInfo?.maintainNoticeInfo);
      if (getIsLogin()) {
        dispatch(setIsLoginOut(true));
      }
    } else if (!webBulletinInfo && !maintainNoticeInfo?.maintainNoticeInfo) {
      navigate("/");
    }
  }, [webBulletinInfo, maintainNoticeInfo, location.pathname]);
  const pcLogo = useSelector((state: any) => state.systemReducer.pcLogo);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showLoginType, setShowLoginType] = useState();

  const handleLiveSupport = () => {
    safeTrackClick('LiveSupportEntry', 'button');
    
    if (window.Intercom) {
      window.Intercom("show");
    } else {
      dispatch(setShowLiveSupport());
    }
  };

  return (
    <>
      <CustomerService />
      <LogOut />
      <TempThreeLoginOrSignUp
        showType={1}
        showModal={showLoginModal}
        setShowModal={() => setShowLoginModal(false)}
        setShowType={setShowLoginType}
        toJump={null}
      />
      <div className="w-[100vw] min-h-[100vh] flex items-center flex-col justify-center">
        <img src={pcLogo} alt="" className="h-[102px]" />
        <div
          className="text-[--text-color-title] p-[27px] flex flex-col mt-[31px] lg:bg-[#2a2f38] rounded-[16px]"
          style={{ width: window.screen.width > 1024 ? "50%" : "95%" }}
        >
          <span className="w-full flex justify-center mt-[18px]">
            <svg
              width="232"
              height="176"
              viewBox="0 0 232 176"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M122.627 176C171.131 176 210.453 136.677 210.453 88C210.453 39.3228 170.957 0 122.627 0C74.1226 0 34.7998 39.3228 34.7998 88C34.7998 136.677 74.1226 176 122.627 176Z"
                fill="#373E49"
              />
              <path
                d="M215.91 144.779C219.833 144.779 223.013 141.599 223.013 137.677C223.013 133.754 219.833 130.574 215.91 130.574C211.988 130.574 208.808 133.754 208.808 137.677C208.808 141.599 211.988 144.779 215.91 144.779Z"
                fill="#EAEEF9"
              />
              <path
                d="M226.303 117.062C228.982 117.062 231.153 114.891 231.153 112.212C231.153 109.533 228.982 107.361 226.303 107.361C223.624 107.361 221.453 109.533 221.453 112.212C221.453 114.891 223.624 117.062 226.303 117.062Z"
                fill="#EAEEF9"
              />
              <path
                d="M39.4759 30.1412C42.1547 30.1412 44.3263 27.9696 44.3263 25.2908C44.3263 22.612 42.1547 20.4404 39.4759 20.4404C36.7971 20.4404 34.6255 22.612 34.6255 25.2908C34.6255 27.9696 36.7971 30.1412 39.4759 30.1412Z"
                fill="#EAEEF9"
              />
              <path
                d="M9.85504 123.686C14.83 123.686 18.8629 119.653 18.8629 114.678C18.8629 109.703 14.83 105.67 9.85504 105.67C4.88013 105.67 0.847168 109.703 0.847168 114.678C0.847168 119.653 4.88013 123.686 9.85504 123.686Z"
                fill="#EAEEF9"
              />
              <path
                d="M188.127 90.0759L192.808 88.6649C193.148 88.5664 193.148 88.0742 192.808 87.9758L188.127 86.5648C188.007 86.532 187.919 86.4445 187.886 86.3242L186.475 81.6537C186.377 81.3146 185.885 81.3146 185.786 81.6537L184.375 86.3242C184.343 86.4445 184.255 86.532 184.135 86.5648L179.464 87.9758C179.125 88.0742 179.125 88.5664 179.464 88.6649L184.146 90.0759C184.266 90.1087 184.353 90.1962 184.386 90.3165L185.797 94.9979C185.896 95.337 186.388 95.337 186.486 94.9979L187.897 90.3165C187.919 90.1962 188.007 90.1087 188.127 90.0759Z"
                fill="#ADB6C8"
              />
              <path
                d="M160.41 39.843L165.091 38.4321C165.43 38.3337 165.43 37.8415 165.091 37.7431L160.41 36.3321C160.289 36.2993 160.202 36.2118 160.169 36.0915L158.758 31.4213C158.66 31.0822 158.168 31.0822 158.069 31.4213L156.658 36.0915C156.625 36.2118 156.538 36.2993 156.417 36.3321L151.747 37.7431C151.408 37.8415 151.408 38.3337 151.747 38.4321L156.428 39.843C156.549 39.8758 156.636 39.9633 156.669 40.0836L158.08 44.7648C158.178 45.1039 158.671 45.1039 158.769 44.7648L160.18 40.0836C160.202 39.9633 160.289 39.8758 160.41 39.843Z"
                fill="#ADB6C8"
              />
              <path
                d="M90.0046 45.6862L94.1008 44.4517C94.3975 44.3655 94.3975 43.9349 94.1008 43.8487L90.0046 42.6142C89.8993 42.5855 89.8228 42.5089 89.7941 42.4037L88.5594 38.3172C88.4733 38.0206 88.0426 38.0206 87.9565 38.3172L86.7219 42.4037C86.6932 42.5089 86.6166 42.5855 86.5113 42.6142L82.4247 43.8487C82.128 43.9349 82.128 44.3655 82.4247 44.4517L86.5209 45.6862C86.6262 45.7149 86.7027 45.7915 86.7314 45.8967L87.9661 49.9927C88.0522 50.2894 88.4829 50.2894 88.569 49.9927L89.8036 45.8967C89.8228 45.7915 89.8993 45.7149 90.0046 45.6862Z"
                fill="#ADB6C8"
              />
              <path
                d="M60.5569 120.918L64.6531 119.507C64.9498 119.408 64.9498 118.916 64.6531 118.818L60.5569 117.407C60.4516 117.374 60.375 117.286 60.3463 117.166L59.1117 112.496C59.0256 112.156 58.5949 112.156 58.5087 112.496L57.2741 117.166C57.2454 117.286 57.1688 117.374 57.0636 117.407L52.9769 118.818C52.6802 118.916 52.6802 119.408 52.9769 119.507L57.0731 120.918C57.1784 120.95 57.255 121.038 57.2837 121.158L58.5183 125.84C58.6044 126.179 59.0351 126.179 59.1213 125.84L60.3559 121.158C60.375 121.038 60.4516 120.95 60.5569 120.918Z"
                fill="#ADB6C8"
              />
              <path
                d="M53.998 67.5679L56.9239 66.6861C57.1358 66.6245 57.1358 66.3169 56.9239 66.2554L53.998 65.3736C53.9228 65.3531 53.8681 65.2984 53.8476 65.2232L52.9657 62.3044C52.9042 62.0925 52.5966 62.0925 52.535 62.3044L51.6532 65.2232C51.6327 65.2984 51.578 65.3531 51.5028 65.3736L48.5837 66.2554C48.3718 66.3169 48.3718 66.6245 48.5837 66.6861L51.5096 67.5679C51.5848 67.5884 51.6395 67.6431 51.66 67.7182L52.5419 70.6439C52.6034 70.8558 52.911 70.8558 52.9726 70.6439L53.8544 67.7182C53.8681 67.6431 53.9228 67.5884 53.998 67.5679Z"
                fill="#ADB6C8"
              />
              <path
                d="M85.1787 96.5073L88.1045 95.6255C88.3165 95.564 88.3165 95.2564 88.1045 95.1949L85.1787 94.3131C85.1035 94.2926 85.0488 94.2379 85.0283 94.1627L84.1464 91.2439C84.0849 91.032 83.7772 91.032 83.7157 91.2439L82.8338 94.1627C82.8133 94.2379 82.7586 94.2926 82.6835 94.3131L79.7644 95.1949C79.5525 95.2564 79.5525 95.564 79.7644 95.6255L82.6903 96.5073C82.7655 96.5278 82.8202 96.5825 82.8407 96.6577L83.7225 99.5833C83.7841 99.7953 84.0917 99.7953 84.1532 99.5833L85.0351 96.6577C85.0488 96.5825 85.1035 96.5278 85.1787 96.5073Z"
                fill="#ADB6C8"
              />
              <path
                d="M109.988 14.0659L112.914 13.1841C113.126 13.1226 113.126 12.815 112.914 12.7535L109.988 11.8717C109.913 11.8512 109.858 11.7965 109.837 11.7213L108.955 8.80248C108.894 8.59058 108.586 8.59058 108.525 8.80248L107.643 11.7213C107.622 11.7965 107.568 11.8512 107.493 11.8717L104.573 12.7535C104.362 12.815 104.362 13.1226 104.573 13.1841L107.499 14.0659C107.575 14.0864 107.629 14.1411 107.65 14.2163L108.532 17.1419C108.593 17.3538 108.901 17.3538 108.962 17.1419L109.844 14.2163C109.858 14.1411 109.913 14.0864 109.988 14.0659Z"
                fill="#ADB6C8"
              />
              <path
                d="M179.319 139.399L182.245 138.517C182.457 138.456 182.457 138.148 182.245 138.086L179.319 137.205C179.244 137.184 179.189 137.129 179.169 137.054L178.287 134.135C178.225 133.924 177.918 133.924 177.856 134.135L176.974 137.054C176.954 137.129 176.899 137.184 176.824 137.205L173.905 138.086C173.693 138.148 173.693 138.456 173.905 138.517L176.831 139.399C176.906 139.419 176.961 139.474 176.981 139.549L177.863 142.475C177.925 142.687 178.232 142.687 178.294 142.475L179.176 139.549C179.189 139.474 179.244 139.419 179.319 139.399Z"
                fill="#ADB6C8"
              />
              <path
                d="M91.8935 66.3355C90.7293 67.7602 89.9894 69.4836 89.7583 71.3089C89.6932 71.8101 89.4438 72.2691 89.0587 72.5964C88.6736 72.9238 88.1804 73.096 87.6753 73.0795L86.842 73.0534C87.1921 72.0388 87.7213 71.0952 88.4043 70.2673C88.7217 69.8484 88.8869 69.3339 88.8727 68.8085C88.8585 68.2831 88.6658 67.7782 88.3262 67.3771L83.0925 61.0237C82.7803 60.6509 82.5903 60.1911 82.5484 59.7066C82.5065 59.2222 82.6146 58.7365 82.8581 58.3156C82.9301 58.1921 83.0176 58.0784 83.1185 57.9771L85.9567 55.0088L86.0348 55.4775C86.0194 55.8253 86.0816 56.1722 86.2171 56.493C86.3133 56.707 86.4358 56.9081 86.5816 57.0918L91.7893 63.4452C92.1323 63.8436 92.3294 64.3469 92.3483 64.8721C92.3673 65.3974 92.2068 65.9135 91.8935 66.3355Z"
                fill="white"
              />
              <path
                d="M108.94 50.3483L108.705 44.7761C108.676 44.3486 108.776 43.9222 108.992 43.5523L109.018 43.5262C109.188 43.2445 109.424 43.0076 109.705 42.8355C109.986 42.6634 110.304 42.5609 110.632 42.5367L111.96 42.4586L112.246 42.4326L112.455 47.6663C112.487 48.1824 112.327 48.6921 112.004 49.0964C111.682 49.5007 111.221 49.7705 110.71 49.8535C110.101 49.9385 109.505 50.1049 108.94 50.3483Z"
                fill="white"
              />
              <path
                d="M118.685 133.359L121.679 134.114L121.523 134.531L120.716 137.109C120.652 137.33 120.556 137.541 120.429 137.733L119.883 138.228C119.628 138.395 119.339 138.503 119.037 138.544C118.735 138.584 118.428 138.557 118.138 138.463L107.696 135.234C107.207 135.08 106.782 134.77 106.485 134.352C106.189 133.933 106.038 133.429 106.056 132.916L106.186 124.87C106.194 124.349 106.03 123.84 105.719 123.421C105.409 123.002 104.969 122.697 104.468 122.553C102.521 121.968 100.825 120.749 99.6506 119.09C99.3451 118.668 98.8964 118.372 98.3884 118.257C97.8804 118.142 97.348 118.216 96.8905 118.465L90.9538 121.746C90.4828 121.994 89.9376 122.062 89.42 121.938C88.9024 121.813 88.448 121.504 88.1416 121.069L81.4497 112.007C81.1862 111.652 81.025 111.231 80.9837 110.79C80.9424 110.35 81.0225 109.906 81.2154 109.508H81.2414C81.2423 109.459 81.2608 109.413 81.2935 109.378L81.4237 109.221C81.4784 109.124 81.5487 109.036 81.632 108.961L84.366 106.383L84.5223 106.253C84.4487 106.453 84.4397 106.672 84.4962 106.878L84.6004 107.399C84.6651 107.645 84.7804 107.876 84.9389 108.076L91.6308 117.137C91.9347 117.575 92.3875 117.888 92.9047 118.017C93.422 118.146 93.9686 118.084 94.4429 117.84L100.38 114.533C100.834 114.29 101.361 114.22 101.863 114.335C102.366 114.449 102.81 114.742 103.114 115.158C104.303 116.814 106.005 118.032 107.957 118.621C108.46 118.771 108.9 119.081 109.21 119.504C109.52 119.927 109.684 120.44 109.675 120.965L109.519 128.985C109.514 129.501 109.673 130.006 109.972 130.427C110.271 130.848 110.696 131.163 111.186 131.328L118.763 133.671L118.685 133.359Z"
                fill="white"
              />
              <path
                d="M139.621 51.0772C139.439 51.3077 139.208 51.4946 138.944 51.6241L140.663 49.5409C140.845 49.3103 141.076 49.1235 141.34 48.9941L139.621 51.0772Z"
                fill="white"
              />
              <path
                d="M150.534 133.097C150.488 133.14 150.435 133.176 150.377 133.201C150.282 133.302 150.167 133.382 150.039 133.435L141.29 137.966C140.799 138.194 140.242 138.239 139.721 138.09C139.2 137.942 138.749 137.612 138.452 137.159L133.478 129.712C133.223 129.326 132.848 129.033 132.411 128.878C131.974 128.723 131.499 128.714 131.057 128.853C129.236 129.408 127.315 129.55 125.433 129.269L126.24 126.718C126.377 126.248 126.679 125.845 127.091 125.581C127.503 125.318 127.996 125.213 128.479 125.285C130.501 125.663 132.584 125.537 134.546 124.921C134.984 124.782 135.456 124.791 135.889 124.947C136.321 125.102 136.691 125.395 136.942 125.78L141.941 133.227C142.233 133.686 142.684 134.021 143.207 134.17C143.73 134.319 144.289 134.271 144.779 134.034L149.023 131.821L150.638 130.988L151.054 130.78C151.064 130.79 151.072 130.802 151.076 130.816C151.081 130.829 151.082 130.844 151.08 130.858C151.201 131.245 151.214 131.659 151.118 132.053C151.021 132.448 150.82 132.808 150.534 133.097Z"
                fill="white"
              />
              <path
                d="M165.058 114.663L166.699 114.923L166.62 115.157L165.605 117.813C165.467 118.195 165.212 118.523 164.876 118.751V118.777C164.606 118.965 164.297 119.089 163.972 119.139C163.647 119.188 163.315 119.162 163.001 119.063L155.164 116.511C154.722 116.358 154.238 116.376 153.81 116.563C154.746 115.681 155.545 114.663 156.179 113.543C156.421 113.133 156.795 112.817 157.239 112.647C157.684 112.477 158.173 112.462 158.627 112.605L165.058 114.663Z"
                fill="white"
              />
              <path
                d="M169.721 87.5547V87.5807C169.537 87.7716 169.315 87.9222 169.07 88.0233L164.696 89.8461C164.67 89.6378 164.643 89.4034 164.617 89.1951C164.536 88.7247 164.615 88.2406 164.842 87.8205C165.069 87.4003 165.43 87.0684 165.867 86.8777L168.888 85.6278L170.138 85.0811L170.19 85.2112C170.339 85.6065 170.375 86.0358 170.292 86.4502C170.209 86.8646 170.011 87.2474 169.721 87.5547Z"
                fill="white"
              />
              <path
                d="M123.922 134.683L120.433 137.73L119.886 138.225L118.767 133.668L118.688 133.355L121.683 134.111L123.063 134.475L123.922 134.683Z"
                fill="white"
              />
              <path
                d="M154.461 129.636L150.529 133.099L150.399 133.229L150.373 133.203L149.019 131.823L150.633 130.99L151.076 130.86L154.461 129.636Z"
                fill="white"
              />
              <path
                d="M164.878 118.776V118.75L163.94 114.506L165.06 114.662L166.701 114.922L167.872 115.079L168.862 115.235L164.878 118.776Z"
                fill="white"
              />
              <path
                d="M85.2435 110.365L81.2336 109.506H81.2075L81.2856 109.376L81.4158 109.22L84.1239 105.262L84.3582 106.381L84.4884 106.876L84.5925 107.397L85.2435 110.365Z"
                fill="white"
              />
              <path
                d="M74.6318 77.7899L77.4492 73.7461L77.3554 79.183L74.6318 77.7899Z"
                fill="white"
              />
              <path
                d="M86.891 60.0602L82.855 58.3156L85.7713 54.0713L85.9536 55.0087L86.0317 55.4774C86.0162 55.8252 86.0785 56.1722 86.2139 56.4929L86.891 60.0602Z"
                fill="white"
              />
              <path
                d="M109.01 43.5503L111.838 39.457L112.114 45.9926L109.01 43.5503Z"
                fill="white"
              />
              <path
                d="M169.722 87.5819V87.5559L168.889 85.629L170.139 85.0822L171.024 84.7177L173.628 84.1709L169.722 87.5819Z"
                fill="white"
              />
              <path
                d="M159.097 70.5022C159.318 70.6802 159.573 70.8112 159.846 70.8874C160.119 70.9635 160.405 70.983 160.686 70.9448L167.56 69.8772C168.085 69.8168 168.615 69.9356 169.064 70.2145C169.513 70.4934 169.855 70.9159 170.033 71.4135L173.679 81.2821C173.89 81.8078 173.889 82.3953 173.675 82.9201C173.461 83.445 173.052 83.8661 172.533 84.0943L165.867 86.8803C165.43 87.071 165.069 87.403 164.842 87.8232C164.615 88.2433 164.536 88.7274 164.617 89.1978C164.922 91.3057 164.771 93.4545 164.175 95.4991C164.057 95.9707 164.09 96.4672 164.267 96.9197C164.444 97.3722 164.758 97.7584 165.164 98.0248L171.518 102.321C171.96 102.622 172.29 103.062 172.455 103.571C172.62 104.08 172.611 104.63 172.429 105.133L169.07 113.908C168.883 114.406 168.509 114.813 168.028 115.041C167.547 115.269 166.997 115.302 166.492 115.132L158.629 112.606C158.175 112.463 157.686 112.478 157.241 112.648C156.797 112.818 156.423 113.134 156.181 113.544C155.3 115.133 154.071 116.504 152.588 117.554C152.181 117.846 151.887 118.27 151.754 118.753C151.621 119.236 151.658 119.75 151.859 120.21L154.488 126.693C154.711 127.197 154.736 127.767 154.557 128.288C154.378 128.809 154.01 129.244 153.525 129.505L144.776 134.036C144.286 134.272 143.727 134.321 143.204 134.172C142.68 134.023 142.23 133.688 141.938 133.229L136.938 125.782C136.688 125.396 136.318 125.104 135.886 124.948C135.453 124.793 134.981 124.784 134.543 124.923C132.581 125.539 130.498 125.664 128.476 125.287C127.993 125.215 127.499 125.32 127.088 125.583C126.676 125.846 126.374 126.25 126.237 126.719L124.206 133.177C124.128 133.44 123.999 133.685 123.825 133.898C123.651 134.111 123.437 134.287 123.194 134.415C122.952 134.544 122.686 134.622 122.412 134.647C122.139 134.671 121.863 134.64 121.602 134.557L111.186 131.328C110.697 131.163 110.272 130.848 109.973 130.427C109.673 130.006 109.515 129.501 109.52 128.985L109.676 120.965C109.684 120.44 109.521 119.927 109.211 119.504C108.9 119.081 108.46 118.771 107.958 118.621C106.006 118.032 104.303 116.815 103.115 115.158C102.811 114.742 102.367 114.45 101.864 114.335C101.362 114.22 100.835 114.29 100.38 114.533L94.4437 117.84C93.9693 118.084 93.4227 118.146 92.9055 118.017C92.3882 117.888 91.9355 117.575 91.6315 117.137L84.9397 108.076C84.5983 107.636 84.4286 107.087 84.4622 106.531C84.4957 105.975 84.7302 105.451 85.1219 105.055L90.3817 100.082C90.745 99.736 90.9793 99.2763 91.0456 98.7791C91.1119 98.2818 91.0062 97.7769 90.7462 97.3479C89.6417 95.5071 88.924 93.4604 88.6371 91.333C88.5886 90.8419 88.3863 90.3786 88.059 90.0092C87.7317 89.6398 87.2961 89.3832 86.8144 89.2759L79.5497 87.974C78.9995 87.8623 78.5056 87.5616 78.1537 87.1241C77.8019 86.6866 77.6141 86.1398 77.6229 85.5784L77.8051 74.9548C77.8015 74.6746 77.8545 74.3965 77.9609 74.1373C78.0672 73.8781 78.2248 73.643 78.4241 73.4461C78.6235 73.2492 78.8605 73.0945 79.121 72.9913C79.3815 72.8881 79.6602 72.8386 79.9403 72.8456L87.6737 73.08C88.1789 73.0965 88.6721 72.9243 89.0572 72.5969C89.4423 72.2696 89.6917 71.8106 89.7568 71.3094C89.9878 69.4841 90.7277 67.7607 91.8919 66.336C92.2053 65.914 92.3657 65.3979 92.3468 64.8726C92.3278 64.3473 92.1307 63.8441 91.7878 63.4457L86.5801 57.0923C86.2206 56.6647 86.0257 56.1229 86.0305 55.5643C86.0353 55.0057 86.2394 54.4672 86.6061 54.0459L93.298 47.0154C93.672 46.6384 94.1742 46.4158 94.7047 46.3917C95.2353 46.3675 95.7555 46.5437 96.1622 46.8853L103.167 52.4575C103.555 52.7869 104.052 52.9611 104.562 52.9467C105.071 52.9323 105.557 52.7303 105.927 52.3794C107.201 51.0314 108.886 50.1433 110.718 49.8536C111.228 49.7706 111.689 49.5008 112.012 49.0965C112.334 48.6922 112.495 48.1825 112.462 47.6664L112.202 40.8704C112.179 40.5963 112.21 40.3202 112.295 40.0584C112.379 39.7966 112.515 39.5543 112.695 39.3457C112.874 39.1372 113.093 38.9666 113.34 38.8439C113.586 38.7211 113.854 38.6488 114.129 38.6311L124.622 37.928C125.155 37.8997 125.681 38.0586 126.109 38.3772C126.537 38.6958 126.84 39.154 126.966 39.6726L129.153 47.7445C129.26 48.21 129.508 48.6316 129.861 48.9527C130.215 49.2738 130.658 49.4791 131.132 49.5412C133.215 49.7411 135.222 50.4279 136.991 51.5462C137.411 51.802 137.91 51.8955 138.394 51.8092C138.878 51.7229 139.315 51.4627 139.62 51.0774L144.151 45.6354C144.487 45.2308 144.964 44.9684 145.486 44.9008C146.007 44.8332 146.535 44.9654 146.963 45.2709L155.973 51.468C156.425 51.7728 156.759 52.2231 156.92 52.7441C157.081 53.2651 157.059 53.8255 156.858 54.3322L156.051 56.2591"
                fill="white"
              />
              <path
                d="M159.097 70.5022C159.318 70.6802 159.573 70.8112 159.846 70.8874C160.119 70.9635 160.405 70.983 160.686 70.9448L167.56 69.8772C168.085 69.8168 168.615 69.9356 169.064 70.2145C169.513 70.4934 169.855 70.9159 170.033 71.4135L173.679 81.2821C173.89 81.8078 173.889 82.3953 173.675 82.9201C173.461 83.445 173.052 83.8661 172.533 84.0943L165.867 86.8803C165.43 87.071 165.069 87.403 164.842 87.8232C164.615 88.2433 164.536 88.7274 164.617 89.1978C164.922 91.3057 164.771 93.4545 164.175 95.4991C164.057 95.9707 164.09 96.4672 164.267 96.9197C164.444 97.3722 164.758 97.7584 165.164 98.0248L171.518 102.321C171.96 102.622 172.29 103.062 172.455 103.571C172.62 104.08 172.611 104.63 172.429 105.133L169.07 113.908C168.883 114.406 168.509 114.813 168.028 115.041C167.547 115.269 166.997 115.302 166.492 115.132L158.629 112.606C158.175 112.463 157.686 112.478 157.241 112.648C156.797 112.818 156.423 113.134 156.181 113.544C155.3 115.133 154.071 116.504 152.588 117.554C152.181 117.846 151.887 118.27 151.754 118.753C151.621 119.236 151.658 119.75 151.859 120.21L154.488 126.693C154.711 127.197 154.736 127.767 154.557 128.288C154.378 128.809 154.01 129.244 153.525 129.505L144.776 134.036C144.286 134.272 143.727 134.321 143.204 134.172C142.68 134.023 142.23 133.688 141.938 133.229L136.938 125.782C136.688 125.396 136.318 125.104 135.886 124.948C135.453 124.793 134.981 124.784 134.543 124.923C132.581 125.539 130.498 125.664 128.476 125.287C127.993 125.215 127.499 125.32 127.088 125.583C126.676 125.846 126.374 126.25 126.237 126.719L124.206 133.177C124.128 133.44 123.999 133.685 123.825 133.898C123.651 134.111 123.437 134.287 123.194 134.415C122.952 134.544 122.686 134.622 122.412 134.647C122.139 134.671 121.863 134.64 121.602 134.557L111.186 131.328C110.697 131.163 110.272 130.848 109.973 130.427C109.673 130.006 109.515 129.501 109.52 128.985L109.676 120.965C109.684 120.44 109.521 119.927 109.211 119.504C108.9 119.081 108.46 118.771 107.958 118.621C106.006 118.032 104.303 116.815 103.115 115.158C102.811 114.742 102.367 114.45 101.864 114.335C101.362 114.22 100.835 114.29 100.38 114.533L94.4437 117.84C93.9693 118.084 93.4227 118.146 92.9055 118.017C92.3882 117.888 91.9355 117.575 91.6315 117.137L84.9397 108.076C84.5983 107.636 84.4286 107.087 84.4622 106.531C84.4957 105.975 84.7302 105.451 85.1219 105.055L90.3817 100.082C90.745 99.736 90.9793 99.2763 91.0456 98.7791C91.1119 98.2818 91.0062 97.7769 90.7462 97.3479C89.6417 95.5071 88.924 93.4604 88.6371 91.333C88.5886 90.8419 88.3863 90.3786 88.059 90.0092C87.7317 89.6398 87.2961 89.3832 86.8144 89.2759L79.5497 87.974C78.9995 87.8623 78.5056 87.5616 78.1537 87.1241C77.8019 86.6866 77.6141 86.1398 77.6229 85.5784L77.8051 74.9548C77.8015 74.6746 77.8545 74.3965 77.9609 74.1373C78.0672 73.8781 78.2248 73.643 78.4241 73.4461C78.6235 73.2492 78.8605 73.0945 79.121 72.9913C79.3815 72.8881 79.6602 72.8386 79.9403 72.8456L87.6737 73.08C88.1789 73.0965 88.6721 72.9243 89.0572 72.5969C89.4423 72.2696 89.6917 71.8106 89.7568 71.3094C89.9878 69.4841 90.7277 67.7607 91.8919 66.336C92.2053 65.914 92.3657 65.3979 92.3468 64.8726C92.3278 64.3473 92.1307 63.8441 91.7878 63.4457L86.5801 57.0923C86.2206 56.6647 86.0257 56.1229 86.0305 55.5643C86.0353 55.0057 86.2394 54.4672 86.6061 54.0459L93.298 47.0154C93.672 46.6384 94.1742 46.4158 94.7047 46.3917C95.2353 46.3675 95.7555 46.5437 96.1622 46.8853L103.167 52.4575C103.555 52.7869 104.052 52.9611 104.562 52.9467C105.071 52.9323 105.557 52.7303 105.927 52.3794C107.201 51.0314 108.886 50.1433 110.718 49.8536C111.228 49.7706 111.689 49.5008 112.012 49.0965C112.334 48.6922 112.495 48.1825 112.462 47.6664L112.202 40.8704C112.179 40.5963 112.21 40.3202 112.295 40.0584C112.379 39.7966 112.515 39.5543 112.695 39.3457C112.874 39.1372 113.093 38.9666 113.34 38.8439C113.586 38.7211 113.854 38.6488 114.129 38.6311L124.622 37.928C125.155 37.8997 125.681 38.0586 126.109 38.3772C126.537 38.6958 126.84 39.154 126.966 39.6726L129.153 47.7445C129.26 48.21 129.508 48.6316 129.861 48.9527C130.215 49.2738 130.658 49.4791 131.132 49.5412C133.215 49.7411 135.222 50.4279 136.991 51.5462C137.411 51.802 137.91 51.8955 138.394 51.8092C138.878 51.7229 139.315 51.4627 139.62 51.0774L144.151 45.6354C144.487 45.2308 144.964 44.9684 145.486 44.9008C146.007 44.8332 146.535 44.9654 146.963 45.2709L155.973 51.468C156.425 51.7728 156.759 52.2231 156.92 52.7441C157.081 53.2651 157.059 53.8255 156.858 54.3322L156.051 56.2591L156.155 57.0897L159.097 70.5022Z"
                stroke="#9FA8BA"
                strokeWidth="1.30192"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M123.063 134.479C122.601 134.675 122.085 134.703 121.605 134.557L121.527 134.531L118.767 133.672L118.688 133.359L121.683 134.115L123.063 134.479Z"
                fill="white"
                stroke="#9FA8BA"
                strokeWidth="1.30192"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M167.871 115.081C167.476 115.25 167.035 115.278 166.621 115.159C166.576 115.163 166.531 115.154 166.491 115.133L165.059 114.664L166.699 114.924L167.871 115.081Z"
                fill="white"
                stroke="black"
                strokeWidth="1.30192"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M142.54 65.6065C147.041 70.1087 153.814 79.2222 153.814 85.1093C154.974 91.4299 153.848 97.9568 150.637 103.524C147.427 109.09 142.342 113.334 136.29 115.496H136.264C134.489 116.077 132.655 116.461 130.796 116.642C115.642 118.256 101.347 106.695 98.9251 90.8898C98.0344 85.8242 98.5794 80.6104 100.498 75.8384C102.417 71.0664 105.633 66.9268 109.783 63.8879C109.816 63.8721 109.851 63.8632 109.887 63.8619C113.45 61.3407 117.602 59.7803 121.943 59.3312C129.367 58.6283 136.769 60.8833 142.54 65.6065Z"
                fill="white"
                stroke="#9FA8BA"
                strokeWidth="1.30192"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M135.172 70.4259C138.904 72.5868 141.362 76.333 143.347 80.1618C144.496 82.3785 145.516 84.6315 145.715 85.9694C146.534 90.4215 145.742 95.0198 143.481 98.9414C141.219 102.863 137.636 105.852 133.373 107.373H133.347C132.097 107.789 130.804 108.06 129.493 108.18C124.145 108.567 118.849 106.907 114.678 103.538C110.506 100.169 107.77 95.3413 107.022 90.0314C106.399 86.4606 106.785 82.7867 108.138 79.4237C109.49 76.0607 111.755 73.1422 114.677 70.9972H114.755C117.263 69.2185 120.186 68.1155 123.244 67.7946C126.394 67.5085 129.567 67.9675 132.508 69.1345C133.429 69.5002 134.319 69.9322 135.172 70.4259Z"
                fill="white"
                stroke="#9FA8BA"
                strokeWidth="1.30192"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M100.305 110.492C98.513 111.884 96.6354 113.161 94.6833 114.317C94.2288 114.662 93.6603 114.822 93.0924 114.765C92.6489 114.608 92.2662 114.316 91.9988 113.929C90.187 111.792 88.5574 109.506 87.127 107.097C89.0713 108.273 91.294 108.91 93.5663 108.943L90.939 109.867C93.8717 111.245 97.2154 111.468 100.305 110.492Z"
                fill="white"
              />
              <path
                d="M151.073 126.945L145.818 130.195C145.545 130.485 145.2 130.698 144.817 130.812C144.435 130.926 144.03 130.936 143.642 130.842C143.255 130.748 142.899 130.552 142.612 130.276C142.324 129.999 142.115 129.652 142.006 129.268L138.66 123.784C140.302 125.094 142.275 125.922 144.36 126.177L142.516 126.698C145.273 127.672 148.265 127.759 151.073 126.945Z"
                fill="white"
              />
              <path
                d="M122.801 49.5303C123.769 49.5303 124.554 48.987 124.554 48.3169C124.554 47.6468 123.769 47.1035 122.801 47.1035C121.833 47.1035 121.049 47.6468 121.049 48.3169C121.049 48.987 121.833 49.5303 122.801 49.5303Z"
                fill="white"
              />
              <path
                d="M128.288 52.8803C129.202 52.8803 129.944 52.4058 129.944 51.8205C129.944 51.2352 129.202 50.7607 128.288 50.7607C127.373 50.7607 126.632 51.2352 126.632 51.8205C126.632 52.4058 127.373 52.8803 128.288 52.8803Z"
                fill="white"
              />
              <path
                d="M122.145 53.6887C122.75 53.6887 123.241 53.4217 123.241 53.0924C123.241 52.7631 122.75 52.4961 122.145 52.4961C121.54 52.4961 121.049 52.7631 121.049 53.0924C121.049 53.4217 121.54 53.6887 122.145 53.6887Z"
                fill="white"
              />
              <path
                d="M106.35 102.438C103.527 98.5787 101.772 94.0437 101.259 89.2898C100.747 84.5359 101.496 79.7308 103.431 75.3584C102.225 84.0332 103.151 92.8712 106.128 101.108"
                fill="white"
              />
              <path
                d="M150.242 94.0713C150.886 94.0713 151.408 93.5491 151.408 92.9048C151.408 92.2606 150.886 91.7383 150.242 91.7383C149.597 91.7383 149.075 92.2606 149.075 92.9048C149.075 93.5491 149.597 94.0713 150.242 94.0713Z"
                fill="white"
              />
              <path
                d="M149.658 97.2886C149.981 97.2886 150.242 97.0275 150.242 96.7053C150.242 96.3832 149.981 96.1221 149.658 96.1221C149.336 96.1221 149.075 96.3832 149.075 96.7053C149.075 97.0275 149.336 97.2886 149.658 97.2886Z"
                fill="white"
              />
              <path
                d="M90.7469 97.3479C89.6423 95.5072 88.9247 93.4605 88.6378 91.3331C88.5893 90.8419 88.3869 90.3786 88.0596 90.0093C87.7323 89.6399 87.2968 89.3833 86.8151 89.276L79.5503 87.9741C79.0001 87.8623 78.5063 87.5617 78.1544 87.1242C77.8025 86.6867 77.6147 86.1399 77.6235 85.5785L77.7797 76.8296L77.3891 76.8035L76.4518 76.7775C76.1384 76.7671 75.827 76.8299 75.5423 76.961C75.2575 77.0921 75.0072 77.2878 74.8114 77.5326L74.6291 77.793C74.4287 78.1229 74.3207 78.5007 74.3166 78.8867L74.1343 89.4843C74.1177 90.0476 74.3027 90.5983 74.6559 91.0374C75.0091 91.4766 75.5074 91.7753 76.0612 91.8798L83.3259 93.2078C83.8125 93.3051 84.2538 93.5587 84.5829 93.9301C84.9119 94.3014 85.1106 94.7701 85.1486 95.2648C85.4433 97.3822 86.1605 99.4189 87.2577 101.254C87.5355 101.715 87.6285 102.263 87.5181 102.79L90.3823 100.082C90.7457 99.736 90.9799 99.2764 91.0462 98.7791C91.1125 98.2819 91.0069 97.7769 90.7469 97.3479ZM86.9896 97.9337C86.8828 97.9546 86.7604 97.7645 86.7135 97.5119C86.6667 97.2594 86.7135 97.0381 86.8229 97.0198C86.9322 97.0016 87.052 97.189 87.0963 97.4416C87.1406 97.6942 87.0963 97.9155 86.9896 97.9337ZM87.3463 96.0147C87.1432 96.0512 86.9036 95.6866 86.8255 95.1997C86.7474 94.7128 86.8255 94.2884 87.0338 94.2493C87.2421 94.2103 87.4739 94.5774 87.5546 95.0643C87.6353 95.5512 87.5494 95.9756 87.3463 96.0147ZM88.9867 97.577C88.8435 97.6031 88.6794 97.3505 88.6195 97.0146C88.5597 96.6787 88.6196 96.3845 88.7628 96.3585C88.906 96.3324 89.0674 96.585 89.1299 96.9209C89.1924 97.2568 89.1273 97.5562 88.9867 97.5823V97.577Z"
                fill="white"
              />
              <path
                d="M137.827 86.8012C137.571 85.1523 136.996 83.5691 136.134 82.1404L132.385 84.2495C132.302 84.2821 132.21 84.2872 132.124 84.2639C132.038 84.2406 131.961 84.1902 131.906 84.1202C131.85 84.0502 131.819 83.9642 131.816 83.875C131.813 83.7858 131.839 83.698 131.89 83.6245L134.858 80.3437C133.615 78.839 132.024 77.6592 130.223 76.9062C128.422 76.1532 126.465 75.8495 124.521 76.0213C122.711 76.2137 120.98 76.8682 119.496 77.9221H119.444C117.71 79.1902 116.367 80.9187 115.566 82.9116C114.765 84.9044 114.539 87.0819 114.913 89.1968C115.351 92.3434 116.97 95.2055 119.441 97.2026C121.912 99.1996 125.05 100.182 128.218 99.9507C128.988 99.8777 129.748 99.7204 130.484 99.482H130.51C133.033 98.5759 135.153 96.8045 136.492 94.4826C137.832 92.1607 138.305 89.4389 137.827 86.8012ZM116.111 88.4677C115.932 86.5315 116.236 84.5812 116.996 82.7914C116.649 84.7829 116.666 86.821 117.048 88.8062C117.23 88.5198 117.386 88.2073 117.569 87.8949C117.217 89.8938 117.396 91.9501 118.09 93.8577C116.995 92.2519 116.315 90.4002 116.111 88.4677ZM133.27 92.8942C133.213 92.9728 133.139 93.0372 133.053 93.0823C132.967 93.1275 132.872 93.1522 132.775 93.1546C132.627 93.1517 132.482 93.1066 132.359 93.0245L126.812 88.7021L123.115 93.9358C123.06 94.0246 122.984 94.0976 122.892 94.1478C122.801 94.198 122.698 94.2237 122.594 94.2222C122.462 94.2187 122.334 94.1731 122.23 94.0921C122.157 94.0442 122.096 93.982 122.049 93.9094C122.001 93.8369 121.969 93.7555 121.955 93.6701C121.94 93.5848 121.943 93.4973 121.964 93.4131C121.984 93.329 122.021 93.2499 122.073 93.1807L125.797 87.9209L120.615 83.911C120.547 83.8565 120.489 83.789 120.447 83.7123C120.404 83.6355 120.377 83.5512 120.368 83.464C120.358 83.3769 120.366 83.2887 120.39 83.2045C120.415 83.1203 120.456 83.0418 120.511 82.9736C120.623 82.8455 120.778 82.7638 120.947 82.7446C121.115 82.7253 121.285 82.7699 121.422 82.8695L126.552 86.8534L129.963 82.0102C130.013 81.9401 130.076 81.8804 130.148 81.8346C130.221 81.7888 130.302 81.7578 130.386 81.7433C130.471 81.7288 130.558 81.7311 130.641 81.7501C130.725 81.7691 130.804 81.8044 130.874 81.8539C130.945 81.9035 131.004 81.9665 131.05 82.0391C131.096 82.1117 131.127 82.1926 131.141 82.2772C131.156 82.3619 131.153 82.4486 131.134 82.5323C131.116 82.6161 131.08 82.6952 131.031 82.7653L127.568 87.6605L133.166 91.9829C133.234 92.0346 133.292 92.0996 133.334 92.1741C133.377 92.2486 133.404 92.331 133.413 92.4163C133.423 92.5015 133.415 92.5878 133.391 92.67C133.366 92.7522 133.325 92.8284 133.27 92.8942Z"
                fill="white"
              />
              <path
                d="M175.774 35.2798C176.06 35.2798 176.292 35.0152 176.292 34.6887C176.292 34.3623 176.06 34.0977 175.774 34.0977C175.488 34.0977 175.256 34.3623 175.256 34.6887C175.256 35.0152 175.488 35.2798 175.774 35.2798Z"
                fill="white"
              />
              <path
                d="M177.557 33.4726C177.724 33.4726 177.859 33.2942 177.859 33.0742C177.859 32.8541 177.724 32.6758 177.557 32.6758C177.39 32.6758 177.255 32.8541 177.255 33.0742C177.255 33.2942 177.39 33.4726 177.557 33.4726Z"
                fill="white"
              />
              <path
                d="M177.922 35.8791C178.125 35.8791 178.289 35.6366 178.289 35.3375C178.289 35.0384 178.125 34.7959 177.922 34.7959C177.72 34.7959 177.555 35.0384 177.555 35.3375C177.555 35.6366 177.72 35.8791 177.922 35.8791Z"
                fill="white"
              />
            </svg>
          </span>
          <div className="mt-[37px]">
            <div className="text-base">
              <FormattedMessage id="site_upgrading" />:
            </div>
            <div className="py-[34px] flex items-center justify-center">
              <Statistic.Timer type="countdown"
                value={parseFloat(maintenanceInfo?.endTime) || 0}
                valueStyle={{
                  fontSize: "32px",
                  fontWeight: 600,
                  fontFamily: "Candal",
                  color: "#47E852"
                }}
              />
            </div>
            <div className="text-[--text-color-subtitle] w-full flex flex-col items-center">
              <FormattedMessage id="we_sincere_apologize" />
            </div>
            <div
              className="mt-[21px] bg-[#373E49] rounded-[10px] min-h-[114px] p-3"
              dangerouslySetInnerHTML={{ __html: maintenanceInfo?.info }}
            ></div>
            {maintainNoticeInfo && (
              <Button
                onClick={() => handleLiveSupport()}
                className="btn mt-[24px] w-full !text-base !font-medium !h-[48px]"
              >
                <FormattedMessage id="live_support" />
              </Button>
            )}
            <span
              className="text-primary-text w-full flex items-center justify-center mt-2"
              onClick={() => {
                dispatch(setIsMainTanceLogin(true));
                setShowLoginModal(true);
              }}
            >
              <FormattedMessage id="maintenance_entrance" />
            </span>
          </div>
        </div>
      </div>
    </>
  );
};

export default Maintenance;
