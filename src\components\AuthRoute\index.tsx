import { useQueryConfigData } from "@/hooks/queryHooks/useQueryConfigData";
import { ProtoMessage } from "@/protos/common";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import HistoryUrl from "./HistoryUrl";

const AuthRoute = ({ children }) => {
  const webBulletinInfo = useSelector(
    (state) => state.systemReducer.webBulletinInfo
  ) as ProtoMessage.MaintainNoticeInfo;
  const { data: maintainNoticeInfo } = useQueryConfigData(0, 1000 * 60 * 60 * 24);
  const nav = useNavigate();
  
  useEffect(() => {
    const canJumpInMain = localStorage.getItem("canJumpInMain")
    if (
      !canJumpInMain &&
      (webBulletinInfo?.endTime ||
        maintainNoticeInfo?.maintainNoticeInfo?.endTime)
    ) {
      nav("/maintenance");
    }
  }, [webBulletinInfo, maintainNoticeInfo]);

  return (
    <>
      <HistoryUrl />
      {children}
    </>
  );
};

export default AuthRoute;
