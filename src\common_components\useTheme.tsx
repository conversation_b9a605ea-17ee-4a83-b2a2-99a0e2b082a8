import { getAllTheme } from '@/utils/commonUtil';
import EventBus from '@/utils/EventBus';
import { useEffect, useState } from 'react';

const useTheme = (locale: string) => {
    const allThemes = getAllTheme()
    const localThemeName = localStorage.getItem('theme');
    const [themeName, setThemeName] = useState(localThemeName || "dark")
    const [theme, setTheme] = useState(allThemes[themeName].componentTheme)

    useEffect(() => {
        EventBus.on('themeChange', changeTheme);

        // 返回一个清理函数，当组件卸载时执行
        return () => {
            EventBus.off('themeChange', changeTheme);
        };
    }, [themeName])

    const changeTheme = (newTheme) => {
        setThemeName(newTheme);
        setTheme(allThemes[newTheme].componentTheme)
    };

    return { theme }
}

export default useTheme;