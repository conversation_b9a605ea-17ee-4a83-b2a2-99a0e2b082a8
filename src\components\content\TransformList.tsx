import betsIcon from "@/assets/content/bets.svg";
import avatar4 from "@/assets/content/profile-avatar4.webp";
import SoltsBanner from "@/assets/content/slotsBanner.svg";
import { useEffect, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
import "../../css/ListJb.css";
import { useScreenWidth } from "../../utils/MobileDetectionContext";

const TransformList: React.FC = ({ showCount = 10 }) => {
  const scrollList = useRef();
  const buttons = ["all_beats", "High Rollers", "Lucky Bets", "my_beats"];
  const [selected, setSelected] = useState(0);
  const hight = showCount * 40 + (showCount - 1) * 8;
  const baseTop = 48;
  const [datas, setDatas] = useState([]);
  const [isUpdate, setIsUpdate] = useState(0);
  const { screenWidth } = useScreenWidth();
  const timeOut = useRef(null);

  function handlerClick(index: number) {
    setSelected(index);
  }

  useEffect(() => {
    // 初始化datas
    const initialData = Array.from({ length: showCount }, (_, index) => getData(index));
    setDatas(() => initialData);
  }, [showCount]);

  useEffect(() => {
    if (timeOut.current) {
      clearTimeout(timeOut.current);
    }
    timeOut.current = setTimeout(() => {
      addClass();
    }, 500);
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, [isUpdate]);

  function getData(index, overflow = false) {
    const top = overflow ? -baseTop + "px" : "";
    const post = overflow ? "absolute" : "relative";
    const key = getRandomInt(1, 100000);
    const data = (
      <div className="flex w-full h-[40px]" key={key} style={{ top: top, position: post }}>
        <div
          className={`flex items-center  bg-[--main-div-checked-bg-color] w-[50%] sm:w-[16%]`}
          title={key + ""}
        >
          <img src={SoltsBanner} className="h-[40px] w-[40px]" alt=""></img>
          <span className="text-[#E9EAEC] text-lg truncate">{key}</span>
        </div>
        {screenWidth > 1024 && (
          <>
            <div
              className={`flex items-center  bg-[--main-div-checked-bg-color] w-[17%]`}
              title={key + ""}
            >
              <img src={avatar4} className="h-[18px] w-[18px]" alt="" />
              <span className="text-[#E9EAEC] text-lg ml-1 truncate">{key}</span>
            </div>
            <div
              className={`flex items-center  bg-[--main-div-checked-bg-color] w-[17%] truncate`}
              title={key + ""}
            >
              <span className="text-[#8EA3C3] ml-1 truncate">{key}</span>
            </div>
            <div
              className={`flex items-center  bg-[--main-div-checked-bg-color] w-[16%] truncate`}
              title={key + ""}
            >
              <img
                alt=""
                src="https://pks01.s3.sa-east-1.amazonaws.com/images/usdt_20230810040553.webp"
                className="h-[18px] w-[18px]"
              ></img>
              <span className="text-[#8EA3C3] ml-1 truncate">{key}</span>
            </div>
          </>
        )}

        <div
          className={`flex items-center  bg-[--main-div-checked-bg-color] w-[20%] sm:w-[16%] truncate`}
          title={key + ""}
        >
          <span className="text-[#8EA3C3] ml-1 truncate">
            {key}sdfasfasdfasfasxzcvasdfasdfgasdfasf
          </span>
        </div>
        <div
          className={`flex items-center  bg-[--main-div-checked-bg-color] justify-end pr-4 w-[30%] sm:w-[18%] truncate`}
          title={key + ""}
        >
          <img
            alt=""
            src="https://pks01.s3.sa-east-1.amazonaws.com/images/usdt_20230810040553.webp"
            className="h-[18px] w-[18px]"
          ></img>
          <span className="text-[#8EA3C3] ml-1 truncate">{key}</span>
        </div>
      </div>
    );
    return data;
  }

  function getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  function addClass() {
    if (scrollList.current) {
      const children = Array.from(scrollList.current.children);
      if (children && children.length > showCount) {
        children.forEach((child, index) => {
          if (index === 0) {
            child.classList.add("slide-enter-active", "slide-enter-to");
          } else if (index === children.length - 1) {
            child.classList.add("slide-leave-active", "slide-leave-to", "slide-move");
          } else {
            child.classList.add("slide-move");
          }
        });
      }
    }
  }

  function clearClass() {
    if (scrollList.current) {
      const children = Array.from(scrollList.current.children);
      children.forEach((child, index) => {
        child.classList.remove(
          "slide-enter-active",
          "slide-enter-to",
          "slide-leave-active",
          "slide-leave-to",
          "slide-move"
        );
        child.style.top = "";
        child.style.position = "relative";
      });
    }
    if (datas.length > showCount) {
      setDatas((data) => {
        return data?.slice(0, -1);
      });
    }
  }

  function addData() {
    clearClass();
    setDatas((prevDatas) => {
      return [getData(0, true), ...prevDatas];
    });
    setIsUpdate((prev) => prev + 1);
  }
  useEffect(() => {
    const interval = setInterval(() => {
      addData();
    }, 1500);

    return () => {
      clearInterval(interval);
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  return (
    <div className="flex flex-col w-full relative mt-8">
      <div className="flex items-center justify-between">
        <div className="flex justify-between gap-[6px]">
          <img src={betsIcon} alt="icon-banner-title" className="w-[20px] h-[20px]" />
          <div className="uppercase font-bold">
            <FormattedMessage id="game_details_1_1_2" />
          </div>
        </div>
        <div className="flex ml-auto overflow-hidden">
          {buttons.map((button, index) => (
            <div
              onClick={() => handlerClick(index)}
              key={index}
              className={`${selected === index ? "bg-[#273954]" : ""} 
                            sm:h-[40px] h-[30px] flex items-center justify-center rounded-md cursor-pointer`}
            >
              <div
                className={`${
                  selected === index ? "text-[--wallet-title-text-color]" : "text-[#55657E]"
                } sm:p-2 p-1 text-xs sm:text-sm font-bold`}
              >
                {button.indexOf("_") > -1 ? <FormattedMessage id={button} /> : button}
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="w-full flex flex-col mt-2">
        <div className="grid grid-cols-3 auto-cols-auto sm:grid-cols-6 bg-content-basic-gray py-2 w-full">
          <div className="web font-bold text-sub-text text-left ml-[1.125rem]">
            <FormattedMessage id="game_details_1_1_3" />
          </div>
          {screenWidth > 1024 && (
            <>
              <div className="web font-bold text-sub-text text-left ">
                <FormattedMessage id="game_details_1_1_5" />
              </div>
              <div className="web font-bold text-sub-text text-left ">
                <FormattedMessage id="game_details_1_1_6" />
              </div>
              <div className="web font-bold text-sub-text text-left ">
                <FormattedMessage id="game_details_1_1_7" />
              </div>
            </>
          )}
          <div className=" relative flex web font-bold text-sub-text justify-center left-5 sm:left-0 sm:justify-start">
            {screenWidth > 1024 ? (
              <FormattedMessage id="game_details_1_1_8" />
            ) : (
              <FormattedMessage id="Multi" />
            )}
          </div>
          <div className=" flex web font-bold text-sub-text justify-end pr-4">
            <FormattedMessage id="game_details_1_1_9" />
          </div>
        </div>

        <div
          className="flex flex-col gap-y-2 top-2 overflow-hidden items-start relative"
          style={{ height: `${hight}px` }}
        >
          <div className={`scroll_list`} ref={scrollList}>
            {datas}
          </div>

          <div className="custer_zz"></div>
        </div>
      </div>
    </div>
  );
};

export default TransformList;
