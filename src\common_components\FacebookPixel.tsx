import { memo, useEffect } from "react";
import ReactPixel from "react-facebook-pixel";
import { useLocation } from "react-router-dom";
import { usePoint } from "./usePoint";

// 修改为 React 组件，接收 locale 作为 props
const FacebookPixel: React.FC<{ locale: string }> = ({ locale }) => {
  const location = useLocation();
  const { errorMessagePoint } = usePoint();

  useEffect(() => {
    const parsedUrl = new URL(window.location.href);
    const search = decodeURIComponent(parsedUrl.search)
      .replace("-~.", "&")
      .replace(/&amp;/g, '&');
    const urlParams = new URLSearchParams(decodeURIComponent(search));
    let fbPixelId = urlParams.get("fb_dynamic_pixel");
    let token = urlParams.get("fb_token");
    if (!fbPixelId) {
      fbPixelId = localStorage.getItem("fb_dynamic_pixel");
      token = localStorage.getItem("fb_token");
    } else {
      localStorage.setItem("fb_dynamic_pixel", fbPixelId);
      localStorage.setItem("fb_token", token);
    }

    const ea = localStorage.getItem("ea");
    const al = localStorage.getItem("al");
    const de = localStorage.getItem("de");
    const referralCode = localStorage.getItem("referralCode");
    const activity = localStorage.getItem("activity");
    // Combine all values into a string and log
    const combinedValues = `ea: ${ea || ''}, al: ${al || ''}, de: ${de || ''}, referralCode: ${referralCode || ''}, activity: ${activity || ''}, fbPixelId: ${fbPixelId || ''}, token: ${token || ''}`;
    errorMessagePoint("faceboo get " + combinedValues, location.pathname, location.pathname)

    console.log("获取到 fb_pixel_id 和 fb_token 参数", fbPixelId, token);

    if (fbPixelId) {
      // 初始化 Pixel，使用你自己的 Pixel ID
      ReactPixel.init(fbPixelId, {
        autoConfig: true, // 自动配置
        debug: false, // 开启调试模式
        fb_token: token
      });
      ReactPixel.trackCustom("first_open");
      // 追踪页面视图事件
      ReactPixel.pageView();
      // 创建URL对象
      // const parsedUrl = new URL(window.location.href);
      // 获取hostname（这将是IP地址或域名）
      // const hostname = parsedUrl.hostname;
      // const data = [
      //   {
      //     event_name: "ViewContent",
      //     event_time: Math.floor(new Date().getTime() / 1000),
      //     user_data: {
      //       country: CryptoJS.SHA256(locale).toString(CryptoJS.enc.Hex)
      //     },
      //     event_source_url: hostname
      //   }
      // ];
      // fbApiFunc(fbPixelId, token, data)
    }
  }, []);

  // 组件不渲染任何内容
  return null;
};

export default memo(FacebookPixel);
