import {Col} from "antd";
import {Form} from "antd";
import {Input} from "antd";
import {Row} from "antd";
import React, { useEffect, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";

interface PixInputProps {
  key: string | number;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>, key: string | number) => void;
  classNames?: string;
}

const PixInput: React.FC<PixInputProps> = ({ value, onChange, key, classNames }) => {
  const [localValues, setLocalValues] = useState({
    part1: "",
    part2: "",
    part3: "",
    part4: ""
  });

  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // 使用 value 初始化输入框的值
  useEffect(() => {
    if (value) {
      setLocalValues({
        part1: value.slice(0, 3),
        part2: value.slice(3, 6),
        part3: value.slice(6, 9),
        part4: value.slice(9, 11)
      });
    } else {
      setLocalValues({
        part1: "",
        part2: "",
        part3: "",
        part4: ""
      });
    }
  }, []);

  useEffect(() => {
    // 拼接成一个完整的数字字符串
    const fullValue = `${localValues.part1}${localValues.part2}${localValues.part3}${localValues.part4}`;
    const event = {
      target: {
        value: fullValue
      }
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(event, key);
  }, [localValues, key]);

  // 处理输入变化
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    part: string,
    index: number
  ) => {
    let val = e.target.value;

    if (index < 3) {
      val = val.replace(/\D/g, "").slice(0, 3); // 每个部分最多3个字符
    } else {
      val = val.replace(/\D/g, "").slice(0, 2); // 最后部分最多2个字符
    }

    setLocalValues({
      ...localValues,
      [part]: val
    });

    // 自动跳转到下一个输入框
    if (val.length === (index < 3 ? 3 : 2)) {
      inputRefs.current[index + 1]?.focus();
    }
    // 如果当前输入框的值为空，则回退到上一个输入框
    if (val.length === 0 && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  return (
    <Row gutter={8}>
      {/* 输入框 1 */}
      <Col span={5}>
        <Form.Item
          rules={[{ required: true, message: <FormattedMessage id="required" /> }]}
          style={{ marginBottom: 0 }}
        >
          <Input
            value={localValues.part1}
            onChange={(e) => handleInputChange(e, "part1", 0)}
            maxLength={3}
            type="tel"
            placeholder="xxx"
            className={`h-[40px] ${classNames}`}
            ref={(el) => (inputRefs.current[0] = el)}
          />
        </Form.Item>
      </Col>

      {/* 输入框 2 */}
      <Col span={1}>
        <div className="text-[32px] text-[#fff] text-center">.</div>
      </Col>

      {/* 输入框 3 */}
      <Col span={5}>
        <Form.Item
          rules={[{ required: true, message: <FormattedMessage id="required" /> }]}
          style={{ marginBottom: 0 }}
        >
          <Input
            value={localValues.part2}
            onChange={(e) => handleInputChange(e, "part2", 1)}
            maxLength={3}
            type="tel"
            placeholder="xxx"
            className={`h-[40px] ${classNames}`}
            ref={(el) => (inputRefs.current[1] = el)}
          />
        </Form.Item>
      </Col>

      {/* 输入框 4 */}
      <Col span={1}>
        <div className="text-[32px] text-[#fff] text-center">.</div>
      </Col>

      {/* 输入框 5 */}
      <Col span={5}>
        <Form.Item
          rules={[{ required: true, message: <FormattedMessage id="required" /> }]}
          style={{ marginBottom: 0 }}
        >
          <Input
            value={localValues.part3}
            onChange={(e) => handleInputChange(e, "part3", 2)}
            maxLength={3}
            type="tel"
            placeholder="xxx"
            className={`h-[40px] ${classNames}`}
            ref={(el) => (inputRefs.current[2] = el)}
          />
        </Form.Item>
      </Col>

      {/* 输入框 6 */}
      <Col span={1}>
        <div className="text-[32px] text-[#fff] text-center leading-[35px]">-</div>
      </Col>

      {/* 输入框 7 */}
      <Col span={5}>
        <Form.Item
          rules={[{ required: true, message: <FormattedMessage id="required" /> }]}
          style={{ marginBottom: 0 }}
        >
          <Input
            value={localValues.part4}
            onChange={(e) => handleInputChange(e, "part4", 3)}
            maxLength={2}
            type="tel"
            placeholder="xx"
            className={`h-[40px] ${classNames}`}
            ref={(el) => (inputRefs.current[3] = el)}
          />
        </Form.Item>
      </Col>
    </Row>
  );
};

export default PixInput;
