import { useEffect } from "react";
import { simulateIdleCallback } from "@/utils/commonUtil";

const useRecaptcha = () => {

  useEffect(() => {
    simulateIdleCallback(() => {
      loadRecaptchaScript();
    });
  }, []);
  
  const loadRecaptchaScript = () => {
    return new Promise<void>((resolve, reject) => {
      if (window.grecaptcha) {
        resolve();
        return;
      }
      const script = document.createElement("script");
      script.src = "https://www.google.com/recaptcha/api.js?render=6LcDwSIrAAAAALSEpHd2cI9o_pwjXdpYVCeq1OZu";
      script.async = true;
      script.defer = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error("Failed to load reCAPTCHA script"));
      document.body.appendChild(script);
    });
  };

  const executeRecaptcha = async (action) => {
    return new Promise((resolve, reject) => {
      if (window.grecaptcha) {
        window.grecaptcha.ready(() => {
          window.grecaptcha
            .execute("6LcDwSIrAAAAALSEpHd2cI9o_pwjXdpYVCeq1OZu", { action })
            .then((token) => resolve(token))
            .catch((error) => reject(error));
        });
      } else {
        reject(new Error("reCAPTCHA script not loaded"));
      }
    });
  };

  return {executeRecaptcha};
};

export default useRecaptcha;
