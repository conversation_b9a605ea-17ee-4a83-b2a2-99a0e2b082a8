import kefuIcon from "@/assets/customer-service-2-fill.svg";
import copyIcon from "@/assets/kefucopy.svg";
import sendIcon from "@/assets/send-plane-horizontal.svg";
import CloseIcon from "@/components/CloseIcon";
import { useAllGame } from "@/components/games/AllGameProvider";
import { useUtils } from "@/components/useUtils";
import { BackStageMessage } from "@/protos/BackStage";
import { setShowLiveSupport } from "@/store";
import { Divider } from "antd";
import { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { useDispatch, useSelector } from "react-redux";
import copy from "copy-to-clipboard";
import { useLocation } from "react-router-dom";

const CustomerService: React.FC = () => {
  const intl = useIntl();
  const { getCurrentLangData } = useUtils();
  const dispatch = useDispatch();
  const [customerServiceList, setCustomerServiceList] = useState<
  BackStageMessage.ICustomerServiceInfo[]
  >([]);
  const showLiveSupport = useSelector((state: BulletinInfo) => state.systemReducer.showLiveSupport);
  const { allConfigData } = useAllGame()  as {
    allConfigData: BackStageMessage.ResConfigDataMessage;
  };
  const location = useLocation();

  useEffect(() => {
    if (window.Intercom && showLiveSupport) {
      window.Intercom("show");
      dispatch(setShowLiveSupport());
    }
  }, [showLiveSupport])

  const [isThirdPartySupport, setIsThirdPartySupport] = useState(true);
  useEffect(() => {
    const insertScripts = () => {
      console.log("开始加载聊天");

      if (
        allConfigData &&
        allConfigData.PopupList &&
        allConfigData.customerServiceList &&
        allConfigData.customerServiceList.length > 0
      ) {
        setIsThirdPartySupport(false);
        setCustomerServiceList(allConfigData.customerServiceList);
      }
      if (allConfigData?.thirdPartyCustomer) {
        setIsThirdPartySupport(true);
        // 创建一个临时容器
        const container = document.createElement("div");
        container.innerHTML = allConfigData.thirdPartyCustomer;
        // 遍历所有子节点
        Array.from(container.childNodes).forEach((node) => {
          if (node.tagName === "SCRIPT") {
            // 处理 <script> 标签
            const script = document.createElement("script");
            if (node.src) {
              // 外部脚本
              script.src = node.src;
              script.defer = true;
            } else {
              // 内联脚本
              script.textContent = node.textContent;
            }
            document.body.appendChild(script); // 插入到 body 末尾
          } else {
            // 处理非 <script> 节点
            document.body.appendChild(node);
          }
        });
        const isHomePage = location.pathname === "/" || location.pathname === "/mobile_personal_center";
        if (window.Intercom) {
          window.Intercom("update", { hide_default_launcher: !isHomePage });
        }
      }
    };

    const timer = setTimeout(insertScripts, 10000); // 兼容不支持 requestIdleCallback 的浏览器
    return () => {
      if (timer) {clearTimeout(timer)}
    }
  }, [allConfigData]);

  const hanldeCopy = async (text: string) => {
    if (text) {
      copy(text);
      intl.formatMessage({ id: "copy_successfully" });
    }
  };
  const handleShowLive = () => {
    dispatch(setShowLiveSupport());
  };

  return (
    <>
      {isThirdPartySupport ? (
        <></>
      ) : (
        <>
          {" "}
          <div
            onClick={() => handleShowLive()}
            style={{ zIndex: 1000 }}
            className=" rounded-xl cursor-pointer w-[46px] h-[46px] fixed lg:bottom-14 bottom-24 right-5 bg-[--bet_table_bg_color] flex items-center justify-center"
          >
            <img src={kefuIcon} alt="" />
          </div>
          <div
            style={{ width: "324px", height: "338px", zIndex: 9999 }}
            className={`${
              showLiveSupport ? "lg:bottom-12 bottom-[50%]" : "-bottom-[100vh]"
            } right-[50%] max-[1024px]:translate-x-[50%] max-[1024px]:translate-y-[50%]  transition-all duration-300 ease-in-out fixed lg:right-24 bg-gradient-to-b from-[#6ced75] to-[#fbfffb] rounded-xl`}
          >
            <div className="flex flex-col p-4 h-full gap-y-2">
              <div className="flex justify-between">
                <div className="flex space-x-1 items-center font-semibold">
                  <img src="/src/assets/chat.svg" alt="" />
                  <span>
                    <FormattedMessage id="send_message" />
                  </span>
                </div>
                <div onClick={() => handleShowLive()}>
                  <CloseIcon />
                </div>
              </div>
              <div className="flex flex-col gap-y-2">
                <div className="mt-3 text-2xl">Hi 👋</div>
                <div className="text-2xl">
                  <FormattedMessage id="how_can__help" />?
                </div>
              </div>

              <div className="flex flex-col gap-y-6 mt-5 overflow-y-auto">
                {customerServiceList
                  .filter((item) => item.language == getCurrentLangData().paramCode)
                  .map((item, index) => (
                    <div key={index} className=" border-[#B6E5BB] flex h-[32px] flex-col">
                      <div className="flex justify-between items-center">
                        <div className="flex gap-x-2 items-center">
                          <img src={item.icon} alt="" className="w-[32px] h-[32px]" />
                          <span className="text-sm">{item.mediaName}</span>
                        </div>
                        <span>
                          {item.links ? (
                            <a href={item.links} target="_blank">
                              {item.contactDetails}
                            </a>
                          ) : (
                            <span>{item.contactDetails}</span>
                          )}
                        </span>
                        <div className="flex gap-x-2 items-center text-sm">
                          {item.links ? (
                            <>
                              <img
                                src={sendIcon}
                                alt=""
                                onClick={() => window.open(item.links, "_blank")}
                                className="w-[16px] h-[16px] cursor-pointer"
                              />
                            </>
                          ) : (
                            <>
                              <img
                                onClick={() => hanldeCopy(item.contactDetails)}
                                src={copyIcon}
                                alt=""
                                className="w-[16px] h-[16px] cursor-pointer"
                              />
                            </>
                          )}
                        </div>
                      </div>

                      <Divider className="!mt-[6px] !p-0 border-[#B7E8BC]" />
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default CustomerService;