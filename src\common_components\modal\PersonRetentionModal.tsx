import CommonModal from "@/components/CommonModal";
import { useAllGame } from "@/components/games/AllGameProvider";
import { Button } from "antd";
import { memo } from "react";
import { useNavigate } from "react-router-dom";
import { useJump } from "../context/useJumpContext";

const PersonRetentionModal: React.FC<{ handleCancel: () => void; showModal: boolean }> = memo(
  ({ showModal, handleCancel }) => {
    const handleClose = () => {};

    const { handleJump } = useJump() || {};
    const { allConfigData } = useAllGame();
    const navigate = useNavigate();

    return (
      <>
        <CommonModal
          width={window.screen.width > 1024 ? "20%" : "90%"}
          footer={null}
          open={showModal}
          onCancel={handleCancel}
          afterClose={handleClose}
          maskClosable={false}
        >
          {allConfigData?.registerRetrieveList?.length > 0 && (
            <div className="text-primary-text flex flex-col w-full min-h-[200px] px-4 pb-4 gap-y-[14px] relative overflow-hidden">
              <span
                className="mb-[14px] ml-5 font-semibold h-[32px] relative"
                style={{ zIndex: 10 }}
              >
              </span>
              <div className="flex w-full min-h-[111px] max-h-[194px] relative">
                <img
                  src={allConfigData?.registerRetrieveList[0]?.icon}
                  alt=""
                  className="object-cover absolute"
                  style={{ zIndex: 3, top: "50%", left: "50%", transform: "translate(-50%, -50%)"}}
                />
              </div>
              <span
                className="w-full flex items-center justify-center text-center"
                style={{ zIndex: 10 }}
                dangerouslySetInnerHTML={{ __html: allConfigData?.registerRetrieveList[0]?.desc }}
              ></span>

              <Button
                className="btn mt-auto"
                style={{ zIndex: 10 }}
                onClick={() => {
                  handleCancel();
                  handleJump(allConfigData?.registerRetrieveList[0], navigate);
                }}
              >
                {allConfigData?.registerRetrieveList?.length > 0 &&
                  allConfigData?.registerRetrieveList[0]?.text}
              </Button>
            </div>
          )}
        </CommonModal>
      </>
    );
  }
);

export default PersonRetentionModal;
