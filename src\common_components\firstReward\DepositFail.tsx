import CommonRewardModal from "../modal/CommonRewardModal";
import { usePersonInfoUtils } from "../usePersonInfoUtils";

import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";

import { useUtils } from "@/components/useUtils";
import { useQueryPromotionList } from "@/hooks/queryHooks/useQueryPromotionList";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { ActivityMessage } from "@/protos/Activity";
import { ProtoMessage } from "@/protos/common";
import { getReceivePromotionsReward } from "@/service/activity";
import { setIschangeUserInfo } from "@/store";
import { PromotionsInfo, ResReceivePromotionsRewardMessage } from "@/types/activity";
import { DItemShow, PlayerInfo } from "@/types/common";
import { ModalType, ShareType } from "@/utils/enums";
import EventBus from "@/utils/EventBus";

const DepositFail = memo(() => {
  const { isLogin } = useUtils();
  const { data: promotionList, refetch } = useQueryPromotionList(isLogin);
  const [showRewardModal, setShowRewardModal] = useState(false);
  const [rewardData, setRewardData] = useState<DItemShow[]>([]);
  const { personValue } = usePersonInfoUtils() as { personValue: PlayerInfo };
  const { checkResponse } = useToastUtils();
  const dispatch = useDispatch();
  const [isDoing, setIsDoing] = useState(false);

  const handleClaim = useCallback(async (item: PromotionsInfo) => {
    const res = (await getReceivePromotionsReward(
      ActivityMessage.ReqReceivePromotionsRewardMessage.create({
        msgID: ProtoMessage.MID.ReqReceivePromotionsReward,
        activityId: item.activityId,
        cId: item.cid
      })
    )) as ResReceivePromotionsRewardMessage;
    if (checkResponse(res)) {
      if (res?.reward?.length > 0) {
        console.log("领取首充失败奖励", res);
        EventBus.emit("closeModal", ModalType.showWalletModal);
        setRewardData(res.reward);
        setShowRewardModal(true);
      }
    }
  }, [checkResponse]);

  const firstReward = useMemo(() => {
    if (promotionList && personValue) {
      const firstReward = promotionList.promotionsList.find(
        (item) => item.activityId === ShareType.firstDepositFail.id
      );
      console.log("首充失败补偿活动", firstReward, personValue);

      // 0.不限制 1.每日 2.每周
      const cycle = firstReward?.cycle;
      let rechargeTimes = 0;
      switch (cycle) {
        case 0:
          rechargeTimes = personValue?.rechargeTimes || 0;
          break;
        case 1:
          rechargeTimes = personValue?.dailyRechargeTimes || 0;
          break;
        case 2:
          rechargeTimes = personValue?.weeklyRechargeTimes || 0;
          break;
      }
      if (isDoing && firstReward?.status != 2 && firstReward?.reward) {
        return firstReward;
      } else if (
        (rechargeTimes === 0 || rechargeTimes === undefined) &&
        firstReward?.status != 2 &&
        firstReward?.reward
      ) {
        return firstReward;
      }
    }
    return null;
  }, [promotionList, personValue, isDoing]);

  useEffect(() => {
    if (firstReward) {
      EventBus.on("showFirstDepositFail", () => {
        setIsDoing(true);
        handleClaim(firstReward);
      });
      return () => {
        EventBus.off("showFirstDepositFail", () => {
          setIsDoing(false);
          handleClaim(firstReward);
        });
      };
    }
  }, [firstReward]);

  return (
    <>
      {showRewardModal && (
        <CommonRewardModal
          showModal={showRewardModal}
          handleCancel={() => {
            setIsDoing(false);
            setShowRewardModal(false);
            refetch();
            dispatch(setIschangeUserInfo(true));
          }}
          rewardData={rewardData || []}
          activityId={firstReward?.activityId || 0}
          activityName={firstReward?.name}
          cId={firstReward?.cid}
        ></CommonRewardModal>
      )}
    </>
  );
});

export default DepositFail;
