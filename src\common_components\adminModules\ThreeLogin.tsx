import telegram from "@/assets/content/tele.png";
import GoogleSignIn from "@/components/GoogleSignIn";
import TeleLogin from "@/components/TeleLogin";
// const GoogleSignIn = lazy(() => import("@/components/GoogleSignIn"));
// const TeleLogin = lazy(() => import("@/components/TeleLogin"));

const ThreeLogin: React.FC = () => {
  <>
    {/* <Suspense fallback={null}> */}
      <TeleLogin>
        <img src={telegram} alt="teleLogo" className="cursor-pointer" />
      </TeleLogin>
    {/* </Suspense> */}
    {/* <Suspense fallback={null}> */}
      <GoogleSignIn>
      </GoogleSignIn>
    {/* </Suspense> */}
  </>;
};

export default ThreeLogin;
