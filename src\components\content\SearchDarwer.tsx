import searchIcon from "@/assets/search.svg";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { ProtoMessage } from "@/protos/common";
import { HallMessage } from "@/protos/Hall";
import { searchGameData } from "@/service/hall";
import { headerHeight } from "@/utils/commonUtil";
import { GameChannel } from '@/utils/enums';
import { ConfigProvider, Drawer, Input } from "antd";
import { useEffect, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useSelector } from "react-redux";
import { useGameData } from "../games/providers/GameDataProvider";
import { useProjectWidth } from "../games/providers/ProjectWidthProvider";
import ScreenSpin from "../ScreenSpin2";
import { useUtils } from "../useUtils";
import SlotsSwipe from "./SlotsSwipe";

const SearchDrawer: React.FC = ({ open, setOpen, divRef, channel = 1 }) => {
  // if (!open) return null;
  const [loading, setLoading] = useState(false);
  const inputRef = useRef(null);
  const {
    isLogin,
    contentPercentage,
    intl,
    getCurrentLangData,
  } = useUtils();
  const { checkResponse } = useToastUtils();
  const leftSiderWith = useSelector((state: any) => state.systemReducer.leftSiderWith);
  const rightDrawerWidth = useSelector((state: any) => state.systemReducer.rightDrawerWidth);
  const [gameList, setGameList] = useState<ProtoMessage.IGameApiInfo[]>([]);
  const [searchText, setSearchText] = useState("");
  const [pageList, setPageList] = useState<PageList[]>([]);
  const sectionId = GameChannel.Casino_PicksForYou;
  const [currentWidth, setCurrentWidth] = useState(0);
  const {
    providerPicksForYouSubChannel,
    allGameMapping,
    allGameLoading,
    allGameChannelMapping
  } = useGameData() ;

  const {leftSideWith}  = useProjectWidth()

  useEffect(() => {
    if (divRef.current) {
      const width =
        window.innerWidth - parseInt(leftSiderWith, 10) - parseInt(rightDrawerWidth, 10);
      console.log(
        "搜索框宽度",
        leftSiderWith,
        rightDrawerWidth,
        window.innerWidth - parseInt(leftSideWith, 10) - parseInt(rightDrawerWidth, 10)
      );
      setCurrentWidth(width);
    }
  }, [divRef, leftSiderWith, rightDrawerWidth]);

  const searchPlaceholder = intl.formatMessage({ id: "game_name_or_provider" });

  useEffect(() => {
    console.log(
      "游戏详情页获取游戏列表数据====",
      allGameLoading,
      allGameMapping,
      providerPicksForYouSubChannel
    );

    if (allGameLoading) return;

    if (allGameMapping && providerPicksForYouSubChannel) {
      setPageList(allGameMapping[providerPicksForYouSubChannel[channel]]);
    }
  }, [allGameLoading, allGameMapping, providerPicksForYouSubChannel]);

  useEffect(() => {
    if (!isLogin) {
      setOpen(false);
    }
  }, [isLogin]);

  useEffect(() => {
    let timer = null
    if (open && inputRef.current) {
      timer = setTimeout(() => {
        inputRef.current.focus();
      }, 100); // 延迟100毫秒以确保Drawer完全打开
    }
    return () => {
      if (timer) {
        clearTimeout(timer)
      }
    }
  }, [open]);
  const getHeaderBottom = () => {
    if (divRef.current) {
      const rect = divRef.current.getBoundingClientRect();
      return rect.bottom;
    }
    return 0;
  };

  useEffect(() => {
    setOpen(false);
  }, [rightDrawerWidth]);

  // 监听键盘事件,搜索数据
  function handlerKeydown(event: React.KeyboardEvent<HTMLDivElement>) {
    if (event.key === "Enter") {
      searchData();
    }
  }

  async function searchData() {
    if (!searchText || searchText.trim().length < 3) {
      return;
    }
    try {
      setLoading(true);
      const res = (await searchGameData(
        HallMessage.ReqSearchGameDataMessage.create({
          msgID: 400031,
          gameName: searchText, //游戏名字
          platformId: null, //平台id
          language: getCurrentLangData().paramCode
        })
      )) as HallMessage.ResSearchGameDataMessage;
      if (checkResponse(res)) {
        console.log("获取到搜索游戏的数据", res);

        setGameList(res.gameApiInfo);
      }
    } finally {
      setLoading(false);
    }
  }

  function handleClose() {
    setOpen(false);
    setGameList([]);
    setSearchText("");
    console.log("关闭时 清空搜索结果");
  }

  useEffect(() => {
    if (searchText && searchText.trim().length >= 3) {
      setGameList([]);
      searchData();
    } else {
      setGameList([]);
    }
  }, [searchText]);

  return (
    <>
      <Drawer
        onClose={() => handleClose()}
        height={"auto"}
        open={open}
        keyboard={false}
        placement={"top"}
        // className="!bg-[--search-input-bg-color]"
        style={{
          top: getHeaderBottom(),
          position: "relative",
          width: currentWidth,
          left: leftSiderWith,
          maxHeight: `calc(100vh - ${headerHeight}px)`,
          minHeight: "420px"
        }}
        closeIcon={null}
        maskClosable={true}
        mask={true}
        getContainer={() => divRef.current}
        maskClassName={`opacity-10`}
        classNames={{ body: " flex flex-col  items-center !overflow-hidden" }}
        destroyOnHidden={true}
      >
        <div
          style={{ width: contentPercentage }}
          className=" mt-6 h-[100%] flex flex-col  items-center "
        >
          <ConfigProvider
            theme={{
              components: {
                Input: {
                  hoverBorderColor: "var(--header-deposit-input-border-color)",
                  colorBorder: "var(--header-deposit-input-border-color)", // 输入框边框颜色
                  activeBorderColor: "var(--header-deposit-input-border-color)", // 输入框激活边框颜色
                  colorBgContainer: `var(--button-bg-color-light-gray)`, // 输入框背景色
                  activeBg: `var(--button-bg-color-light-gray)` // 输入框激活背景颜色
                }
              }
            }}
          >
            <Input
              onChange={(event) => setSearchText(event.target.value)}
              placeholder={searchPlaceholder}
              onKeyDown={(event) => handlerKeydown(event)}
              className="!h-[48px] rounded-md"
              prefix={<img src={searchIcon} className=" cursor-pointer " alt=""/>}
              suffix={
                searchText && (
                  <div className="cursor-pointer" onClick={() => handleClose()}>
                    <FormattedMessage id="cancel" />
                  </div>
                )
              }
            />
          </ConfigProvider>
          <div className="flex flex-col w-full">
            {gameList && gameList.length > 0 && (
              <div className="flex justify-between space-y-2 items-center">
                <span className="text-base py-2">
                  <FormattedMessage id="search_result" />
                </span>
                <span>
                  <FormattedMessage
                    id="abou_results"
                    values={{
                      count: (
                        <span className="text-[--search-input-count-text-color]">
                          {gameList?.length > 100 ? "99+" : gameList?.length}
                        </span>
                      )
                    }}
                  />
                </span>
              </div>
            )}
            {gameList && gameList.length > 0 ? (
              <div
                style={{ overflowY: "auto", minHeight: "50px", maxHeight: "46vh" }}
                className="mt-4 rounded-md grid grid-cols-[repeat(auto-fill,minmax(150px,1fr))] gap-2"
              >
                <SlotsSwipe
                  backFun={handleClose}
                  useControl={false}
                  useSlider={false}
                  datas={gameList}
                  showText={true}
                  sctionId={sectionId}
                  bannerImg={allGameChannelMapping[providerPicksForYouSubChannel[channel]].channelHomeIcon1}
                />
              </div>
            ) : (
              <div className={` w-[100%] min-h-[50px]  rounded-md flex gap-2`}>
                <div className="text-sub-text-light-gray  font-bold flex w-[100%] justify-center py-3 text-base relative">
                  <ScreenSpin loading={loading} />
                  <FormattedMessage id="search_text" />
                </div>
              </div>
            )}
            <div className="w-full">
              {allGameMapping && providerPicksForYouSubChannel[channel] && (
                <SlotsSwipe
                  backFun={handleClose}
                  viewAll={false}
                  infinite={true}
                  autoPlay={true}
                  sctionId={providerPicksForYouSubChannel[channel]}
                  datas={allGameMapping[providerPicksForYouSubChannel[channel]]?.gameApiInfo}
                  title={
                    allGameChannelMapping[providerPicksForYouSubChannel[channel]]?.subChannelName
                  }
                  bannerImg={allGameChannelMapping[providerPicksForYouSubChannel[channel]].channelHomeIcon1}
                />
              )}
            </div>
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default SearchDrawer;
