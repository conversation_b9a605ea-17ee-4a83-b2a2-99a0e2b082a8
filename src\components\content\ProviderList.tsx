import { ProtoMessage } from '@/protos/common';
import { HallMessage } from "@/protos/Hall";
import { getCasinoData } from '@/service/hall';
import { RightOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import MyFooter from "../common/MyFooter";
import { useGameData } from '../games/providers/GameDataProvider';
import { useUtils } from '../useUtils';
import GameImgDetail from "./GameImgDetail";
import { GameChannel } from '@/utils/enums';
import { useToastUtils } from '@/hooks/utils/useToastUtils';

const ProviderList: React.FC = ({channel = 1}) => {
    const {  getCurrentLangData, contentPercentage } = useUtils()
    const { intl, Toast } = useToastUtils();
    const { providerName } = useParams()
    const location = useLocation()
    const [page, setPage] = useState(1)
    const [pageSize, setPageSize] = useState<number>(20)
    const [loading, setLoading] = useState(false)
    const [pageList, setPageList] = useState<PageList>(null)
    const navigate = useNavigate()
    const [showText, setShowText] = useState(true)
    const allProviders = intl.formatMessage({ id: "all_providers" })
    console.log('navigate：', location.state?.from?.pathname);
    const [selectedOptions, setSelectedOptions] = useState([allProviders]);
    const hallHost = useSelector((state: any) => state.systemReducer.hallHost);
    const { allGameMapping, providerSubChannel} = useGameData()
    const [providerId, setProviderId] = useState(0)

    console.log('allGameMapping', allGameMapping);
    const providersText = intl.formatMessage({ id: 'providers' })

    useEffect(() => {
        if (allGameMapping && allGameMapping[providerSubChannel[channel]]) {
            setProviderId(allGameMapping[providerSubChannel[channel]].GameProviders.find((item) => {
                return item.platformName === providerName
            }).platformId)
        }
    }, [allGameMapping, providerSubChannel])

    const fetchCasinoData = async () => {
        if (!hallHost || !providerId) {
            return;
        }
        try {
            const numbersArray = [Number(providerId)];
            const sectionList = [{
                page: page,
                pageSize: pageSize,
                // sectionId: GameChannel.Casino_PicksForYou,
                platformId: numbersArray
            }]
            console.log("请求游戏列表参数", sectionList);

            setLoading(true)
            const [casinoData] = await Promise.all([
                getCasinoData( HallMessage.ReqCasinoDataMessage.create({
                    msgID: ProtoMessage.MID.ReqCasinoData,
                    pagerList: sectionList,
                    language: getCurrentLangData().paramCode
                })) as HallMessage.ResCasinoDataMessage
            ])
            console.log('返回游戏列表数据===========1', casinoData)
            if (casinoData.error > 0) {
                Toast(intl.formatMessage({ id: 'error_message_' + casinoData.error }), 'error', casinoData.error)
                return;
            }

            if (casinoData.pageList.length > 0) {
                setPageList(casinoData.pageList[0])
            }
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchCasinoData()
    }, [page, hallHost, providerId])

    useEffect(() => {
        if (page === 1) {
            fetchCasinoData()
        } else {
            setPage(1)
        }
    }, [location.pathname])

    return (
        <div className={` flex flex-col relative items-center justify-center select-none`}>
            <div style={{ width: contentPercentage }} className="flex flex-col  space-y-4 text-[--wallet-title-text-color]">
                <div className="w-full flex items-center justify-between mt-8">
                    <div className=" space-x-1 rounded-lg border border-custom-gray flex items-center  p-3">
                        <span className="flex text-[#848D98]">
                            <>
                                <span className=" cursor-pointer" onClick={() => navigate("/casino")}>
                                    <FormattedMessage id='casino' />
                                </span>
                                <RightOutlined />
                            </>
                        </span>
                        <span >
                            {
                                providerName
                            }
                        </span>
                    </div>
                </div>
                <div className=' cursor-pointer space-y-4  flex flex-col' 
                    onClick={() => navigate(`/gameList/${providerSubChannel[channel]}/${providersText}` )}>
                    <span className='text-[#37B01E] font-bold'><FormattedMessage id='view_all_providers' /></span>
                    <div className='flex text-primary-text text-2xl space-x-2'>
                        <span>{providerName}</span>
                        <span><FormattedMessage id='games' /></span>
                    </div>
                </div>

                <div className="w-full">
                    <GameImgDetail providerids={selectedOptions} showText={showText} hoverTrans={showText} selectedType={GameChannel.Casino_PicksForYou} loading={loading} page={page} setPage={setPage} datas={pageList} setPageSize={setPageSize} pageSize={pageSize} />
                </div>

            </div>
            <div style={{ width: contentPercentage }} className="flex flex-col space-y-6 pt-[10%]">
                <MyFooter showProviderList = {false} showtransfromlist = {false}/>
            </div>
        </div>
    )
}

export default ProviderList