import huobi4 from '@/assets/content/huobi4.webp';
import ChevronLeftOutlined from '@/assets/deposit/ChevronLeftOutlined.webp';
import searchIcon from '@/assets/search.svg';
import { formatNumberQfw } from '@/utils/commonUtil';
import {Input} from "antd";
import React, { useEffect, useRef, useState } from "react";
import CommonModal from '../CommonModal';

const MyDepositModal: React.FC = ({ popoverContent, open, setOpen, showTitleImg = true, diadiv, type = 1 }) => {

    const currencyDiv = diadiv || useRef(null)
    const [currencyWidth, setCurrencyWidth] = useState('0px')
    const [currentHight, setCurrentHight] = useState('0px')
    useEffect(() => {
        if (currencyDiv.current) {
            setCurrencyWidth(currencyDiv.current.offsetWidth + 'px')
            setCurrentHight(currencyDiv.current.offsetHeight + 'px')
        }
    }, [open])

    const handleCancel = () => {
        setOpen(false);
    };

    function handleCLickHB(index) {
        setOpen(() => false)
    }

    const huobiXlContent = (
        Array.from(Array(40).keys()).map((item, index) => (
            <div key={index} onClick={() => handleCLickHB(index)} className={`hover:bg-[--main-div-checked-bg-color] justify-between flex w-full px-2 py-1 border-2 border-transparent`}>
                <div className='text-[#FFFFFF] flex justify-center items-center space-x-1'>
                    <img src={huobi4} className='w-[16px] h-[16px]' alt=''/>
                    <span>ETH</span>
                </div>
                <div className='text-[#FFFFFF] truncate'>
                    {formatNumberQfw(index)}
                </div>
            </div>
        ))
    )

    return (
        <>
            <CommonModal open={open} width={currencyWidth}
                height={currentHight}
                footer={null} title={null}
                onCancel={handleCancel}
                closable={false}
                maskClosable={false}
            >
                <div className={`flex flex-col justify-center items-center space-y-2 rounded-md w-full`}
                    style={{ height: currentHight }}>
                    <div className="w-[90%] items-center mt-2 " >
                        <img src={ChevronLeftOutlined}  className="cursor-pointer" alt="" onClick={() => setOpen(() => false)}/>
                    </div>
                    <div className='w-[90%] py-3'>
                        <Input style={{ border: 'none' }} placeholder='Search' prefix={<img src={searchIcon} alt=''/>} />
                    </div>
                    <div className=' w-[90%] custom-scorball  flex flex-col items-center overflow-y-auto'
                        style={{ height: '80%' }}>
                        {huobiXlContent}
                    </div>
                </div>
            </CommonModal>
        </>


    )
}

export default MyDepositModal;