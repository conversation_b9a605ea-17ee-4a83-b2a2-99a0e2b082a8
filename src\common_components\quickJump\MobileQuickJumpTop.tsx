// import affiliateIcon from '@/assets/quickJump/affiliate.svg'
// import bounsIcon from '@/assets/quickJump/Bouns.svg'
// import casinoIcon from '@/assets/quickJump/casino.svg'
// import weeklyIcon from '@/assets/quickJump/weekly.svg'
import rightArrowWhiteIcon from '@/assets/rightArrowWhite_two.svg';
import { useAllGame } from '@/components/games/AllGameProvider';
import { BackStageMessage } from "@/protos/BackStage";
import { setActivitySource } from "@/store/popup";
import { useScreenWidth } from '@/utils/MobileDetectionContext';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useJump } from '../context/useJumpContext';

const MobileQuickJumpTop: React.FC = () => {

    const navigate = useNavigate()
    const { screenWidth } = useScreenWidth() 
    const { allConfigData } = useAllGame() as { allConfigData: BackStageMessage.ResConfigDataMessage }
    const {handleJump} = useJump() || {}
    const [bigEnternance, setBigEnternance] = useState<BackStageMessage.IQuickAccessInfo[] | null>(null)
    const [smallEnternance, setSmallEnternance] = useState<BackStageMessage.IQuickAccessInfo[] | null>(null)
    const dispatch = useDispatch()

    useEffect(() => {
        if (allConfigData?.quickAccessList?.length > 0) {
            const quickAccessList = allConfigData.quickAccessList.sort((a, b) => a.sort - b.sort)
            setBigEnternance(quickAccessList.filter(item => item.entranceType === 1))
            setSmallEnternance(quickAccessList.filter(item => item.entranceType === 2))
        }

    }, [allConfigData])

    const handleCustomer = (item: BackStageMessage.IBannerInfo, e: React.MouseEvent) => {
        console.log('banner点击弹框', item);
        e.stopPropagation()
        const jumpType = item?.jumpType
        const url = item?.externalLinks
        const popupLinks = item?.popupLinks
        const innerLinks = item?.innerLinks
        const isJump = item.isJump
        if (isJump && isJump > 0) {
            const popupInfo = BackStageMessage.PopupInfo.create({
                jumpType: jumpType,
                popupLinks: popupLinks,
                externalLinks: url,
                popupType: 1,
                innerLinks: innerLinks,
                notLoginJump: item.notLoginJump,
            })
            dispatch(setActivitySource(4))
            handleJump(popupInfo, navigate)
        }
    }

    return (
        <>
            {
                screenWidth > 1024 ? (
                    <div className="w-full flex justify-between ">
                        {
                            bigEnternance && bigEnternance.map((item, index) => (
                                <div key={item.quickAccessId} onClick={(e) => handleCustomer(item, e)} className={`${item.isJump > 0 ? 'cursor-pointer': ''} flex relative h-[268px] w-[284px] rounded-xl`} 
                                    style={{ backgroundImage: `url(${item.imageUrl})`, backgroundSize: 'cover', backgroundRepeat: 'no-repeat', backgroundPosition: 'center' }} >
                                    <div className='absolute font-bold text-lg text-primary-text top-[8px] left-0 flex justify-between items-center w-full px-2'>
                                        <span >{item.entranceName}</span>
                                        <img src={rightArrowWhiteIcon} alt="" />
                                    </div>
                                </div>
                            ))
                        }
                    </div>
                ) : (
                    <div className="w-full flex flex-col  gap-y-[12px] flex-1 justify-between ">
                        <div className='w-full flex justify-between items-center flex-wrap gap-y-4'>
                            {
                                bigEnternance && bigEnternance.map((item, index) => (
                                    <div key={item.quickAccessId} onClick={(e) => handleCustomer(item, e)} className={`${item.isJump > 0 ? 'cursor-pointer': ''} relative h-[186px] w-[48%] rounded-xl`}
                                        style={{ backgroundImage: `url(${item.imageUrl})`, backgroundSize: 'cover', backgroundRepeat: 'no-repeat', backgroundPosition: 'center' }} >
                                        <div className='absolute font-bold text-lg text-primary-text top-[8px] left-0 flex justify-between items-center w-full px-2'>
                                            <span >{item.entranceName}</span>
                                            <img src={rightArrowWhiteIcon} alt="" />
                                        </div>
                                    </div>
                                ))
                            }
                        </div>
                    </div>
                    
                )
            }

        </>
    )
}

export default MobileQuickJumpTop;