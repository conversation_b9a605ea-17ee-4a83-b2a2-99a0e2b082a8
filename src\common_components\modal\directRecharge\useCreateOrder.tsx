import { usePersonInfoUtils } from "@/common_components/usePersonInfoUtils";
import { usePoint } from "@/common_components/usePoint";
import { useUtils } from "@/components/useUtils";
import { useQueryDepositData } from "@/hooks/queryHooks/useQueryDepositData";
import { useQueryDepositWithdrawData } from "@/hooks/queryHooks/useQueryDepositWithdrawData";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { callKwaiEventBase } from "@/pages/download/kwaiEventService.tsx";
import { BillingMessage } from "@/protos/Billing";
import { ProtoMessage } from "@/protos/common";
import { createRechargeOrder } from "@/service/billing";
import { setGlobalMask } from "@/store";
import { PlayerInfo } from "@/types/common";
import { urlParams } from "@/utils/enums";
import EventBus from "@/utils/EventBus";
import { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";

export function useCreateOrder() {
  const { intl, Toast, checkResponse } = useToastUtils();
  const detailData = useRef<BillingMessage.IDepositFiatData>();
  const onlyCrpto = useRef(false);
  const [rechargeLoading, setRechargeLoading] = useState(true);
  const { isLogin } = useUtils()
  const { data: depositWithdrawData } = useQueryDepositWithdrawData(isLogin);
  const { data: depositData } = useQueryDepositData(depositWithdrawData?.depositWithdrawData.find(item => Math.floor(item.currencyId / 1000) === 1)?.currencyId, "");

  const { personValue } = usePersonInfoUtils() as { personValue: PlayerInfo };
  const dispatch = useDispatch();
  const { rechargePoint, errorMessagePoint } = usePoint();

  useEffect(() => {
    if (depositData) {
      const sortedData = [...depositData.depositFiatData].sort(
        (a, b) => a.sort - b.sort
      );
      const groupedData = sortedData.reduce((accumulator, currentValue) => {
        const key = currentValue.currencyId + "_" + currentValue.category;
        if (!accumulator[key]) {
          accumulator[key] = [];
        }
        accumulator[key].push(currentValue);
        return accumulator;
      }, {});

      console.log("返回充值法币数据", groupedData);

      if (groupedData && Object.keys(groupedData).length > 0) {
        const keys = Object.keys(groupedData);
        if (keys.length > 0) {
          const key = keys[0];
          const dataArray = groupedData[key];
          if (dataArray && dataArray.length > 0) {
            console.log("获取到的分组数据", key, dataArray.slice(0, 1));
            handleDetial(dataArray[0]);
          }
        }
      }
      setRechargeLoading(false);
    }
  }, [depositData]);

  function handleDetial(item: BillingMessage.IDepositFiatData) {
    console.log("放入detailData", item);
    detailData.current = item;
    // setDetailData(item);
  }

  const createOrderByParam = useCallback(
    async (params, pathname = "", isModal = false) => {
      try {
        if (personValue?.agentId && String(personValue.agentId).startsWith("32") && personValue?.currencyTraderData) {
          // if (!personValue?.agentId) {
            EventBus.emit("deposit_bs_connect_model");
            return;
          }
        dispatch(setGlobalMask(true));
        const data = await createRechargeOrder(params);
        console.log("创建充值返回", data);
        const kwaiDynamicPixel = localStorage.getItem(urlParams.kwai_dynamic_pixel) || "";
        const kwaiToken = localStorage.getItem(urlParams.kwai_token) || "";
        const clickId = localStorage.getItem(urlParams.click_id) || "";

        // 调用充值请求事件 (购物车)
        callKwaiEventBase("EVENT_ADD_TO_CART", clickId, kwaiDynamicPixel, kwaiToken);

        // const data = await doDeposit(res)
        if (checkResponse(data)) {
          if (pathname.includes("mobile_deposit") || pathname.includes("profile")) {
            rechargePoint(3, 1, params?.amounts, data?.orderId);
          } else {
            rechargePoint(3, 2, params?.amounts, data?.orderId);
          }
          if (isModal) {
            EventBus.emit("create_order_tcp_PayAddress", data);
          }
          return data;
        }
        return null;
      } catch (error) {
        const errorMessage = JSON.stringify(error, Object.getOwnPropertyNames(error));
        errorMessagePoint(errorMessage, pathname, window.location.host);
        if (error.code === "ECONNABORTED") {
          Toast(intl.formatMessage({ id: "cheannel_exception" }), "error");
        } else if (error.message === "Network Error" || error.message.includes("ERR_NETWORK")) {
          Toast(intl.formatMessage({ id: "newwork_error" }), "error");
        }
      } finally {
        dispatch(setGlobalMask(false));
      }
    },
    []
  );

  const createOrder = useCallback(
    async (
      amount: number,
      paymentSource: number,
      pathName = "",
      currencyId = null,
      isModal = false
    ) => {
      if (personValue?.agentId && String(personValue.agentId).startsWith("32") && personValue?.currencyTraderData) {
      // if (!personValue?.agentId) {
        EventBus.emit("deposit_bs_connect_model");
        return;
      }
      try {
        dispatch(setGlobalMask(true));
        const data = await createRechargeOrder(
          BillingMessage.ReqCreateRechargeOrderMessage.create({
            msgID: ProtoMessage.MID.ReqCreateRechargeOrder,
            currencyId: currencyId ? currencyId : detailData.current?.currencyId, //货币
            paymentMethod: detailData.current?.paymentMethod, //支付方式
            channel: detailData.current?.channel, //渠道
            amounts: Number(amount), //充值金额
            paymentMethodName: detailData.current?.paymentMethodName,
            paymentSource: paymentSource
          })
        );
        console.log("创建充值返回", data);

        const kwaiDynamicPixel = localStorage.getItem(urlParams.kwai_dynamic_pixel) || "";
        const kwaiToken = localStorage.getItem(urlParams.kwai_token) || "";
        const clickId = localStorage.getItem(urlParams.click_id) || "";
        // 调用充值请求事件 (购物车)
        callKwaiEventBase("EVENT_ADD_TO_CART", clickId, kwaiDynamicPixel, kwaiToken);

        // const data = await doDeposit(res)
        if (checkResponse(data)) {
          rechargePoint(3, 3, amount, data?.orderId);
          data.isModal = isModal;
          if (isModal) {
            EventBus.emit("create_order_tcp_PayAddress", data);
          }
          return data;
        }

        return null;
      } catch (error) {
        const errorMessage = JSON.stringify(error, Object.getOwnPropertyNames(error));
        errorMessagePoint(errorMessage, pathName, window.location.host);
        if (error.code === "ECONNABORTED") {
          Toast(intl.formatMessage({ id: "cheannel_exception" }), "error");
        } else if (error.message === "Network Error" || error.message.includes("ERR_NETWORK")) {
          Toast(intl.formatMessage({ id: "newwork_error" }), "error");
        }
      } finally {
        dispatch(setGlobalMask(false));
      }
    },
    [detailData, personValue]
  );

  return { detailData, createOrder, onlyCrpto, rechargeLoading, createOrderByParam };
}
