import { usePersonInfoUtils } from "../usePersonInfoUtils";

import { memo, PropsWithChildren, useCallback, useMemo, useState } from "react";
import { FormattedMessage } from "react-intl";

import reward_1Icon from "@/assets/commonReward/reward_1.webp";
import reward_2Icon from "@/assets/commonReward/reward_2.webp";
import tgShareIcon from "@/assets/commonReward/tgShare.webp";
import CommonModal from "@/components/CommonModal";
import { useUtils } from "@/components/useUtils";
import { useQueryConfigData } from "@/hooks/queryHooks/useQueryConfigData";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { ActivityMessage } from "@/protos/Activity";
import { ProtoMessage } from "@/protos/common";
import { receiveTgShareReward } from "@/service/activity";
import { ResReceiveTgShareRewardMessage } from "@/types/activity";
import { DItemShow, PlayerInfo } from "@/types/common";
import { useScreenWidth } from "@/utils/MobileDetectionContext";

import md5Util from "crypto-js/md5";

const SHARE_URL = (import.meta as any).env.VITE_TG_SHARE_SERVICE_URL;
console.log("TgShareUrl", SHARE_URL);

const CommonRewardModal: React.FC<
  PropsWithChildren<{
    showModal: boolean;
    handleCancel: () => void;
    rewardData: DItemShow[];
    activityId: string | number;
    activityName?: string;
    canFy?: boolean;
    cId?: string | number | null;
  }>
> = memo(
  ({
    showModal,
    handleCancel,
    rewardData,
    activityId = 0,
    activityName,
    canFy = true,
    cId = null
  }) => {
    const { screenWidth } = useScreenWidth();
    const { personValue, updateInfo } = usePersonInfoUtils() as { personValue: PlayerInfo };
    const { formatNumberQfw, cronMap, hanldeMask } = useUtils();
    const { data: config, refetch } = useQueryConfigData(40);
    const { checkResponse, Toast, intl } = useToastUtils();
    const [tgReward, setTgReward] = useState<DItemShow[]>([]);
    console.log("tgReward", config);

    const handleCloseTg = () => {
      setTgReward([]);
      handleCancel();
    };

    const handleName = useMemo(() => {
      if (canFy) {
        return <FormattedMessage id={activityName || "activities"} />;
      }
      return activityName;
    }, [activityName, canFy]);

    const { isOpenShare, shareText } = useMemo(() => {
      let shareText = null;
      const haveText = config?.tgShareList.find(
        (item) => item.amount > 0 && item.activityId == activityId
      );
      const isOpenShare =
        config?.tgShareList.filter((item) => item.activityId == activityId)?.length || 0;
      if (personValue?.tgShareTimes > 0 && haveText) {
        shareText = (
          <span className="text-white text-[13px] font-semibold">
            <FormattedMessage
              id="share_to_tg_channel"
              values={{
                amount: (
                  <span className="text-[#FFF421]">
                    {cronMap[haveText?.currencyId || 0]?.symbol}
                    {formatNumberQfw(haveText?.amount || 0)}
                  </span>
                )
              }}
            />
          </span>
        );
      }
      return { isOpenShare, shareText };
    }, [config?.tgShareList, personValue?.tgShareTimes, activityId, cronMap, formatNumberQfw]);

    const handleReciveTgShardReward = useCallback(async () => {
      try {
        debugger;
        if (personValue?.tgShareTimes > 0) {
          hanldeMask(true);
          const res = (await receiveTgShareReward(
            ActivityMessage.ReqReceiveTgShareRewardMessage.create({
              msgID: ProtoMessage.MID.ReqReceiveTgShareReward,
              activityId: activityId
            })
          )) as ResReceiveTgShareRewardMessage;
          if (checkResponse(res) && res?.rewardShow?.num > 0) {
            const rewards = [];
            rewards.push(res.rewardShow);
            setTgReward(rewards);
            refetch();
          }
        }
        if (!personValue?.businessNo) {
          Toast("无商户号");
          return;
        }
        await shareTg(rewardData);
      } finally {
        hanldeMask(false);
      }
    }, [activityId, checkResponse, hanldeMask, personValue, rewardData]);

    function md5(str: string) {
      return md5Util(str).toString();
    }

    const shareTg = useCallback(
      async (rewardData: DItemShow[]) => {
        const paramAmountList: any[][] = [];
        rewardData.forEach((item) => {
          const amountList = [formatNumberQfw(item.num), cronMap[item.itemId]?.name];
          paramAmountList.push(amountList);
        });
        let name = activityName;
        if (canFy) {
          name = intl.formatMessage({ id: activityName });
        }
        const time = Date.now();
        try {
          const response = await fetch(SHARE_URL + "/tg/api/realtime-push/template", {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              business_no: personValue?.businessNo,
              type: activityId,
              activity_id: cId,
              params: [personValue?.playerName, paramAmountList, name],
              sign: md5(time + "wgs_tg_template_check"),
              time: time
            })
          });

          if (response.ok) {
            const data = await response.json();
            console.log("活动分享tg返回数据", data);
            if (data.status == "success") {
              Toast(intl.formatMessage({ id: "share_success" }));
              updateInfo({ tgShareTimes: personValue?.tgShareTimes - 1 });
              return true;
            }
            Toast(intl.formatMessage({ id: "share_fail" }));
            return false;
          } else {
            throw new Error("Network response was not ok");
          }
        } catch (error) {
          console.log("活动埋点失败", error);
        }
      },
      [personValue, handleName]
    );

    return (
      <>
        <CommonModal
          width={screenWidth > 1024 ? "25%" : "90%"}
          footer={null}
          open={tgReward?.length > 0}
          onCancel={handleCloseTg}
          maskClosable={false}
          title={
            <div className="bg-[--header-bg-color-basic] p-4 rounded-tl-2xl rounded-tr-2xl text-[16px]/6 font-medium">
              <span className="text-[#24A2E0] flex items-center justify-center">
                <FormattedMessage id="share_reward" />
              </span>
            </div>
          }
        >
          <div className="w-full px-4 pb-4">
            <div className="flex items-center justify-center gap-x-2">
              <span className="relative">
                <img src={reward_1Icon} alt="" className="h-[140px]" />
                <img src={reward_2Icon} alt="" className="h-[43px] absolute top-4 left-2" />
                <img src={tgShareIcon} alt="" className=" absolute top-11 h-[68px] left-2" />
              </span>
              <span className="flex flex-col gap-y-2 text-white font-semibold items-center">
                {tgReward?.map((item, index) => {
                  return (
                    <div key={index} className="flex flex-col gap-y-[8px]">
                      <span className=" text-[#FFF421] text-[24px] font-bold flex items-center gap-x-1">
                        <span>{cronMap[item?.itemId || 0]?.symbol}</span>
                        <span>{formatNumberQfw(item.num || 0)}</span>
                      </span>
                      <span className="font-bold text-[#D4D4D7] text-center">
                        <FormattedMessage id="cash" />
                      </span>
                    </div>
                  );
                })}
                <span
                  className="text-pxs font-semibold"
                  style={{ color: "rgba(255, 255, 255, 0.52)" }}
                ></span>
              </span>
            </div>

            <div className="mt-[20px] flex flex-col gap-y-3">
              <button
                className=" w-full !h-[44px] rounded-lg text-white text-[16px] font-semibold"
                onClick={() => handleCloseTg()}
                style={{
                  border: "1px solid rgba(141, 126, 255, 0.80)",
                  background: "rgba(0, 0, 0, 0.32)",
                  boxShadow:
                    "0px 1px 9.1px 0px rgba(165, 90, 255, 0.40), 0px 0px 0px 1px var(--Colors-Effects-Shadows-shadow-skeumorphic-inner-border, rgba(16, 24, 40, 0.18)) inset, 0px -2px 0px 0px var(--Colors-Effects-Shadows-shadow-skeumorphic-inner, rgba(16, 24, 40, 0.05)) inset, 0px 1px 2px 0px var(--Colors-Effects-Shadows-shadow-xs, rgba(16, 24, 40, 0.05))"
                }}
              >
                <FormattedMessage id="okey" />
              </button>
            </div>
          </div>
        </CommonModal>
        <CommonModal
          width={screenWidth > 1024 ? "25%" : "90%"}
          footer={null}
          open={showModal}
          onCancel={handleCancel}
          maskClosable={false}
          title={
            <div className="bg-[--header-bg-color-basic] p-4 rounded-tl-2xl rounded-tr-2xl text-[20px]/6 font-medium">
              <span className="text-white flex items-center justify-center">
                <FormattedMessage id="congratulations" />
              </span>
            </div>
          }
        >
          <div className="w-full px-4 pb-4">
            <div className="flex items-center justify-center gap-x-2">
              <span className="relative">
                <img src={reward_1Icon} alt="" className="h-[140px]" />
                <img src={reward_2Icon} alt="" className="h-[43px] absolute top-4 left-2" />
              </span>
              <span className="flex flex-col gap-y-2 text-white font-semibold items-center">
                <span>
                  <FormattedMessage id="receive_your_reward" />
                </span>

                {rewardData?.map((item, index) => {
                  return (
                    <div key={index} className="flex flex-col gap-y-[8px]">
                      <span className=" text-[#FFF421] text-[24px] font-bold flex items-center gap-x-1">
                        <span>{cronMap[item?.itemId || 0]?.symbol}</span>
                        <span>{formatNumberQfw(item.num || 0)}</span>
                      </span>
                      <span className="font-bold text-[#D4D4D7] text-center">
                        <FormattedMessage id="cash" />
                      </span>
                    </div>
                  );
                })}
                <span
                  className="text-pxs font-semibold"
                  style={{ color: "rgba(255, 255, 255, 0.52)" }}
                >
                  {"<"}
                  {handleName}
                  {">"}
                </span>
              </span>
            </div>

            <div className="mt-[10px] flex flex-col gap-y-3">
              {shareText}

              {isOpenShare > 0 && (
                <button
                  onClick={() => handleReciveTgShardReward()}
                  className="h-[44px] rounded-lg flex items-center gap-x-2 justify-center text-white font-semibold"
                  style={{
                    border: "1px solid rgba(255, 255, 255, 0.30)",
                    background: "linear-gradient(180deg, #25A3E1 0%, #24A1DE 100%)",
                    boxShadow:
                      "0px 1px 9.1px 0px rgba(165, 90, 255, 0.40), 0px 0px 0px 1px var(--Colors-Effects-Shadows-shadow-skeumorphic-inner-border, rgba(16, 24, 40, 0.18)) inset, 0px -2px 0px 0px var(--Colors-Effects-Shadows-shadow-skeumorphic-inner, rgba(16, 24, 40, 0.05)) inset, 0px 1px 2px 0px var(--Colors-Effects-Shadows-shadow-xs, rgba(16, 24, 40, 0.05))"
                  }}
                >
                  <span>
                    <FormattedMessage id="share_to_tg_channel_2" />
                  </span>

                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="32"
                    height="32"
                    viewBox="0 0 32 32"
                    fill="none"
                  >
                    <g clip-path="url(#clip0_9951_51928)">
                      <path
                        d="M16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16C0 24.8366 7.16344 32 16 32Z"
                        fill="white"
                      />
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M7.24251 15.8309C11.9068 13.7988 15.0171 12.459 16.5734 11.8117C21.0167 9.96359 21.94 9.64255 22.5418 9.63195C22.6742 9.62962 22.9701 9.66242 23.1618 9.81797C23.3237 9.94931 23.3682 10.1267 23.3895 10.2513C23.4108 10.3758 23.4374 10.6595 23.4163 10.8811C23.1755 13.4111 22.1336 19.5507 21.6036 22.3843C21.3793 23.5833 20.9376 23.9853 20.5101 24.0247C19.581 24.1102 18.8754 23.4106 17.9755 22.8207C16.5673 21.8976 15.7717 21.323 14.4048 20.4222C12.8251 19.3812 13.8492 18.8091 14.7494 17.874C14.985 17.6293 19.0789 13.9056 19.1581 13.5678C19.168 13.5256 19.1772 13.3681 19.0837 13.285C18.9901 13.2018 18.852 13.2302 18.7524 13.2529C18.6112 13.2849 16.3615 14.7718 12.0035 17.7136C11.365 18.1521 10.7866 18.3657 10.2684 18.3545C9.69709 18.3422 8.59817 18.0315 7.78124 17.766C6.77923 17.4403 5.98286 17.2681 6.05221 16.7149C6.08833 16.4268 6.4851 16.1321 7.24251 15.8309Z"
                        fill="#24A2E0"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_9951_51928">
                        <rect width="32" height="32" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </button>
              )}
              <button
                className=" w-full !h-[44px] rounded-lg text-white text-[16px] font-semibold"
                onClick={handleCancel}
                style={{
                  border: "1px solid rgba(141, 126, 255, 0.80)",
                  background: "rgba(0, 0, 0, 0.32)",
                  boxShadow:
                    "0px 1px 9.1px 0px rgba(165, 90, 255, 0.40), 0px 0px 0px 1px var(--Colors-Effects-Shadows-shadow-skeumorphic-inner-border, rgba(16, 24, 40, 0.18)) inset, 0px -2px 0px 0px var(--Colors-Effects-Shadows-shadow-skeumorphic-inner, rgba(16, 24, 40, 0.05)) inset, 0px 1px 2px 0px var(--Colors-Effects-Shadows-shadow-xs, rgba(16, 24, 40, 0.05))"
                }}
              >
                <FormattedMessage id="okey" />
              </button>
            </div>
          </div>
        </CommonModal>
      </>
    );
  }
);

export default CommonRewardModal;
