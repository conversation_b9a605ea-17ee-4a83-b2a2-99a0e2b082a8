import SiderPopup from "@/common_components/adminModules/SiderPopup";
import { useJump } from "@/common_components/context/useJumpContext";
import DailyRecharge from "@/common_components/modal/DailyRecharge";
import FirstDepositModal from "@/common_components/modal/firstDeposit/FirstDepositModal";
import LimitTimeRecharge from "@/common_components/modal/LimitTimeRecharge";
import PersonRetentionModal from "@/common_components/modal/PersonRetentionModal";
import PlayNowModal from "@/common_components/modal/PlayNowModal";
import PwaInstallModal from "@/common_components/modal/PwaInstallModal";
import GoogleAuthCodeModal from "@/common_components/person/GoogleAuthCodeModal";
import OTPModal from "@/common_components/person/OTPModal";
import TwoStepVerifi from "@/common_components/person/TwoStepVerifi";
import { useHint } from "@/common_components/useHint";
import { useAllGame } from "@/components/games/AllGameProvider";
import SpinModal from "@/components/header/SpinModal";
import DailyCheckIn from "@/components/leftSilder/DailyCheckIn";
import NoticeModal from "@/components/NoticeModal";
import { useUtils } from "@/components/useUtils";
import { BackStageMessage } from "@/protos/BackStage";
import FixedTempWalletModal from "@/templateFixed/componts/modal/FixedTempWalletModal";
import TempFixedLoginOrSignUp from "@/templateFixed/pages/login/TempFixedLoginOrSignUp";
import TempThreeLoginOrSignUp from "@/templateThree/components/common/login/TempThreeLoginOrSignUp";
import TempThreeQuestModal from "@/templateThree/components/TempThreeQuestModal";
import TempThreeWalletModal from "@/templateThree/components/TempThreeWalletModal";
import { getIsLogin } from "@/utils/commonUtil";
import { ModalType, PopuTriggerType } from "@/utils/enums";
import EventBus from "@/utils/EventBus";
import { useScreenWidth } from "@/utils/MobileDetectionContext";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";

// 拆分登录相关弹窗组件
const LoginModals = React.memo(
  ({
    websitId,
    showLoginType,
    showLoginModal,
    handleLoginModal,
    setShowLoginType,
    commonJumpto
  }: {
    websitId: number;
    showLoginType: number;
    showLoginModal: boolean;
    handleLoginModal: (show: boolean) => void;
    setShowLoginType: (type: number) => void;
    commonJumpto: any;
  }) => {
    return (websitId === 6 || websitId === 7) ? (
      <TempFixedLoginOrSignUp
        showType={showLoginType}
        showModal={showLoginModal}
        setShowModal={() => handleLoginModal(false)}
        setShowType={setShowLoginType}
        toJump={commonJumpto}
      />
    ) : (
      <TempThreeLoginOrSignUp
        showType={showLoginType}
        showModal={showLoginModal}
        setShowModal={() => handleLoginModal(false)}
        setShowType={setShowLoginType}
        toJump={commonJumpto}
      />
    );
  }
);

// 拆分认证相关弹窗组件
const AuthModals = React.memo(
  ({
    show2FAModal,
    showGoogleAuthCodeModal,
    showOtpModal,
    setShow2FAModal,
    setShowGoogleAuthCodeModal,
    setShowOtpModal
  }: {
    show2FAModal: boolean;
    showGoogleAuthCodeModal: boolean;
    showOtpModal: boolean;
    setShow2FAModal: (show: boolean) => void;
    setShowGoogleAuthCodeModal: (show: boolean) => void;
    setShowOtpModal: (show: boolean) => void;
  }) => {
    return (
      <>
        <TwoStepVerifi showModal={show2FAModal} handleCancel={() => setShow2FAModal(false)} />
        <GoogleAuthCodeModal
          showModal={showGoogleAuthCodeModal}
          handleCancel={() => setShowGoogleAuthCodeModal(false)}
        />
        <OTPModal showModal={showOtpModal} handleCancel={() => setShowOtpModal(false)} />
      </>
    );
  }
);

const AllPopup = () => {
  const websitId = useSelector((state: any) => state.systemReducer.websitId);
  const [ofPopup, setOfPopup] = useState(false);
  const [showQuest, setShowQuest] = useState(false);
  const [showCheckIn, setShowCheckIn] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showLoginType, setShowLoginType] = useState(1);
  const [showWellet, setShowWellet] = useState(false);
  const [showSpinModal, setShowSpinModal] = useState(false);
  const { screenWidth } = useScreenWidth();
  const [show2FAModal, setShow2FAModal] = useState(false);
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [showAnimatModal, setShowAnimatModal] = useState(false);
  const [showDailyRechargeModal, setShowDailyRechargeModal] = useState(false);
  const [showGoogleAuthCodeModal, setShowGoogleAuthCodeModal] = useState(false);
  const [showPwaInstallModal, setShowPwaInstallModal] = useState(false);
  const [showRetentionModal, setShowRetentionModal] = useState(false);
  const [showLimitRechargeModal, setShowLimitRechargeModal] = useState(false);
  const [showPlayNowModal, setShowPlayNowModal] = useState(false);
  const [showFirstDepositModal, setShowFirstDepositModal] = useState(false);
  const { delay, isLogin } = useUtils();
  const wholeLoading = useSelector((state: any) => state.systemReducer.loading);

  const [currentPopup, setCurrentPopup] = useState<BackStageMessage.IPopupInfo>();
  const [currentRTPopup, setCurrentRTPopup] = useState<BackStageMessage.IPopupInfo>();
  const [currentRDPopup, setCurrentRDPopup] = useState<BackStageMessage.IPopupInfo>();
  const [currentLDPopup, setCurrentLDPopup] = useState<BackStageMessage.IPopupInfo>();
  const [showModal, setShowModal] = useState(false);
  const { allConfigData } = useAllGame() as {
    allConfigData: BackStageMessage.ResConfigDataMessage;
  };
  const { isNotShow } = useHint();
  const pupupIndex = useSelector((state: any) => state.popupReducer.pupupIndex);
  const location = useLocation();

  const handleShowModal = useCallback((type: number) => {
    switch (type) {
      case ModalType.showSignUpModal:
        handleSignUpModal()
        break;
      case ModalType.showLoginModal:
        handleCommonLoginModal()
        break;
      case ModalType.showWalletModal:
        handleWalletModal()
        break;
      case ModalType.showQuestModal:
        handleQuestModal()
        break;
      case ModalType.showCheckInModal:
        handleCheckInModal()
        break;
      case ModalType.showSpinModal:
        handleSpinModal()
        break;
      case ModalType.show2FAModal:
        setShow2FAModal(true)
        break;
      case ModalType.showGoogleAuthCodeModal:
        setShowGoogleAuthCodeModal(true)
        break;
      case ModalType.showOtpModal:
        setShowOtpModal(true)
        break;
      case ModalType.showAnimatModal:
        setShowAnimatModal(true)
        break;
      case ModalType.showRetentionModal:
        setShowRetentionModal(true)
        break;
      case ModalType.showDailyRechargeModal:
        setShowDailyRechargeModal(true)
        break;
      case ModalType.showPwaInstallModal:
        setShowPwaInstallModal(true)
        break;
      case ModalType.showLimitRechargeModal:
        setShowLimitRechargeModal(true)
        break;
      case ModalType.showPlayNowModal:
        setShowPlayNowModal(true)
        break;
      case ModalType.showFirstDepositModal:
        setShowFirstDepositModal(true)
        break;
    }
  }, [])

  const handleCloseModal = useCallback((type: number) => {
    switch (type) {
      case ModalType.showSpinModal:
        setShowSpinModal(false);
        break;
      case ModalType.showWalletModal:
        handleShowWallet(false)
        break;
    }
  }, [])

  useEffect(() => {
    EventBus.on("showModal", handleShowModal)
    EventBus.on("closeModal", handleCloseModal)
    return () => {
      EventBus.off("showModal", handleShowModal)
      EventBus.off("closeModal", handleCloseModal)
    }
  }, [])

  const {
    refreshPopupList,
    handleInnerJum,
    centerPopup,
    rightTopPopup,
    setRightTopPopup,
    rightDownPopup,
    setRightDownPopup,
    leftDownPopup,
    setLeftDownPopup,
    notOpen,
    setNotOpen,
    setCenterPopup,
    commonJumpto,
    setCommonJumpto,
    handleJump,
    setShowHint,
  } = useJump();

  // 使用 useMemo 优化弹窗状态
  const modalStates = useMemo(
    () => ({
      showQuest,
      showCheckIn,
      showWellet,
      showModal,
      showAnimatModal,
      showRetentionModal,
      showDailyRechargeModal,
      showFirstDepositModal,
      showPwaInstallModal,
      showLimitRechargeModal,
      showPlayNowModal,
      show2FAModal,
      showGoogleAuthCodeModal,
      showOtpModal
    }),
    [
      showQuest,
      showCheckIn,
      showWellet,
      showModal,
      showAnimatModal,
      showRetentionModal,
      showDailyRechargeModal,
      showFirstDepositModal,
      showPwaInstallModal,
      showLimitRechargeModal,
      showPlayNowModal,
      show2FAModal,
      showGoogleAuthCodeModal,
      showOtpModal
    ]
  );

  // 登录相关状态单独处理
  const loginStates = useMemo(
    () => ({
      showLoginModal,
      showLoginType
    }),
    [showLoginModal, showLoginType]
  );

  // 使用 useCallback 优化处理函数
  const handleShowWallet = useCallback(
    async (show: boolean) => {
      console.log("触发弹框-关闭---1", show, ofPopup);
      setShowWellet(show);
      setNotOpen(true);
    },
    [setNotOpen]
  );

  const handleShowQuest = useCallback(
    async (show: boolean) => {
      console.log("触发弹框-关闭---3", show, ofPopup);
      setShowQuest(show);
      setNotOpen(true);
    },
    [setNotOpen]
  );

  const handleShowCheckIn = useCallback(
    (show: boolean) => {
      setShowCheckIn(show);
    },
    []
  );

  const handleShowSpinModal = useCallback(
    async (show: boolean) => {
      console.log("触发弹框-关闭---4", show);
      setShowSpinModal(show);
      setNotOpen(true);
    },
    [setShowSpinModal, setNotOpen]
  );

  const handleClose = useCallback(
    async (type: number) => {
      if (type === 1) {
        setShowModal(false);
        await delay(200);
        setCenterPopup((pre) => pre?.slice(1));
      } else if (type === 2) {
        await delay(200);
        setRightTopPopup((pre) => pre?.slice(1));
      } else if (type === 3) {
        await delay(200);
        setRightDownPopup((pre) => pre?.slice(1));
      } else if (type === 4) {
        await delay(200);
        setLeftDownPopup((pre) => pre?.slice(1));
      }
      console.log("关闭弹窗", rightDownPopup);
    },
    [delay, setCenterPopup, setRightTopPopup, setRightDownPopup, setLeftDownPopup]
  );

  const handleLoginModal = useCallback(
    async (show: boolean) => {
      console.log("触发弹框-关闭登陆---2", show, getIsLogin(), showLoginType, notOpen);
      // 只处理登录相关的状态
      setShowLoginType(1);
      setShowHint(false);
      setShowQuest(show),
        setShowSpinModal(show),
        setShowWellet(show),
        setShowRetentionModal(show),
        setShowLoginModal(show),
        setShowCheckIn(show),
        setShowCheckIn(show),
        setCommonJumpto(null),
        setShowFirstDepositModal(show),
        setShowDailyRechargeModal(show),
        setShowPwaInstallModal(show),
        setShowLimitRechargeModal(show),
        setShowPlayNowModal(show);
      // 使用 Promise.all 来批量处理状态更新
      await Promise.all([]);

      // 这个状态决定是否开启下一个弹窗，如果是注册弹
      if (
        showLoginType != 2 ||
        getIsLogin() ||
        (allConfigData?.registerRetrieveList?.length ?? 0) == 0
      ) {
        setNotOpen(true);
      } else {
        // 开启挽留弹窗
        setShowRetentionModal(true);
        return;
      }
      setNotOpen(true);
    },
    [showLoginModal, showLoginType, notOpen, getIsLogin, allConfigData]
  );

  // 处理登录相关弹窗的方法
  const handleCommonLoginModal = useCallback(() => {
    if (!showLoginModal) {
      setShowLoginType(1);
      setShowLoginModal(true);
    }
  }, [showLoginModal]);

  const handleSignUpModal = useCallback(() => {
    if (!showLoginModal) {
      setShowLoginType(2);
      setShowLoginModal(true);
    }
  }, [showLoginModal]);

  // 处理其他功能弹窗的方法
  const handleQuestModal = useCallback(() => {
    if (getIsLogin()) {
      setShowQuest(true);
    } else {
      setShowLoginModal(true);
    }
  }, []);

  const handleCheckInModal = useCallback(() => {
    if (getIsLogin()) {
      setShowCheckIn(true);
    } else {
      setShowLoginModal(true);
    }
  }, []);

  const handleWalletModal = useCallback(() => {
    if (getIsLogin()) {
      setShowWellet(true);
    } else {
      setShowLoginModal(true);
    }
  }, []);

  const handleSpinModal = useCallback(() => {
    setShowSpinModal(true);
  }, []);

  // 使用 useMemo 优化弹窗列表处理
  const popupHandlers = useMemo(
    () => ({
      openRightTopPopupNotice: async (
        wholeLoading: boolean,
        popuList: BackStageMessage.IPopupInfo[]
      ) => {
        if (!wholeLoading && popuList && popuList.length > 0) {
          const item = popuList[0];
          setCurrentRTPopup(item);
        }
      },
      openRightDownPopupNotice: async (
        wholeLoading: boolean,
        popuList: BackStageMessage.IPopupInfo[]
      ) => {
        if (!wholeLoading && popuList && popuList.length > 0) {
          const item = popuList[0];
          setCurrentRDPopup(item);
        }
      },
      openLeftDownPopupNotice: async (
        wholeLoading: boolean,
        popuList: BackStageMessage.IPopupInfo[]
      ) => {
        if (!wholeLoading && popuList && popuList.length > 0) {
          const item = popuList[0];
          setCurrentLDPopup(item);
        }
      },
      openNotice: async (wholeLoading: boolean, popuList: BackStageMessage.IPopupInfo[]) => {
        if (!wholeLoading && popuList && popuList.length > 0) {
          const item = popuList[0];
          const fisrtPopup = item.popupDataList?.[0];
          if (fisrtPopup) {
            console.log("公告居中弹框", item);
            // 1.功能弹窗 2.配置弹窗
            const popupType = fisrtPopup.popupType;
            if (popupType === 1) {
              //系统弹窗 1.任务 2.转盘 3.充值 4.客服
              const systemPopup = fisrtPopup.systemPopup;
              //弹框链接 1.任务 2.转盘 3.充值 4.客服（内连） 5.登录 6.注册
              const popupLinks = fisrtPopup.popupLinks;
              setOfPopup(true);
              handleInnerJum(systemPopup || popupLinks, item);
            } else {
              if (isNotShow(item?.popupId)) {
                handleClose(1);
                return;
              }
              setShowHint(item.showHint ? item?.popupId : null);
              setShowModal(true);
              setCurrentPopup(item);
            }
          }
        }
      }
    }),
    [isNotShow]
  );

  useEffect(() => {
    if (showSpinModal) {
      handleSpinModal();
    }
  }, [showSpinModal, handleSpinModal]);

  // 处理游客访问时的弹窗
  useEffect(() => {
    if (!isLogin && allConfigData?.PopupList?.length > 0) {
      console.log("处理游客访问时的弹窗", allConfigData, isLogin, refreshPopupList, PopuTriggerType.VISITOR);

      refreshPopupList(PopuTriggerType.VISITOR, null, allConfigData);
    }
  }, [isLogin, allConfigData]);

  // 处理中心弹窗
  useEffect(() => {
    if (centerPopup && centerPopup.length > 0 && pupupIndex == 1) {
      console.log("处理中心弹窗-关闭弹窗后", centerPopup);

      popupHandlers.openNotice(wholeLoading, centerPopup);
    }
  }, [centerPopup, wholeLoading, pupupIndex]);

  // 处理右上弹窗
  useEffect(() => {
    if (rightTopPopup && pupupIndex == 1) {
      popupHandlers.openRightTopPopupNotice(wholeLoading, rightTopPopup);
    }
  }, [rightTopPopup, wholeLoading, pupupIndex]);

  // 处理右下弹窗
  useEffect(() => {
    if (rightDownPopup && pupupIndex == 1) {
      popupHandlers.openRightDownPopupNotice(wholeLoading, rightDownPopup);
    }
  }, [rightDownPopup, wholeLoading, pupupIndex]);

  // 处理左下弹窗
  useEffect(() => {
    if (leftDownPopup && pupupIndex == 1) {
      popupHandlers.openLeftDownPopupNotice(wholeLoading, leftDownPopup);
    }
  }, [leftDownPopup, wholeLoading, pupupIndex]);

  return (
    <>
      {/* <Suspense fallback={null}> */}

      <>
        {/* <Suspense fallback={null}> */}
        {/* <WalletModal */}
        {
          websitId === 6 || websitId === 7 || websitId === 8 ? (
            <FixedTempWalletModal
              showWalletModal={modalStates.showWellet}
              setShowWalletModal={() => handleShowWallet(false)}
              screenWidth={screenWidth}
            />
          ) : (
            <TempThreeWalletModal
              showWalletModal={modalStates.showWellet}
              setShowWalletModal={() => handleShowWallet(false)}
              screenWidth={screenWidth}
            />
          )
        }

        {/* </Suspense> */}

        {/* <Suspense fallback={null}> */}
        <LoginModals
          websitId={websitId}
          showLoginType={loginStates.showLoginType}
          showLoginModal={loginStates.showLoginModal}
          handleLoginModal={handleLoginModal}
          setShowLoginType={setShowLoginType}
          commonJumpto={commonJumpto}
        />
        {/* </Suspense> */}

        {/* <Suspense fallback={null}> */}
        <TempThreeQuestModal
          showModal={modalStates.showQuest}
          handleCancel={() => handleShowQuest(false)}
        />
        {/* </Suspense> */}
      </>

      {/* <Suspense fallback={null}> */}
      <DailyCheckIn
        showModal={modalStates.showCheckIn}
        handleCancel={() => handleShowCheckIn(false)}
      />
      {/* </Suspense> */}

      {/* <Suspense fallback={null}> */}
      <AuthModals
        show2FAModal={modalStates.show2FAModal}
        showGoogleAuthCodeModal={modalStates.showGoogleAuthCodeModal}
        showOtpModal={modalStates.showOtpModal}
        setShow2FAModal={setShow2FAModal}
        setShowGoogleAuthCodeModal={setShowGoogleAuthCodeModal}
        setShowOtpModal={setShowOtpModal}
      />
      {/* </Suspense> */}

      {/* <Suspense fallback={null}> */}
      <SpinModal showModal={showSpinModal} handleCancel={() => handleShowSpinModal(false)} />
      {/* </Suspense> */}

      <PersonRetentionModal
        showModal={modalStates.showRetentionModal}
        handleCancel={() => handleLoginModal(false)}
      />

      <DailyRecharge
        showModal={modalStates.showDailyRechargeModal}
        handleCancel={() => handleLoginModal(false)}
      />

      <FirstDepositModal
        showModal={modalStates.showFirstDepositModal}
        handleCancel={() => {
          handleLoginModal(false);
        }}
      />

      <PwaInstallModal
        showModal={modalStates.showPwaInstallModal}
        handleCancel={() => handleLoginModal(false)}
      />

      <LimitTimeRecharge
        showModal={modalStates.showLimitRechargeModal}
        handleCancel={() => handleLoginModal(false)}
      />

      <PlayNowModal
        showModal={modalStates.showPlayNowModal}
        handleCancel={() => handleLoginModal(false)}
      />

      {/* <Suspense fallback={null}> */}
      <NoticeModal
        handleJump={handleJump}
        showModal={modalStates.showModal}
        handleCancel={() => handleClose(1)}
        currentPopup={currentPopup}
      />
      {/* </Suspense> */}

      {currentRTPopup && location.pathname == "/" && (
        // <Suspense fallback={null}>
        <SiderPopup
          smallPopup={currentRTPopup}
          smallPopupType={3}
          handleClose={handleClose}
          handleJump={handleJump}
        />
        // </Suspense>
      )}
      {currentRDPopup && location.pathname == "/" && (
        <SiderPopup
          smallPopup={currentRDPopup}
          smallPopupType={4}
          handleClose={handleClose}
          handleJump={handleJump}
        />
      )}
      {currentLDPopup && location.pathname == "/" && (
        <SiderPopup
          smallPopup={currentLDPopup}
          smallPopupType={2}
          handleClose={handleClose}
          handleJump={handleJump}
        />
      )}

      {/* </Suspense> */}
    </>
  );
};

export default React.memo(AllPopup);
