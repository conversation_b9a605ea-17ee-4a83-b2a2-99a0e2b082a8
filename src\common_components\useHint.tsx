export function useHint() {
  const key = "notShowHintArray";
  const clearTime = "hintClearTime";

  const isNotShow = (popupId: number) => {
    clearMemory();
    let notShowHintArray = [];
    if (localStorage.getItem(key)) {
      notShowHintArray = JSON.parse(localStorage.getItem(key));
    }
    return notShowHintArray.includes(popupId);
  };

  const clearMemory = () => {
    const now = new Date().getTime(); // 当前时间
    const storedTimestamp = localStorage.getItem(clearTime);
    if (storedTimestamp) {
      const nextClearTime = parseInt(storedTimestamp, 10);

      // 如果当前时间已经超过了下次清空的时间戳（即零点），则清空数组
      if (now > nextClearTime) {
        // 清空数组
        localStorage.setItem(key, JSON.stringify([]));
        // 更新下次清空的时间戳为明天零点
        const now = new Date();
        const midnight = new Date(now);
        midnight.setHours(24, 0, 0, 0); // 设置为当天的零点时间
        localStorage.setItem(clearTime, midnight.getTime().toString());
      }
    } else {
      const now = new Date();
      const midnight = new Date(now);
      midnight.setHours(24, 0, 0, 0); // 设置为当天的零点时间
      localStorage.setItem(clearTime, midnight.getTime().toString());
    }
  };

  const addPopup = (popupId: number) => {
    let notShowHintArray = JSON.parse(localStorage.getItem(key));
    if (!notShowHintArray) {
      notShowHintArray = [];
    }
    notShowHintArray.push(popupId);
    localStorage.setItem(key, JSON.stringify(notShowHintArray));
  };

  const removePopup = (popupId: number) => {
    let notShowHintArray = JSON.parse(localStorage.getItem(key));
    if (!notShowHintArray) {
      return; // 如果数组为空，则无需操作
    }

    // 移除指定的 popupId
    notShowHintArray = notShowHintArray.filter((id) => id !== popupId);

    // 将更新后的数组存回 localStorage
    localStorage.setItem(key, JSON.stringify(notShowHintArray));
  };
  return { isNotShow, addPopup, removePopup };
}
