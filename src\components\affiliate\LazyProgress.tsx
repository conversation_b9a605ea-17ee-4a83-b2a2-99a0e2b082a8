import {Progress} from "antd";
import { useEffect, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";

const LazyProgress: React.FC = ({ customPer }) => {
  const [progress, setProgress] = useState(0);
  const progressRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            // observer.disconnect();
          } else {
            setIsVisible(false);
            setProgress(0);
          }
        });
      },
      {
        threshold: 0.1
      }
    );

    if (progressRef.current) {
      observer.observe(progressRef.current);
    }

    return () => {
      if (progressRef.current) {
        observer.unobserve(progressRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (isVisible) {
      const intervalTime = 2000 / customPer; // 每次增加1%的时间间隔
      const interval = setInterval(() => {
        setProgress((prev) => (prev < customPer ? prev + 1 : prev));
      }, intervalTime);

      return () => clearInterval(interval);
    }
  }, [isVisible]);

  return (
    <div ref={progressRef} className={`fade-in-up ${isVisible ? "visible" : ""}`}>
      <div className="progress-container">
        <Progress
          format={(percent) => (
            <div className="text-[--wallet-title-text-color] flex flex-col space-y-1 progress-text">
              <span>{percent}%</span>
              <span className="text-sm text-[--2-step-text-bg-color]">
                <FormattedMessage id="revenue_share" />
              </span>
            </div>
          )}
          style={{ color: "white" }}
          rootClassName="!text-white"
          className="affiliate-progress"
          strokeLinecap={"square"}
          strokeWidth={9}
          size={180}
          strokeColor="var(--affiliate-progress-bg-color)"
          type="circle"
          percent={progress}
        />
      </div>
    </div>
  );
};

export default LazyProgress;
