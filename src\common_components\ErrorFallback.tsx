import { memo } from 'react';

interface FallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

const ErrorFallback: React.FC<FallbackProps> = memo(({ error, resetErrorBoundary }) => {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      width: '100vw',
      backgroundColor: 'gray',
      padding: '20px',
    }}>
      <h2>An error occurred</h2>
      <div style={{width: '100%', height: '100%'}}>
        <p>error message: {error.message}</p>
        {/* <span>{error.stack}</span> */}
      </div>
      <div style={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '10px',
      }}>
        <button style={{
          padding: '10px',
          borderRadius: '5px',
        }} onClick={resetErrorBoundary}>Retry</button>
        <button style={{
          padding: '10px',
          borderRadius: '5px',
        }} onClick={() => window.location.reload()}>Refresh</button>
      </div>
    </div>
  );
});

export default ErrorFallback;