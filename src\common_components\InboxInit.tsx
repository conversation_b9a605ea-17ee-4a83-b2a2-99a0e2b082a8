import { useUtils } from "@/components/useUtils";
import { useQueryInBox } from "@/hooks/queryHooks/useQueryInBox";
import { memo, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setPlayNewMessageMp3 } from "@/store/promotion";
import usePlayNewMessage from "./usePlayNewMessage";

const InboxInit = () => {
    const { isLogin } = useUtils();
    const dispatch = useDispatch();
    const {isAudioEnabled, playNewMessage} = usePlayNewMessage();
    const playNewMessageMp3 = useSelector((state: boolean) => state.promotionReducer.playNewMessageMp3);
    const isInit = useRef(false)

    const { data } = useQueryInBox(isLogin)

    useEffect(() => {
        if (isAudioEnabled && !isInit.current && data && data.inboxList && (data.inboxList.filter(item => !item.read && item.inboxType != 3)?.length || 0) > 0) {
            isInit.current = true;
            console.log("站内信消息---用户已授权", isInit.current, data.inboxList, isAudioEnabled);
            
            dispatch(setPlayNewMessageMp3(true))
        }
    }, [data, isAudioEnabled])

    useEffect(() => {
        console.log("isAudioEnabled", isAudioEnabled);
        
        if (isAudioEnabled && playNewMessageMp3) {
          console.log("站内信消息---调用音频");
          
          playNewMessage()
          dispatch(setPlayNewMessageMp3(false))

        }
      },[playNewMessageMp3, isAudioEnabled])

    return null

}

export default memo(InboxInit)