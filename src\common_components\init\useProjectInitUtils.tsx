import { ActivityMessage } from "@/protos/Activity";
import { BackStageMessage } from "@/protos/BackStage";
import { ProtoMessage } from "@/protos/common";
import { HallMessage } from "@/protos/Hall";
import { getActivityDataMessage } from "@/service/activity";
import { getConfigData } from "@/service/backStage";
import { getGameChannelData } from "@/service/hall";
import { setAllConfig } from "@/store";
import { getCurrentLangData } from "@/utils/commonUtil";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setPromotions } from "@/store/promotion";
import { getPromotionsData } from "@/service/activity";
import { getCasinoData } from "@/service/hall";
import { useQuery } from "@tanstack/react-query";
import { QueryKey } from "@/utils/QueryKey";
import { transformCasinoData } from "@/utils/transformCasinoData";
import { setProviderWidth, setProviderHeight } from "@/store/popup";
import useCustmerDebounce from "@/hooks/useCustmerDebounce";

export function useProjectInitUtils(defaultLang) {
  const dispatch = useDispatch();
  const [isInit, setIsinit] = useState(false);
  const allConfig_redux = useSelector((state: any) => state.systemReducer.allConfig);
  const [channel, setChannel] = useState();
  const [allConfigData, setAllConfigData] = useState();
  console.log("gamechannel_redux", defaultLang, !!defaultLang);
  const [querList, setQuerList] = useState(null);

  const handleResize = useCustmerDebounce(() => {
    const containerWidth = Math.min(window.innerWidth - 20, 520);
    if (!containerWidth) return;
    const newWidth = Math.floor((containerWidth - 18) / 4);
    const newHeight = Math.floor(newWidth * 1.33);
    dispatch(setProviderWidth(newWidth));
    dispatch(setProviderHeight(newHeight));
  }, 100);

  useEffect(() => {
    // 首次执行一次
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // 获取游戏频道数据
  const { data: channelData } = useQuery({
    queryKey: [QueryKey.GameChannelData],
    queryFn: async () => {
      const response = await getGameChannelData(
        HallMessage.ReqGameChannelDataMessage.create({
          msgID: ProtoMessage.MID.ReqGameChannelData,
          language: getCurrentLangData().paramCode
        })
      );
      if (response && response.error <= 0) {
        localStorage.setItem("gameChannelData", JSON.stringify(response));
        const querList = [];
        const list = response.gameChannelInfo.flatMap((item) => item.gameSubChannelList)?.map((item) => {
          return {
            page: 1,
            pageSize: 20,
            sectionId: item.subChannel.toString()
          };
        });
        if (list?.length > 0) {
          querList.push(...list);
        }
        localStorage.setItem("queryList", JSON.stringify(querList));
        setQuerList(querList);
        return response;
      }
      return null;
    },
    placeholderData: () => {
      const channelData = localStorage.getItem("gameChannelData");
      if (channelData) {
        return JSON.parse(JSON.stringify(channelData));
      }
      return JSON.parse('{"msgID":400507,"gameChannelInfo":[]}');
    },
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
    enabled: !!defaultLang
  });

  useQuery({
    queryKey: [QueryKey.CasinoData, querList],
    queryFn: async () => {
      const res = await getCasinoData(
        HallMessage.ReqCasinoDataMessage.create({
          msgID: ProtoMessage.MID.ReqCasinoData,
          pagerList: querList,
          language: getCurrentLangData().paramCode
        })
      )
      if (res.error <= 0) {
        const casinoData = transformCasinoData(res, channelData.gameChannelInfo);
        localStorage.setItem("gameChannelMapping", JSON.stringify(casinoData));
        return casinoData;
      }
      return null;
    },
    placeholderData: () => {
      const gameChannelMapping = localStorage.getItem("gameChannelMapping");
      if (gameChannelMapping) {
        return JSON.parse(gameChannelMapping);
      }
      return null;
    },
    enabled: !!defaultLang && querList?.length > 0 && channelData?.gameChannelInfo?.length > 0,
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
  });

  const { data: allConfig } = useQuery({
    queryKey: [QueryKey.AllConfigData, 0],
    queryFn: async () => {
      const response = await getConfigData(
        BackStageMessage.ReqConfigDataMessage.create({
          msgID: ProtoMessage.MID.ReqConfigData,
          configType: 0
        })
      );
      if (response && response.error <= 0) {
        dispatch(setAllConfig(JSON.parse(JSON.stringify(response))));
        return response;
      }
      return null;
    },
    placeholderData: () => {
      const allConfigRedux = allConfig_redux
        ? JSON.parse(JSON.stringify(allConfig_redux))
        : JSON.parse(
            '{"msgID":400506,"exchangeRateList":[],"cItemList":[],"headList":[],"bannerList":[],"PopupList":[],"referralRewardList":[],"languageList":[],"bottomMenuList":[],"helpCenterInfo":[],"threePartyLoginList":[],"functionId":[],"webSiteData":{},"showCurrencyId":1000,"quickAccessList":[],"thirdPartyCustomer":"","chargingBenefitsOpen":true,"upgradeRewardsOpen":true,"weeklyCashBackOpen":true,"monthlyCashBackOpen":true,"pwaInfo":{},"registerRetrieveList":[],"dailyRechargePopList":[],"firstChargePopList":[],"gamePopList":[]}'
          );
      return allConfigRedux;
    },
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
    enabled: !!defaultLang
  });

  useQuery({
    queryKey: [QueryKey.ActivityData],
    queryFn: async () => {
      const res = (await getActivityDataMessage(
        ActivityMessage.ReqActivityDataMessage.create({
          msgID: ProtoMessage.MID.ReqActivityData,
          language: getCurrentLangData().paramCode
        })
      )) as ActivityMessage.ResActivityDataMessage;
      console.log("返回活动数据", res);

      if (res.error <= 0) {
        return res;
      }
      return null;
    },
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
    enabled: !!defaultLang
  });

  useQuery({
    queryKey: [QueryKey.PromotionData],
    queryFn: async () => {
      const data = ActivityMessage.ReqPromotionsDataMessage.create({
        msgID: ProtoMessage.MID.ReqPromotionsData,
        language: getCurrentLangData().paramCode
      });

      const res = (await getPromotionsData(data)) as ActivityMessage.ResPromotionsDataMessage;
      console.log("init获取到数据", res);
      if (res.error <= 0) {
        dispatch(setPromotions(res.toJSON()));
        return res;
      }
    },
    enabled: !!defaultLang,
    staleTime: 1000 * 5
  });

  

  useEffect(() => {
    async function init() {
      setAllConfigData(allConfig);
      setChannel(channelData);
      setIsinit(true);
    }
    if (defaultLang && channelData && allConfig) {
      init();
    }
  }, [channelData, allConfig, defaultLang]);

  return { isInit, allConfigData, channel };
}
