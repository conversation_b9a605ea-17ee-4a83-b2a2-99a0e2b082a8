import { ProtoMessage } from "@/protos/common";
import { LoginMessage } from "@/protos/Login";
import { getWebSiteModel } from "@/service/user";
import { getLangById } from "@/utils/commonUtil";
import { QueryKey } from "@/utils/QueryKey";
import { useQuery } from "@tanstack/react-query";
import { useCallback, useEffect, useRef, useState } from 'react';
import { IntlProvider } from "react-intl";
import useLanguage from "../useLanguage";

export const LanguageProvider = ({ children }) => {
  const [messages, setMessages] = useState();
  const isInit = useRef(false);

  const {
    data: webSite,
  } = useQuery({
    queryKey: [QueryKey.WebSiteData],
    queryFn: async () => {
      const webSite = await getWebSiteModel(
        LoginMessage.ReqWebSiteModelMessage.create({
          msgID: ProtoMessage.MID.ReqWebSiteModel
        })
      );
      if (webSite.error > 0) {
        window.alert("站点信息获取失败，请联系管理员");
        return null;
      }
      return webSite;
    },
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
  });

  useLanguage();

  const loadMessages = useCallback(async (locale: string) => {
    try {
      console.log('Loading messages for locale:', locale);
      const messages = await import(`@/locales/${locale}.json`);
      console.log('Loaded messages:', messages.default);
      return messages.default;
    } catch (error) {
      console.error(`Failed to load messages for ${locale}`, error);
      const defaultMessages = await import('@/locales/en.json');
      return defaultMessages.default;
    }
  }, []);

  const changeLanguage = useCallback(async (newLocale: string) => {
    try {
      console.log('Changing language to:', newLocale);
      const newMessages = await loadMessages(newLocale);
      setMessages(newMessages);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  }, [loadMessages]);

  // 初始化时加载默认语言
  useEffect(() => {
    const loadMessagesAsync = async () => {
      if (webSite && !isInit.current) {
        isInit.current = true;
        const cacheLang = localStorage.getItem("locale")
        const defaultLang = getLangById(webSite.webSiteInfo.language);
        const savedLang = cacheLang != defaultLang.code ? cacheLang : defaultLang.code;
        await changeLanguage(savedLang);
      }
    };

    loadMessagesAsync();
  }, [webSite]);

  console.log("LanguageProvider渲染了============", messages, getLangById(webSite.webSiteInfo.language).code);
  
  if (!messages) {
    return null;
  }
  
  return (
      <IntlProvider messages={messages} locale={getLangById(webSite.webSiteInfo.language).code} defaultLocale="en">
        {children}
      </IntlProvider>
  );
};

export default LanguageProvider;