import noDataIcon from "@/assets/noData.svg";

import MyAvatar from "@/common_components/person/MyAvatar";
import { useCurrency } from "@/common_components/useCurrency";
import useHeadImg from "@/common_components/useHeadImg";
import { BackStageMessage } from "@/protos/BackStage";
import { ProtoMessage } from "@/protos/common";
import { TcpMessage } from "@/protos/tcp";
import { WalletMessage } from "@/protos/Wallet";
import { getBetHistoryData } from "@/service/wallet";
import { useScreenWidth } from "@/utils/MobileDetectionContext";
import { Table, TableProps } from "antd";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import "../../css/ListJb.css";
import { useAllGame } from "../games/AllGameProvider";
import SelectStyleThirdType from "../SelectStyleThirdType";
import { useUtils } from "../useUtils";
import { useConversionUtils } from "@/hooks/utils/useConversionUtils";
import { useToastUtils } from "@/hooks/utils/useToastUtils";

interface DataType {
  key: string;
  gameImg: string;
  gameValue: string;
  playerImg: string;
  playerValue: string;
  timeValue: string;
  wagerImg: string;
  wagerValue: string;
  mutiValue: string;
  payoutImg: string;
  payoutValue: string;
}

const TransformList_bc: React.FC = ({ gameId }) => {
  const buttons = ["all_beats", "high_rollers", "lucky_bets"];
  console.log("TransformList_bc执行了======================");

  const showCount = useRef(10);
  const [selected, setSelected] = useState(0);
  const { cronMap, formatDate2, hidePersonName, isLogin, formatNumberQfw } = useUtils();
  const { checkResponse } = useToastUtils();
  const { converterPlus } = useConversionUtils();
  const [showData, setShowData] = useState<BetInfo[] | null>([]);
  const [hignRoller, setHignRoller] = useState<BetInfo[] | null>([]);
  const [luckyBets, setLuckyBets] = useState<BetInfo[] | null>([]);
  const [myBetsLoading, setMyBetsLoading] = useState(false);
  const [myBetsList, setMyBetsList] = useState<BetInfo[] | null>([]);
  const { screenWidth } = useScreenWidth();
  const { allConfigData } = useAllGame() as {
    allConfigData: BackStageMessage.ResConfigDataMessage | null;
  };
  const { fiatSymbol, viewFiat } = useCurrency();
  const navigate = useNavigate();
  const [finallyShowData, setFinallyShowData] = useState<BetInfo[] | null>([]);

  // const [tableKey, setTableKey] = useState(new Date().getTime())

  const renderCount = useRef(0);
  renderCount.current++;

  useEffect(() => {
    console.log("TransformList_bc组件已渲染次数:", renderCount.current);
  });

  const gameIconMapping = useMemo(() => {
    if (allConfigData?.exchangeRateList) {
      const gameIconMapping = allConfigData.gameTypeList.reduce((acc, item) => {
        acc[item.gameType] = item.icon;
        return acc;
      }, {});
      return gameIconMapping;
    }

    return {};
  }, [allConfigData]);

  if (gameId && isLogin) {
    buttons.push("my_beats");
  }

  const gameNodeData = useSelector(
    (state: TcpMessage.ResTcpGameNoteDataMessage) => state.systemReducer.gameNoteData
  );
  const myBetsData = useSelector((state: ResTcpGameNoteDataMessage) => state.systemReducer.myBets);

  useEffect(() => {
    if (myBetsData && myBetsData.length > 0) {
      setMyBetsList((prev) => {
        const newData = gameNodeData.allBet;
        const updateDate = [...newData, ...prev];
        return updateDate?.slice(0, 40);
      });
    }
  }, [myBetsData]);

  useEffect(() => {
    if (gameNodeData) {
      const updateList = (prevList: BetInfo[], newData: BetInfo[]) => {
        return [...newData, ...prevList]?.slice(0, 40);
      };

      if (gameNodeData.allBet?.length > 0) {
        setShowData(prev => updateList(prev || [], gameNodeData.allBet));
      }
      if (gameNodeData.highRollers?.length > 0) {
        setHignRoller(prev => updateList(prev || [], gameNodeData.highRollers));
      }
      if (gameNodeData.luckyBet?.length > 0) {
        setLuckyBets(prev => updateList(prev || [], gameNodeData.luckyBet));
      }
      // setTableKey(new Date().getTime())
    }
  }, [gameNodeData]);

  const pageOptions = [
    { value: 10, label: "10" },
    { value: 20, label: "20" },
    { value: 40, label: "40" }
  ];

  const handleEnterGame = useCallback((item: ProtoMessage.BetInfo) => {
    navigate(`/gameDetail/${item.gameId}`);
  }, [navigate]);

  const { HeadImgMapping } = useHeadImg();

  const getAvatarSrc = (headId: number) => {
    // 先检查本地缓存中是否已经有这个头像
    const cachedAvatar = localStorage.getItem(`avatar-${headId}`);

    // 如果缓存中有头像，直接返回缓存的头像
    if (cachedAvatar) {
      return cachedAvatar;
    }

    // 如果缓存中没有头像，使用映射表获取头像URL
    const avatarUrl = HeadImgMapping[headId];

    // 将头像URL缓存到 localStorage 中
    localStorage.setItem(`avatar-${headId}`, avatarUrl);

    return avatarUrl;
  };

  const mobileColumns: TableProps<DataType>["columns"] = [
    {
      title: (
        <span className="text-pxs font-medium text-[--mobile-bets-title-color]">
          <FormattedMessage id="game_details_1_1_3" />
        </span>
      ),
      dataIndex: "game",
      key: "game",
      align: "left",
      width: "30%",
      ellipsis: true,
      hidden: isLogin && gameId,
      render: (_, record: ProtoMessage.BetInfo) => (
        <div
          className="cursor-pointer flex gap-x-1 items-center"
          onClick={() => handleEnterGame(record)}
        >
          <img
            loading="lazy"
            src={gameIconMapping && gameIconMapping[record.gameType]}
            alt=""
            className="h-[12px] w-[12px]"
          />
          <span className="text-pxs/4 ">{record.gameName}</span>
        </div>
      ),
      className: "web font-medium text-sub-text text-left ml-[1.125rem] truncate"
    },
    {
      title: (
        <span className="text-pxs font-medium text-[--mobile-bets-title-color]">
          <FormattedMessage id="player" />
        </span>
      ),
      dataIndex: "player",
      key: "player",
      className: "web font-medium text-sub-text  ml-[1.125rem]",
      align: "center",
      width: "30%",
      ellipsis: true,
      render: (_, record: BetInfo) => (
        <div className="flex gap-x-1 items-center">
          <MyAvatar url={`/avatars/${record.headId}.webp`} />
          <span className="text-[#E9EAEC] font-medium text-pxs/4 ml-1">
            {hidePersonName(record.playerName)}
          </span>
        </div>
      )
    },
    {
      title: (
        <span className="text-pxs font-medium text-[--mobile-bets-title-color]">
          <FormattedMessage id="payout" />
        </span>
      ),
      dataIndex: "payout",
      key: "payout",
      className: "web font-medium text-sub-text text-left ml-[1.125rem]",
      align: "right",
      width: "40%",
      ellipsis: true,
      render: (_, record: BetInfo) => (
        <div
          className={`flex justify-end space-x-1 font-medium text-pxs/4 items-center rounded-lg`}
        >
          <span>{fiatSymbol}</span>
          <span
            className={`ml-1 ${
              record.payout >= 0 ? "text-[var(--main-theme-color)]" : "text-[#EE6300]"
            }`}
          >
            {converterPlus(
              record.payout || 0,
              record.currencyId,
              viewFiat,
              allConfigData?.exchangeRateList
            )}
          </span>
          <img
            loading="lazy"
            src={cronMap[record.currencyId]?.icon}
            className="h-[16px] w-[16px]"
            alt=""
          ></img>
        </div>
      )
    }
  ];

  const columns: TableProps<DataType>["columns"] = useMemo(() => {
    return [
      {
        title: (
          <span className="text-xs font-medium">
            <FormattedMessage id="game_details_1_1_3" />
          </span>
        ),
        dataIndex: "game",
        key: "game",
        align: "left",
        hidden: isLogin && gameId,
        render: (_, record: ProtoMessage.BetInfo) => (
          <div
            className="cursor-pointer flex gap-x-1 items-center"
            onClick={() => handleEnterGame(record)}
          >
            <img
              loading="lazy"
              src={gameIconMapping && gameIconMapping[record.gameType]}
              alt=""
              className="h-[24px] w-[24px]"
            />
            <span className="text-xs ">{record.gameName}</span>
          </div>
        ),
        className: "web font-medium text-sub-text text-left ml-[1.125rem]"
      },
      {
        title: (
          <span className="text-xs">
            <FormattedMessage id="bet_id" />
          </span>
        ),
        dataIndex: "bet_id",
        key: "bet_id",
        align: "left",
        hidden: !isLogin || !gameId,
        render: (_, record: BetInfo) => (
          <div>
            <span className="text-xs">{record.noteId}</span>
          </div>
        ),
        className: "web font-medium text-sub-text ml-[1.125rem]"
      },
      {
        title: (
          <span className="text-xs lowercase font-medium first-letter:uppercase">
            <FormattedMessage id="player" />
          </span>
        ),
        dataIndex: "player",
        key: "player",
        className: "web font-medium text-sub-text  ml-[1.125rem]",

        align: "left",
        render: (_, record: BetInfo) => (
          <div className="flex gap-x-1 items-center">
            <MyAvatar url={`/avatars/${record.headId}.webp`} />
            <span className="text-[#E9EAEC] text-xs ml-1">{hidePersonName(record.playerName)}</span>
          </div>
        )
      },
      {
        title: (
          <span className="text-xs lowercase font-medium first-letter:uppercase">
            <FormattedMessage id="game_details_1_1_6" />
          </span>
        ),
        dataIndex: "time",
        key: "time",
        className: "web font-medium text-sub-text text-left ml-[1.125rem]",

        align: "left",
        render: (_, record: BetInfo) => (
          <div>
            <span className="text-sub-text ml-1 text-xs">
              {formatDate2(parseInt(record.time), 4)}
            </span>
          </div>
        )
      },
      {
        title: (
          <span className="text-xs font-medium">
            <FormattedMessage id="bet_amount" />
          </span>
        ),
        dataIndex: "bet_amount",
        key: "bet_amount",
        className: "web font-medium text-sub-text ",
        align: "left",
        render: (_, record: BetInfo) => (
          <div>
            <div className={`flex text-primary-text  text-xs space-x-1 items-center justify-start`}>
              <img
                loading="lazy"
                src={cronMap[record.currencyId]?.icon}
                className="h-[20px] w-[20px]"
                alt=""
              ></img>
              <span>{fiatSymbol}</span>
              <span className={`ml-1`}>
                {converterPlus(
                  record.amount || 0,
                  record.currencyId,
                  viewFiat,
                  allConfigData?.exchangeRateList
                )}
              </span>
            </div>
          </div>
        )
      },
      {
        title: (
          <span className="text-xs font-medium">
            <FormattedMessage id="multiplier" />
          </span>
        ),
        dataIndex: "multiplier",
        key: "multiplier",
        className: "web font-medium text-sub-text",
        align: "left",
        render: (_, record: BetInfo) => (
          <div className={`flex text-primary-text text-xs space-x-1 items-center justify-start`}>
            <span>{formatNumberQfw(record.mul || 0, 2)}x</span>
          </div>
        )
      },
      {
        title: (
          <span className="text-xs font-medium">
            <FormattedMessage id="payout" />
          </span>
        ),
        dataIndex: "payout",
        key: "payout",
        className: "web font-medium text-sub-text text-left ml-[1.125rem]",
        align: "left",
        render: (_, record: BetInfo) => (
          <div className={`flex justify-start space-x-1 text-xs items-center rounded-lg`}>
            {/* <span className={`ml-1 ${record.payout >= 0 ? 'text-[var(--main-theme-color)]' : 'text-[#EE6300]'}`}>{formatNumberQfw(record.payout,4)}</span> */}
            <span>{fiatSymbol}</span>
            <span className={`ml-1 ${record.payout >= 0 ? "text-[#4af255]" : "text-[#EE6300]"}`}>
              {converterPlus(
                record.payout || 0,
                record.currencyId,
                viewFiat,
                allConfigData?.exchangeRateList
              )}
            </span>
            <img
              loading="lazy"
              src={cronMap[record.currencyId]?.icon}
              className="h-[20px] w-[20px]"
              alt=""
            ></img>
          </div>
        )
      }
    ];
  }, [
    allConfigData?.exchangeRateList,
    converterPlus,
    cronMap,
    fiatSymbol,
    formatDate2,
    formatNumberQfw,
    gameIconMapping,
    gameId,
    handleEnterGame,
    isLogin,
    viewFiat
  ]);

  const fetchData = useCallback(async () => {
    if (!selected || selected !== 3) return;
    
    try {
      setMyBetsLoading(true);
      const res = await getBetHistoryData(
        WalletMessage.ReqBetHistoryDataMessage.create({
          msgID: ProtoMessage.MID.ReqBetHistoryData,
          gameType: 0,
          assets: 0,
          past: 3,
          page: 1,
          pageSize: showCount.current,
          gameId: gameId ? Number(gameId) : null
        })
      );

      if (checkResponse(res)) {
        const betList = res.betList;
        setMyBetsList(betList);
        setFinallyShowData(betList?.slice(0, showCount.current));
      }
    } finally {
      setMyBetsLoading(false);
    }
  }, [gameId, checkResponse, selected]);

  const handlerClick = useCallback((index: number) => {
    setSelected(index);
  }, []);

  const handleChangePageSize = useCallback((value: number) => {
    showCount.current = value;
    const currentData = selected === 0 ? showData 
      : selected === 1 ? hignRoller 
      : selected === 2 ? luckyBets 
      : myBetsList;
    setFinallyShowData(currentData?.slice(0, value));
  }, [selected, showData, hignRoller, luckyBets, myBetsList]);

  useEffect(() => {
    const currentData = selected === 0 ? showData 
      : selected === 1 ? hignRoller 
      : selected === 2 ? luckyBets 
      : null;
      
    if (currentData) {
      setFinallyShowData(currentData?.slice(0, showCount.current));
    }
  }, [showData, selected, hignRoller, luckyBets]);

  useEffect(() => {
    if (selected == 3) {
      fetchData();
    }
  }, [selected, showCount.current]);

  const getRowKey = useCallback((record: BetInfo) => {
    return `${record.noteId}-${record.time}`;
  }, []);

  return (
    <div className="flex flex-col w-full relative ">
      <div className="flex items-center gap-x-2 ">
        <div className="flex justify-between lg:gap-x-2 gap-x-1 items-center">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.86523 11.8557C11.1162 11.1113 12.8344 12.9446 12.7256 15.4743C12.6103 18.1561 10.5495 20.3347 7.86523 20.3347C5.18102 20.3347 3.12014 18.1561 3.00486 15.4743C2.89612 12.9446 4.61433 11.1113 7.86523 11.8557ZM11.9176 11.8139C12.6999 11.2031 13.8612 10.9675 15.3655 11.3125C18.4627 10.6036 20.099 12.3512 19.9954 14.7603C19.886 17.3166 17.9217 19.3922 15.3655 19.3922C14.342 19.3962 13.3481 19.0496 12.5491 18.41C13.0927 17.5351 13.4 16.5342 13.4408 15.505C13.4992 14.1703 13.0996 12.9865 12.3191 12.1721C12.1944 12.0428 12.0602 11.9231 11.9176 11.8139ZM12.8503 3.03957C12.9669 2.86663 13.5518 3.30717 13.6888 3.46443C13.7546 3.54002 13.7876 3.63865 13.7807 3.73861C13.7737 3.83857 13.7273 3.93167 13.6517 3.99743C13.2666 4.33255 12.9012 4.67294 12.5516 5.01688C12.5508 5.02162 12.5511 5.02628 12.5501 5.03101C12.1989 6.711 13.2432 7.99144 14.2531 9.22964C14.6119 9.65006 14.9434 10.093 15.2455 10.5559C14.917 10.4923 14.584 10.4538 14.2496 10.4408C14.0695 10.2006 13.8705 9.95598 13.6676 9.70721C12.7801 8.61908 11.7976 7.40945 11.7302 5.86835C10.207 7.53509 9.09642 9.27974 8.44507 11.0255C8.25463 11.0497 8.06168 11.0802 7.86518 11.121C7.78443 11.1042 7.70484 11.0909 7.62505 11.0769C8.29942 9.11544 9.51531 7.15874 11.2202 5.30503C11.3913 5.12074 11.5496 4.92508 11.6942 4.71939C12.0796 4.16582 12.468 3.60632 12.8503 3.03957ZM13.8448 4.37874C14.1839 4.3241 16.5541 4.4804 17.5883 5.26028C18.849 6.21101 18.7993 6.5751 19.3214 9.07205C17.8747 8.82042 16.6811 8.74803 15.1214 7.36236C14.2766 6.60476 13.8095 5.51288 13.8448 4.37874Z"
              fill="url(#paint0_linear_474_45421)"
            />
            <defs>
              <linearGradient
                id="paint0_linear_474_45421"
                x1="11.5"
                y1="3"
                x2="11.5"
                y2="20.3347"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="var(--main-theme-color)" />
                <stop offset="1" stopColor="var(--main-theme-color)" />
              </linearGradient>
            </defs>
          </svg>

          <div className="lg:uppercase leading-[22px] lg:font-semibold font-bold">
            <FormattedMessage id="game_details_1_1_2" />
          </div>
        </div>

        <div className="flex ml-auto lg:rounded-xl lg:bg-[--bets-button-div-bg-color] h-full items-center lg:px-2">
          {buttons.map((button, index) => (
            <div
              onClick={() => handlerClick(index)}
              key={index}
              className={`${screenWidth > 1024 ? "" : "px-2"} ${
                selected === index ? "lg:bg-button-bg-green bg-[--button-bg-color]" : ""
              } 
                                lg:h-[36px] h-[auto]  overflow-clip flex items-center justify-center lg:rounded-xl rounded-[4px] cursor-pointer px-2 `}
            >
              <div
                className={`${
                  selected === index
                    ? "lg:text-sub-text-black text-primary-text  lg:font-semibold font-medium"
                    : "lg:text-sub-text text-[--2-step-bg-color]"
                } items-center justify-center lg:text-sm/4 text-pxs/5 text-nowrap `}
              >
                {button.indexOf("_") > -1 ? <FormattedMessage id={button} /> : button}
              </div>
            </div>
          ))}
        </div>

        {screenWidth > 1024 && (
          // <Suspense fallback={null}>
          <SelectStyleThirdType
            classNames=" !h-[44px] !rounded-xl !min-w-[68px] !p-0"
            defaultValue={pageOptions[0].value}
            onChange={handleChangePageSize}
            options={pageOptions}
          />
          // </Suspense>
        )}
      </div>
      <div className="w-full flex flex-col mt-[12px] px-3 bg-content-basic-gray rounded-lg py-1">
        {selected === 0 && (
          <Table
            rowKey={getRowKey}
            rowHoverable={false}
            pagination={false}
            columns={screenWidth <= 1024 ? mobileColumns : columns}
            dataSource={finallyShowData}
            className={`overflow-hidden 
                                        transition-all duration-300 ease-in-out ${
                                          screenWidth > 1024
                                            ? "bet-table_list"
                                            : "bet-table_list_mobile"
                                        }`}
            // style={{ maxHeight: `${hight}px` }}
            locale={{
              emptyText: (
                <div className="min-h-[436px] flex flex-col items-center justify-center gap-y-5">
                  <span>
                    <img src={noDataIcon} alt="" loading="lazy" />
                  </span>
                  <span className="text-[--node_data_text_color] text-lg ">
                    <FormattedMessage id="no_data" />
                  </span>
                </div>
              )
            }}
          />
        )}
        {selected === 1 && (
          <Table
            rowKey={getRowKey}
            rowHoverable={false}
            pagination={false}
            size="small"
            columns={screenWidth <= 1024 ? mobileColumns : columns}
            dataSource={finallyShowData}
            className={`overflow-hidden 
                                transition-all duration-300 ease-in-out ${
                                  screenWidth > 1024 ? "bet-table_list" : "bet-table_list_mobile"
                                }`}
            //  style={{ maxHeight: `${hight}px` }}
            locale={{
              emptyText: (
                <div className="min-h-[436px] flex flex-col items-center justify-center space-y-5">
                  <span>
                    <img src={noDataIcon} alt="" loading="lazy" />
                  </span>
                  <span className="text-[--node_data_text_color] text-lg ">
                    <FormattedMessage id="no_data" />
                  </span>
                </div>
              )
            }}
          />
        )}
        {selected === 2 && (
          <Table
            rowKey={getRowKey}
            rowHoverable={false}
            pagination={false}
            size="small"
            columns={screenWidth <= 1024 ? mobileColumns : columns}
            dataSource={finallyShowData}
            className={`overflow-hidden 
                                transition-all duration-300 ease-in-out ${
                                  screenWidth > 1024 ? "bet-table_list" : "bet-table_list_mobile"
                                }`}
            // style={{ maxHeight: `${hight}px` }}
            locale={{
              emptyText: (
                <div className="min-h-[436px] flex flex-col items-center justify-center space-y-5">
                  <span>
                    <img src={noDataIcon} alt="" loading="lazy" />
                  </span>
                  <span className="text-[--node_data_text_color] text-lg ">
                    <FormattedMessage id="no_data" />
                  </span>
                </div>
              )
            }}
          />
        )}
        {selected === 3 && (
          <Table
            rowKey={getRowKey}
            rowHoverable={false}
            loading={myBetsLoading}
            pagination={false}
            size="small"
            columns={screenWidth <= 1024 ? mobileColumns : columns}
            dataSource={finallyShowData}
            className={`overflow-hidden 
                                transition-all duration-300 ease-in-out ${
                                  screenWidth > 1024 ? "bet-table_list" : "bet-table_list_mobile"
                                }`}
            // style={{ maxHeight: `${hight}px` }}
            locale={{
              emptyText: (
                <div className="min-h-[436px] flex flex-col items-center justify-center space-y-5">
                  <span>
                    <img src={noDataIcon} alt="" loading="lazy" />
                  </span>
                  <span className="text-[--node_data_text_color] text-lg ">
                    <FormattedMessage id="no_data" />
                  </span>
                </div>
              )
            }}
          />
        )}
      </div>
    </div>
  );
};

export default memo(TransformList_bc);
