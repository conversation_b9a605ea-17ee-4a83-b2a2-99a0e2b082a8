import NoticeModal from "@/components/NoticeModal";
import { useUtils } from "@/components/useUtils";
import { BackStageMessage } from "@/protos/BackStage";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useJump } from "../context/useJumpContext";
import SliderPopup from "./SiderPopup";
import { PopuTriggerType } from "@/utils/enums";
import { useQueryConfigData } from "@/hooks/queryHooks/useQueryConfigData";
// const SliderPopup = lazy(() => import("./SiderPopup"));

const SmallPop: React.FC = () => {

  const {
    handleInnerJum,
    refreshPopupList,
    centerPopup,
    setCenterPopup,
    rightTopPopup,
    setRightTopPopup,
    rightDownPopup,
    setRightDownPopup
  } = useJump() || {};

  const wholeLoading = useSelector((state: any) => state.systemReducer.loading);

  const [smallPopupType, setSmallPopupType] = useState<number>();
  const [currentPopup, setCurrentPopup] = useState<BackStageMessage.IPopupInfo>();
  const [currentRTPopup, setCurrentRTPopup] = useState<BackStageMessage.IPopupInfo>();
  const [currentRDPopup, setCurrentRDPopup] = useState<BackStageMessage.IPopupInfo>();
  const [ofPopup, setOfPopup] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const navigate = useNavigate();
  const { delay, isLogin } = useUtils();
  const {data:allConfigData} = useQueryConfigData(0,1000 * 60 * 60 * 24)

  // 弹窗
  useEffect(() => {
    if (!isLogin && allConfigData?.PopupList?.length > 0) {
      refreshPopupList(PopuTriggerType.VISITOR);
    }
  }, [isLogin, allConfigData]);

  async function openRightTopPopupNotice(
    wholeLoading: boolean,
    popuList: BackStageMessage.IPopupInfo[]
  ) {
    if (!wholeLoading && popuList && popuList.length > 0) {
      const item = popuList[0];
      setCurrentRTPopup(item);
      setSmallPopupType(2);
    }
  }

  async function openRightDownPopupNotice(
    wholeLoading: boolean,
    popuList: BackStageMessage.IPopupInfo[]
  ) {
    if (!wholeLoading && popuList && popuList.length > 0) {
      const item = popuList[0];
      setCurrentRDPopup(item);
      setSmallPopupType(3);
    }
  }

  async function openNotice(wholeLoading: boolean, popuList: BackStageMessage.IPopupInfo[]) {
    if (!wholeLoading && popuList && popuList.length > 0) {
      const item = popuList[0];
      const fisrtPopup = item.popupDataList?.[0];
      if (fisrtPopup) {
        console.log("公告居中弹框", fisrtPopup, wholeLoading);
        // 1.功能弹窗 2.配置弹窗
        const popupType = fisrtPopup.popupType;
        if (popupType === 1) {
          //系统弹窗 1.任务 2.转盘 3.充值 4.客服
          const systemPopup = fisrtPopup.systemPopup;
          //弹框链接 1.任务 2.转盘 3.充值 4.客服（内连） 5.登录 6.注册
          const popupLinks = fisrtPopup.popupLinks;
          setOfPopup(true);
          handleInnerJum(systemPopup || popupLinks, item);
        } else {
          setShowModal(true);
          setCurrentPopup(item);
        }
      }
    }
  }

  useEffect(() => {
    console.log("刷新弹窗-中心弹框", centerPopup);

    if (centerPopup) {
      openNotice(wholeLoading, centerPopup);
    }
  }, [centerPopup, wholeLoading]);

  useEffect(() => {
    if (rightTopPopup) {
      openRightTopPopupNotice(wholeLoading, rightTopPopup);
    }
  }, [rightTopPopup, wholeLoading]);

  useEffect(() => {
    if (rightDownPopup) {
      openRightDownPopupNotice(wholeLoading, rightDownPopup);
    }
  }, [rightDownPopup, wholeLoading]);

  const handleJump = (popupInfo: BackStageMessage.IPopupData) => {
    console.log("跳转到内部链接", popupInfo);
    // 1.内部跳转 2.外部跳转
    const jumpType = popupInfo.jumpType;
    // 内部跳转链接
    const innerLinks = popupInfo.innerLinks;
    if (jumpType === 2) {
      window.open(popupInfo?.externalLinks, "_blank");
    } else if (innerLinks) {
      navigate(innerLinks);
    } else {
      handleInnerJum(popupInfo.popupLinks, popupInfo);
    }
  };

  async function handleClose(type: number) {
    if (type === 1) {
      setShowModal(false);
      await delay(200);
      setCenterPopup((pre) => pre?.slice(1));
    } else if (type === 2) {
      await delay(200);
      setRightTopPopup((pre) => pre?.slice(1));
    } else if (type === 3) {
      await delay(200);
      setRightDownPopup((pre) => pre?.slice(1));
    }
    console.log("关闭弹窗", rightDownPopup);
  }

  return (
    <>
      <NoticeModal
        handleJump={handleJump}
        showModal={showModal}
        handleCancel={() => handleClose(1)}
        currentPopup={currentPopup}
      />
      {currentRTPopup && (
        // <Suspense fallback={}>
        <SliderPopup
          smallPopup={currentRTPopup}
          smallPopupType={2}
          handleClose={handleClose}
          handleJump={handleJump}
        />
        // </Suspense>
      )}
      {currentRDPopup && (
        // <Suspense fallback={}>
        <SliderPopup
          smallPopup={currentRDPopup}
          smallPopupType={3}
          handleClose={handleClose}
          handleJump={handleJump}
        />
        // </Suspense>
      )}
    </>
  );
};

export default SmallPop;
