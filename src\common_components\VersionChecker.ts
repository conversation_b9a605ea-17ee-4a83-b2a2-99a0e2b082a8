import { Modal } from "antd";
import { useIntl } from "react-intl";
import { useEffect, useRef } from "react";
import "./VersionChecker.css";

const VERSION_KEY = 'app_version';
const CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟检查一次

export const VersionChecker = () => {
  const intl = useIntl();
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const isModalVisible = useRef<boolean>(false);

  const checkVersion = async () => {
    try {
      // 通过请求获取最新版本号
      const response = await fetch('/version.json?' + new Date().getTime());
      const data = await response.json();
      return data.version;
    } catch (error) {
      console.error('Failed to fetch version:', error);
      return null;
    }
  };

  const handleNewVersion = (newVersion: string) => {
    isModalVisible.current = true;
    Modal.confirm({
      centered: true,
      icon: null,
      title: intl.formatMessage({ id: 'new_version_prompt' }),
      content: intl.formatMessage({ id: 'new_version_prompt_content' }),
      okText: intl.formatMessage({ id: 'immediately_refresh' }),
      cancelText: intl.formatMessage({ id: 'later_remind' }),
      className: 'custom-version-modal',
      maskStyle: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
      },
      okButtonProps: {
        className: 'mobile_btn custom-ok-button'
      },
      cancelButtonProps: {
        className: 'custom-cancel-button'
      },
      onOk: () => {
        isModalVisible.current = false;
        if (newVersion) {
          localStorage.setItem(VERSION_KEY, newVersion);
        }
        localStorage.removeItem('gameChannelMapping');
        localStorage.removeItem('persist:root');
        localStorage.removeItem('queryList');
        localStorage.removeItem('gameChannelData');
        localStorage.removeItem('locale');
        window.location.reload();
      },
      onCancel: () => {
        isModalVisible.current = false;
        // 重新开始检查
        if (!timerRef.current) {
          timerRef.current = setInterval(checkAndUpdateVersion, CHECK_INTERVAL);
        }
      }
    });
  };

  const checkAndUpdateVersion = async () => {
    // 如果弹框正在显示，不进行检查
    if (isModalVisible.current) return;
    
    const newVersion = await checkVersion();
    if (!newVersion) return;
    
    const storedVersion = localStorage.getItem(VERSION_KEY);
    if (!storedVersion) {
        localStorage.setItem(VERSION_KEY, newVersion);
    } else if (storedVersion !== newVersion) {
        handleNewVersion(newVersion);
    }
  };

  useEffect(() => {
    // 初始化检查
    checkAndUpdateVersion();

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // 设置定时检查
    timerRef.current = setInterval(checkAndUpdateVersion, CHECK_INTERVAL);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  return null;
};