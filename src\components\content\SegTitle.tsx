import category0 from '@/assets/content/category1-icon-0.svg'
import category1 from '@/assets/content/category1-icon-1.svg'
import category2 from '@/assets/content/category2-icon-0.svg'
import category3 from '@/assets/content/category2-icon-1.svg'
import hotGame from '@/assets/content/hotGame.svg'
import hotGame2 from '@/assets/content/hotGame2.svg'
import live from '@/assets/content/live.svg'
import live2 from '@/assets/content/live2.svg'
import newGame from '@/assets/content/newGame.svg'
import newGame2 from '@/assets/content/newGame2.svg'
import poplar from '@/assets/content/poplar.svg'
import poplar2 from '@/assets/content/poplar2.svg'
import { Segmented } from 'antd'
import { useState } from 'react'
import { FormattedMessage } from 'react-intl'

const SegTitle: React.FC = () => {
    const options = []
    const datas = [
        {
            id: 1,
            label: <FormattedMessage id="lobby" />,
            hoverOrSelectedImg: category1,
            defaultImg: category0,
        },
        {
            id: 2,
            label: <FormattedMessage id="slots" />,
            hoverOrSelectedImg: category3,
            defaultImg: category2,
        },
        {
            id: 3,
            label: <FormattedMessage id="popular" />,
            hoverOrSelectedImg: poplar,
            defaultImg: poplar2,
        },
        {
            id: 4,
            label: <FormattedMessage id="hot_game" />,
            hoverOrSelectedImg: hotGame2,
            defaultImg: hotGame,
        },
        {
            id: 5,
            label: <FormattedMessage id="live_casino" />,
            hoverOrSelectedImg: live2,
            defaultImg: live,
        },
        {
            id: 6,
            label: <FormattedMessage id="new" />,
            hoverOrSelectedImg: newGame2,
            defaultImg: newGame,
        }
    ]
    const [selectedOption, setSelectedOption] = useState(0);
    const [hoverIndex, setHoverIndex] = useState(-1);

    const handleOptionChange = (value: string) => {
        setSelectedOption(Number(value));
    };

    const handleMouseEnter = (index: number) => {
        setHoverIndex(index);
    };

    const handleMouseLeave = () => {
        setHoverIndex(-1);
    };

    function handlerClick(index: number) {
        console.log('click', index);
    }

    for (let i = 0; i < datas.length; i++) {
        const data = datas[i]
        const option = {
            label: (
                <div key={data.id} 
                    onMouseEnter={() => handleMouseEnter(data.id)}
                    onMouseLeave={handleMouseLeave}
                    onClick={() => handlerClick(data.id)}
                >
                    <div className='flex flex-col items-center justify-center text-center select-none w-[62px]'>
                        {
                            hoverIndex === data.id || selectedOption === data.id ?
                                <img src={data.hoverOrSelectedImg} alt="icon" /> :
                                <img src={data.defaultImg} alt="icon" />
                        }
                        <div>{data.label}</div>
                    </div>

                </div>
            ),
            value: data.id.toString()
        }
        options.push(option)
    }

    return (
        <div className='w-full overflow-x-auto items-center justify-center'>
            <Segmented
                defaultValue='1'
                options={options}
                onChange={handleOptionChange}
                size='middle'
            />
        </div>

    )
}

export default SegTitle;