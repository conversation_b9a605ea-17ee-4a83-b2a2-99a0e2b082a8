import first_1Icon from "@/assets/firstDeposit/1.webp";
import first_2Icon from "@/assets/firstDeposit/2.webp";
import first_3Icon from "@/assets/firstDeposit/3.webp";
import { usePoint } from "@/common_components/usePoint";
import ScreenSpin from "@/components/ScreenSpin2";
import { useUtils } from "@/components/useUtils";
import { useQueryActivityData } from "@/hooks/queryHooks/useQueryActivityData";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { ActivityMessage } from "@/protos/Activity";
import { ProtoMessage } from "@/protos/common";
import { getFirstChargeSignInMessage } from "@/service/activity";
import { FirstChargeReward, ResFirstChargeSignInMessage } from "@/types/activity";
import { DItemShow } from "@/types/common";
import { ShareType } from "@/utils/enums";
import { memo, useCallback, useEffect, useImperativeHandle, useState } from "react";
import { FormattedMessage } from "react-intl";
import CommonRewardModal from "../CommonRewardModal";

const FirstDepositContent = ({ childrenRef, callback }) => {
  const { cronMap, hanldeMask } = useUtils();
  const { intl, Toast, checkResponse } = useToastUtils();
  const { activityPoint } = usePoint();
  const [showRewardModal, setShowRewardModal] = useState(false);
  const [reward, setReward] = useState<DItemShow[]>();
  const {data, isLoading: loading, refetch:refetchActivityData} = useQueryActivityData(1000 * 10, false)

  const handleSignIn = useCallback(async () => {
    try {
      const res = await getFirstChargeSignInMessage(
        ActivityMessage.ReqFirstChargeSignInMessage.create({
          msgID: ProtoMessage.MID.ReqFirstChargeSignIn,
          day: data?.firstChargeSignInInfo.currDay
        })
      ) as ResFirstChargeSignInMessage;
      console.log("返回首充签到", res);
      if (checkResponse(res)) {
        setShowRewardModal(true);
        const rewards = []
        rewards.push(res.rewardShow)
        setReward(rewards);
        Toast(intl.formatMessage({ id: "you_can_withdraw_now" }));
        // 领取奖励后再次触发刷新活动数据
        refetchActivityData()
      }
    } finally {
      hanldeMask(false)
    }
  }, [data]);

  useImperativeHandle(childrenRef, () => ({
    handleSignIn
  }));

  useEffect(() => {
    if (data?.firstChargeSignInInfo) {

      activityPoint(data?.firstChargeSignInInfo?.activityId?.toString(), data?.firstChargeSignInInfo?.c_Id?.toString());
      const timer = setTimeout(() => {
        const itemElement = document.getElementById(`${data?.firstChargeSignInInfo.currDay}`);
        if (itemElement) {
          itemElement.scrollIntoView({ behavior: "smooth", block: "center" });
        }
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [data]);

  const getDivData = useCallback(
    (item: FirstChargeReward) => {
      // 当前应领取天数
      const currDay = data?.firstChargeSignInInfo.currDay ?? 0;
      // 已领取天数
      const receiveDay = data?.firstChargeSignInInfo.receiveDay ?? [];
      // 当前天数
      const day = item.id ?? 0;
      let bgColor = "#2E323A";
      if (currDay == day) {
        bgColor = "#FFD25F";
      } else if (receiveDay.includes(day)) {
        bgColor = "rgba(255, 210, 95, 0.10)";
      }
      const textColor = currDay == item.id ? "black" : "white";
      let icon = first_2Icon;
      if (currDay == item.id) {
        icon = first_1Icon;
      } else if (receiveDay.includes(day)) {
        icon = first_3Icon;
      }

      return (
        <div
          id={item.id.toString()}
          className="px-[18.5px] py-[6.3px] w-full h-full rounded-[9.7px] text-[13px] relative flex flex-col gap-y-[9.7px]"
          style={{ backgroundColor: bgColor, color: textColor }}
        >
          <div className="flex items-center gap-x-1  justify-center">
            <span>
              <FormattedMessage id="day" />
            </span>
            <span>{item.id}</span>
          </div>
          <div className="h-[40px]"></div>
          <img
            src={icon}
            alt=""
            className="w-[74px] absolute top-[50%] left-[50%] -translate-x-[50%] -translate-y-[50%]"
          />
          <span className="flex items-center gap-x-1 justify-center">
            <span>{cronMap[item?.currencyId]?.symbol}</span>
            <span>{item.rewardAmount}</span>
          </span>
        </div>
      );
    },
    [data]
  );

  return (
    <>
      <CommonRewardModal
        showModal={showRewardModal}
        handleCancel={() => {
          setShowRewardModal(false);
          callback();
        }}
        rewardData={reward || []}
        activityId={ShareType.firstDepositSign.id}
        activityName={ShareType.firstDepositSign.name}
      >
      </CommonRewardModal>
      <div
        className="h-full w-full grid grid-cols-3 gap-x-[16px] gap-y-[10px] relative overflow-y-auto"
        style={{ fontFamily: "Inter" }}
      >
        <ScreenSpin loading={loading} />
        {data?.firstChargeSignInInfo?.firstChargeRewards.map((item) => (
          <div key={item.id} className="flex flex-col h-[100px] w-[88px]">
            {getDivData(item)}
          </div>
        ))}
      </div>
    </>
  );
};

export default memo(FirstDepositContent);
