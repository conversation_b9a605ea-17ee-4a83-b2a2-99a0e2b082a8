
import styles from '@/css/Button3d.module.css';
import styles_gray from '@/css/Button3d_gray.module.css';

const Button_3d: React.FC = ({ text, onClickEvent, textClass, width='w-full', myColor="default"}) => {
    let buttonClassName = `  my3dButton ${width}  ${styles.my3dButton} ${textClass}`;
    if (myColor === "gray") {
        buttonClassName  = `my3dButton ${width}  ${styles_gray.my3dButton} ${textClass}`;
    }
    
    return (
        <button className={buttonClassName} onClick={onClickEvent}>
            <i className="fa fa-cart-plus"></i>{text}
        </button>
    )
}

export default Button_3d