import rightArrowIcon from "@/assets/rightArrowWhite.svg";
import { Image } from "antd";
import { FormattedMessage } from "react-intl";
import { useLocation, useNavigate } from "react-router-dom";
import { useScreenWidth } from "../../utils/MobileDetectionContext";
import { useCallback } from "react";
import { safeTrackClick } from "@/utils/manualTrack";

const SwipeTopControl: React.FC = ({
  isSvg = false,
  isFirstPage,
  isLastPage,
  sctionId,
  IconBanner,
  prev,
  next,
  text,
  showAllGames = true,
  viewAll,
  modal = 1,
  total
}) => {
  const { screenWidth } = useScreenWidth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleViewAll = useCallback(
    (sctionId: string) => {
      safeTrackClick('ViewAllButton', 'button');
      
      console.log("view all", sctionId);
      if (sctionId) {
        if (modal == 1) {
          navigate(`/gameList/${sctionId}/${text}`, { state: { from: location } });
        } else {
          navigate(`/casino/${sctionId}/true`)
        }
      }
    },
    [modal]
  );

  return (
    <>
      <div className="flex flex-col select-none w-full h-[32px] justify-center ">
        <div className="flex gap-[6px] overflow-visible h-[32px] w-full">
          <div className="flex items-center gap-x-2 w-1/2">
            {isSvg ? (
              IconBanner
            ) : (
              <Image
                width={24}
                loading="lazy"
                preview={false}
                decoding="async"
                src={IconBanner}
                className="h-[24px]"
              />
            )}

            <div className="lg:text-base text-sm lg:font-semibold font-bold select-none tracking-wider lg:uppercase overflow-hidden">
              <span className="overflow-hidden text-white tex-[18px] font-[900]">
                {text}
              </span>
            </div>
          </div>
          <div className="flex ml-auto gap-x-2 items-center justify-end w-1/2">
            {viewAll && screenWidth > 1024 && (
              <span
                onClick={() => handleViewAll(sctionId)}
                className=" h-full w-[56px] justify-center items-center div-with-shadow bg-live-winner-button-bg cursor-pointer rounded-[10px] flex gap-x-1"
              >
                <span>
                  <FormattedMessage id="all" />
                </span>
                <Image
                  loading="lazy"
                  decoding="async"
                  preview={false}
                  src={rightArrowIcon}
                  alt=""
                />
              </span>
            )}
            {viewAll && screenWidth <= 1024 && (
              <span
                onClick={() => handleViewAll(sctionId)}
                style={{
                  backgroundColor: "#28223D"
                }}
                className=" h-[24px] px-[6px] justify-center items-center cursor-pointer text-pxs rounded-[8px] font-medium flex gap-x-1 text-nowrap"
              >
                <span  style={{color: "rgba(255, 255, 255, 0.70)"}}>
                  <FormattedMessage id="view_all" />
                </span>
                <span className="text-primary-text">
                  {total || 0}
                </span>
              </span>
            )}
            <button
              disabled={isFirstPage}
              onClick={prev}
              className={`${
                isFirstPage && " cursor-not-allowed"
              } `}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M15 6L9 12L15 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <button
              disabled={isLastPage}
              className={`flex items-center justify-center ${
                isLastPage && " !cursor-not-allowed"
              } w-[32px] h-full rounded-full group active:translate-y-[1px] transition-all duration-100  overflow-hidden`}
              onClick={next}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M9 6L15 12L9 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default SwipeTopControl;
