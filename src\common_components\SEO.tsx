import { useConfigData } from "@/components/games/providers/ProjectConfigProvider";
import { LoginMessage } from '@/protos/Login';
import { useMemo } from "react";
import { Helmet } from "react-helmet-async";

const SEO = () => {
    const { webSiteData } = useConfigData() as {
        webSiteData: LoginMessage.WebSiteData
    }
    // 使用 useMemo 生成 SEO 数据，避免不必要的重新计算
    const seoData = useMemo(() => {
        if (!webSiteData) {
            return null;
        }

        return {
            title: webSiteData.ogTile || "Default Title",
            keywords: webSiteData.keywords || "default, keywords",
            description: webSiteData.introduce || "Default description",
            image: webSiteData.ogImage || "default-image.jpg",
            ogDescription: webSiteData.ogDescription || "Default OG description",
            siteName: webSiteData.siteName || "Default Site Name",
        };
    }, [webSiteData]);

    // 如果没有有效数据，避免渲染空 Helmet 标签
    if (!seoData) {
        return null;
    }

    const { title, keywords, description, image, ogDescription, siteName } = seoData;
    console.log('获取到seo数据', seoData);
    
    return (
        <Helmet>
            <title>{title}</title>
            <meta name="keywords" content={keywords} />
            <meta name="description" content={description} />
            <meta property="og:title" content={title} />
            <meta property="og:image" content={image} />
            <meta property="og:description" content={ogDescription} />
            <meta property="og:site_name" content={siteName} />
        </Helmet>
    )
}

export default SEO;