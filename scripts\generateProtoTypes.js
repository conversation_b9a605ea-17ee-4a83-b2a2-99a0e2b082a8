//生产对应d.ts 修改命令 "generate:protos": "node scripts/generateProtoTypes.js && npm run proto:activity && npm run tsType:activity && npm run proto:affiliate && npm run tsType:affiliate && npm run proto:backStage && npm run tsType:backStage && npm run proto:billing && npm run tsType:billing && npm run proto:hall && npm run tsType:hall && npm run proto:inbox && npm run tsType:inbox && npm run proto:login && npm run tsType:login && npm run proto:quest && npm run tsType:quest && npm run proto:wallet && npm run tsType:wallet && npm run proto:tcp && npm run tsType:tcp && npm run proto:common && npm run tsType:common"
// scripts/generateProtoTypes.js
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 定义 protoFiles 数组，移除 CommonMessage.proto
const protoFiles = [
    { name: "activity", files: ["ActivityMessage.proto"] },
    { name: "affiliate", files: ["AffiliateMessage.proto"] },
    { name: "backStage", files: ["BackStageMessage.proto"] },
    { name: "billing", files: ["BillingMessage.proto"] },
    { name: "hall", files: ["HallMessage.proto"] },
    { name: "inbox", files: ["InboxMessage.proto"] },
    { name: "login", files: ["LoginMessage.proto"] },
    { name: "quest", files: ["QuestMessage.proto"] },
    { name: "wallet", files: ["WalletMessage.proto"] },
    { name: "tcp", files: ["TcpMessage.proto"] }
];

const protoDir = path.join(__dirname, "../src/protos");
const outputDir = path.join(__dirname, "../src/types");

// 确保输出目录存在
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

if (!fs.existsSync(protoDir)) {
    console.error(`Proto directory not found: ${protoDir}`);
    process.exit(1);
}

// 定义 parseField 函数
function parseField(line) {
    // 提取注释
    const commentMatch = line.match(/\/\/\s*(.+)$/);
    const comment = commentMatch ? commentMatch[1].trim() : "";

    // 移除注释部分
    const lineWithoutComment = line.split("//")[0].trim();
    if (!lineWithoutComment) return null;

    // 分割字段定义
    const parts = lineWithoutComment.split(/\s+/);
    if (parts.length < 3) return null;

    let type = parts[0];
    let name = parts[1];
    let number = parts[2];

    // 处理 repeated 字段
    let isRepeated = false;
    if (type === "repeated") {
        isRepeated = true;
        type = parts[1];
        name = parts[2];
        number = parts[3];
    }

    // 移除字段名中的等号和数字
    name = name.split("=")[0];

    // 类型映射
    let tsType;
    if (type === "int32" || type === "int64" || type === "double") {
        tsType = "number";
    } else if (type === "string") {
        tsType = "string";
    } else if (type === "bool") {
        tsType = "boolean";
    } else {
        tsType = type;
    }

    // 如果是 repeated 字段，添加数组类型
    if (isRepeated) {
        tsType = `${tsType}[]`;
    }

    return { name, tsType, comment };
}

// 获取 common 中定义的所有类型
function getCommonTypes() {
    const commonFiles = ["MIDMessage.proto", "CommonMessage.proto"];
    const commonTypes = new Set();

    commonFiles.forEach((file) => {
        if (fs.existsSync(path.join(protoDir, file))) {
            const content = fs.readFileSync(path.join(protoDir, file), "utf8");
            const messageMatches = content.match(/message\s+(\w+)\s*{/g) || [];
            messageMatches.forEach((msg) => {
                const typeName = msg.match(/message\s+(\w+)\s*{/)[1];
                commonTypes.add(typeName);
            });
        }
    });

    return commonTypes;
}

// 检查字段类型是否来自 common
function isCommonType(type, commonTypes) {
    // 移除数组标记
    const baseType = type.replace("[]", "");
    // 检查是否是基本类型
    if (["number", "string", "boolean"].includes(baseType)) {
        return false;
    }
    // 检查是否在 common 类型集合中
    return commonTypes.has(baseType);
}

// 修改 processFile 函数，添加类型收集功能
function processFile(file, messages, usedTypes = new Set(), commonTypes) {
    const content = fs.readFileSync(path.join(protoDir, file), "utf8");
    const messageMatches = content.match(/message\s+(\w+)\s*{([^}]+)}/g) || [];

    messageMatches.forEach((msg) => {
        const [_, msgName, fields] = msg.match(/message\s+(\w+)\s*{([^}]+)}/) || [];

        // 跳过以 Req 开头的消息
        if (msgName.startsWith("Req")) {
            return;
        }

        const fieldLines = fields
            .split("\n")
            .map((line) => line.trim())
            .filter((line) => line && !line.startsWith("//"));

        const fieldTypes = fieldLines
            .map((line) => {
                const field = parseField(line);
                if (!field) return null;

                // 收集使用的 common 类型
                if (isCommonType(field.tsType, commonTypes)) {
                    const baseType = field.tsType.replace("[]", "");
                    usedTypes.add(baseType);
                }

                return `    ${field.name}: ${field.tsType};${field.comment ? ` // ${field.comment}` : ""}`;
            })
            .filter(Boolean)
            .join("\n");

        messages.push(`export interface ${msgName} {
${fieldTypes}
}`);
    });

    return usedTypes;
}

// 生成通用类型定义
function generateCommonTypes() {
    const commonFiles = ["MIDMessage.proto", "CommonMessage.proto"];
    const messages = [];
    const commonTypes = new Set();

    commonFiles.forEach((file) => {
        if (fs.existsSync(path.join(protoDir, file))) {
            processFile(file, messages, new Set(), commonTypes);
        }
    });

    const dtsContent = `// Common type definitions
${messages.join("\n\n")}`;

    const dtsOutputPath = path.join(outputDir, "common.d.ts");
    fs.writeFileSync(dtsOutputPath, dtsContent);
    console.log(`Successfully generated common.d.ts in ${outputDir}`);
}

// 修改 generateModuleTypes 函数
function generateModuleTypes() {
    // 获取 common 中定义的所有类型
    const commonTypes = getCommonTypes();

    protoFiles.forEach(({ name, files }) => {
        try {
            const missingFiles = files.filter((file) => !fs.existsSync(path.join(protoDir, file)));
            if (missingFiles.length > 0) {
                console.error(`Missing proto files for ${name}: ${missingFiles.join(", ")}`);
                return;
            }

            const messages = [];
            const usedTypes = new Set();

            files.forEach((file) => {
                processFile(file, messages, usedTypes, commonTypes);
            });

            // 生成实际的导入语句
            const importStatement =
                usedTypes.size > 0
                    ? `import { ${Array.from(usedTypes).join(", ")} } from './common';`
                    : "// No common types used";

            const dtsContent = `${importStatement}

${messages.join("\n\n")}`;

            const dtsOutputPath = path.join(outputDir, `${name}.d.ts`);
            fs.writeFileSync(dtsOutputPath, dtsContent);

            console.log(`Successfully generated ${name}.d.ts in ${outputDir}`);
        } catch (error) {
            console.error(`Error processing ${name}:`, error.message);
        }
    });
}

// 主执行流程
try {
    // 先生成通用类型定义
    generateCommonTypes();
    // 再生成各模块的类型定义
    generateModuleTypes();
} catch (error) {
    console.error("Error in type generation:", error);
    process.exit(1);
}
