// usePlayAudio.ts
import newMsgMp3 from "@/assets/audio/newMsg.mp3";
import useCustmerDebounce from "@/hooks/useCustmerDebounce";
import { useEffect, useState } from "react";

// 单例模式：全局唯一的 AudioContext 和缓存
let globalAudioContext: AudioContext | null = null;
const globalAudioBuffers = new Map<string, AudioBuffer>();
// 全局音频状态
let globalIsAudioEnabled = false;

const usePlayNewMessage = () => {
  // 使用全局状态，但保持响应式
  const [isAudioEnabled, setIsAudioEnabled] = useState<boolean>(globalIsAudioEnabled);

  // 初始化音频（用户点击时调用）
  const initAudio = async () => {
    if (globalAudioContext) return true; // 避免重复初始化

    try {
      globalAudioContext = new (window.AudioContext || window.webkitAudioContext)();
      
      // 预加载所有音频
      const audioList = [
        { name: 'newMsg', url: newMsgMp3 },
      ];

      await Promise.all(
        audioList.map(async ({ name, url }) => {
          const response = await fetch(url);
          const buffer = await globalAudioContext!.decodeAudioData(await response.arrayBuffer());
          globalAudioBuffers.set(name, buffer);
        })
      );

      // 标记已授权并持久化
      globalIsAudioEnabled = true;
      setIsAudioEnabled(true);
      return true;
    } catch (err) {
      console.error('音频初始化失败:', err);
      return false;
    }
  };

  // 播放音频（Web Audio API）
  const playAudio = (audioName: string) => {
    if (!globalIsAudioEnabled || !globalAudioContext || !globalAudioBuffers.has(audioName)) {
      return false;
    }

    const buffer = globalAudioBuffers.get(audioName)!;
    const source = globalAudioContext.createBufferSource();
    source.buffer = buffer;
    source.connect(globalAudioContext.destination);
    source.start(0);
    return true;
  };

  useEffect(() => {
    const handleClick = () => {
        console.log("全局点击事件监听");
        initAudio();
    };
    
    document.addEventListener("click", handleClick);
    
    return () => {
        document.removeEventListener("click", handleClick);
    };
  }, []);

  const playNewMessage = useCustmerDebounce(() => playAudio('newMsg'), 3000)

  return {
    isAudioEnabled,
    initAudio,
    playNewMessage: playNewMessage, // 快捷方法
  };
};

export default usePlayNewMessage;