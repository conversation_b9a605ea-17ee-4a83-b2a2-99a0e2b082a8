// import IconBanner from "@/assets/content/icon-banner-title.svg";
// import checkInIcon from '@/assets/leftSider/menuCheckIn.svg';
import checkInIcon from "@/assets/leftSider/menuCheckIn.webp";
// import spinIcon from "@/assets/leftSider/menuSpin.svg";
import spinIcon from "@/assets/leftSider/menuSpin.webp";
import { setActivitySource } from "@/store/popup";
import { useEffect, useRef, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useScreenWidth } from "../../utils/MobileDetectionContext";
import { useAllGame } from "../games/AllGameProvider";
import { useConfigData } from "../games/providers/ProjectConfigProvider";
import { useUtils } from "../useUtils";
import { FuntionOpenMapping } from '@/utils/enums';
import EventBus from "@/utils/EventBus";
import { ModalType } from "@/utils/enums";

const MobileQuestAndSpin: React.FC = () => {
  const { screenWidth } = useScreenWidth();
  const [bannerData, setBannerData] = useState<BannerInfo[] | null>(null);
  const sliderRef = useRef(null);
  const navigate = useNavigate();
  const [showCount, setShowCount] = useState(18);
  const { getCurrentLangData } = useUtils();
  const dispatch = useDispatch();

  const { functionSwitchIds } = useConfigData() as { functionSwitchIds: number[] };
  const { allConfigData } = useAllGame() as {
    allConfigData: ResConfigDataMessage;
  };
  const divRef = useRef(null);
  const intl = useIntl();
  const next = () => {
    sliderRef.slickNext();
  };
  const prev = () => {
    sliderRef.slickPrev();
  };
  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: showCount,
    slidesToScroll: showCount,
    arrows: false,
    lazyLoad: true,
    autoplay: true,
    autoplaySpeed: 5000
  };

  useEffect(() => {
    if (divRef && divRef.current) {
      const totalWidth = divRef.current.offsetWidth;
      setShowCount(Math.round(totalWidth / 390));
    }
  }, [screenWidth]);

  useEffect(() => {
    console.log("allConfigData", allConfigData);
    const currentLangu = getCurrentLangData().paramCode;
    if (allConfigData && allConfigData.bannerList) {
      setBannerData(
        allConfigData?.bannerList.filter((item) => item.index == 2 && item.language == currentLangu)
      );
    }
  }, [allConfigData]);

  const handleButtonClick = (banner: BannerInfo) => {
    // 1.内连 2.外链
    const jumpType = banner.jumpType;
    if (jumpType == 1) {
      navigate(banner.url);
    } else {
      window.open(banner.url, "_blank");
    }
  };

  const handleOpenSpinModal = () => {
    EventBus.emit("showModal", ModalType.showSpinModal);
  };

  return (
    <>
      {(functionSwitchIds?.includes(FuntionOpenMapping.check_in) ||
        functionSwitchIds?.includes(FuntionOpenMapping.Luck_Spin)) && (
        <>
          <div className="w-full grid grid-cols-[repeat(auto-fit,minmax(48%,1fr))] justify-between gap-x-[4%]">
            {/* {functionSwitchIds?.includes(FuntionOpenMapping.Quest) && (
              <div
                onClick={() => handleOpenQuestModal()}
                className="h-[38px] bg-gradient-to-b w-full from-[#7147E8] to-[#C332E7] rounded-xl flex gap-x-1 items-center justify-center"
              >
                <img src={questIcon} alt="" />
                <span className="text-lg font-medium">
                  <FormattedMessage id="quest" />
                </span>
              </div>
            )} */}
            {functionSwitchIds?.includes(FuntionOpenMapping.check_in) && (
              <div
                onClick={() => {
                  dispatch(setActivitySource(6));
                  EventBus.emit("showModal", ModalType.showCheckInModal);
                }}
                className="h-[38px] bg-gradient-to-b w-full from-[#7147E8] to-[#C332E7] rounded-xl flex gap-x-1 items-center justify-center"
              >
                <img src={checkInIcon} alt="" className="w-[30px]" />
                <span className="text-lg font-medium">
                  <FormattedMessage id="check_in" />
                </span>
              </div>
            )}

            {functionSwitchIds?.includes(FuntionOpenMapping.Luck_Spin) && (
              <div
                onClick={() => {
                  dispatch(setActivitySource(6));
                  handleOpenSpinModal();
                }}
                className="h-[38px] bg-gradient-to-b w-full from-[#E8D847] to-[#E7325E] rounded-xl flex gap-x-1 items-center justify-center"
              >
                <img src={spinIcon} alt="" className="w-[30px]" />
                <span className="text-lg font-medium">
                  <FormattedMessage id="spin" />
                </span>
              </div>
            )}
          </div>
        </>
      )}
    </>
  );
};

export default MobileQuestAndSpin;
