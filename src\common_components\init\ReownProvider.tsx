import { createAppKit } from "@reown/appkit/react";

import "./reown-style.css"
import { WagmiProvider } from "wagmi";
//  bsc--币安、tron-波场、mainnet--以太坊主网
import { bsc, tron, mainnet } from "@reown/appkit/networks";
import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";

// 1. Get projectId from https://cloud.reown.com
const projectId = "ea3730270ebf1635ab4f80e53bf27b36";

// 2. Create a metadata object - optional
const metadata = {
  name: "appkit-wingame",
  description: "AppKit Example",
  url: "https://reown.com/appkit", // origin must match your domain & subdomain
  icons: ["https://assets.reown.com/reown-profile-pic.png"]
};

// 3. Set the networks
const networks = [mainnet, bsc, tron];

// 4. Create Wagmi Adapter
const wagmiAdapter = new WagmiAdapter({
  networks,
  projectId,
  ssr: true
});

// 5. Create modal
createAppKit({
  adapters: [wagmiAdapter],
  networks,
  projectId,
  metadata,
  features: {
    analytics: true, // Optional - defaults to your Cloud configuration
    email: false,
    socials: false
  },
  themeVariables: {
    "--w3m-z-index": 9999
  }
});

const ReownProvider = ({ children }: { children: React.ReactNode }) => {
  
  return <WagmiProvider config={wagmiAdapter.wagmiConfig}>
      {children}
  </WagmiProvider>;
};

export default ReownProvider;
