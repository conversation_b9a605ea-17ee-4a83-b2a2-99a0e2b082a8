import searchIcon from "@/assets/search.svg";
import { searchGameData } from "@/service/hall";
import { setOpenTopSearch } from "@/store";
import { ConfigProvider } from "antd";
import { Input } from "antd";
import { useEffect, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useDispatch } from "react-redux";
import ScreenSpin from "../ScreenSpin2";
import { useUtils } from "../useUtils";
import { useToastUtils } from "@/hooks/utils/useToastUtils";

const SearchInput: React.FC = () => {
  const [isOpen, setOpen] = useState(false);
  const containerRef = useRef(null);
  const [zgHight, setZgHight] = useState(400);
  const [searchText, setSearchText] = useState("");
  const { getCurrentLangData } = useUtils();
  const { intl, checkResponse } = useToastUtils();
  const [gameList, setGameList] = useState<GameApiInfo[]>([]);
  const [loading, setLoading] = useState(false);
  // 使用React的useState来保存国际化的placeholder文本
  // 使用useState来保存国际化后的文本
  const [placeholderText, setPlaceholderText] = useState<string>("");
  const dispatch = useDispatch();
  function handlerFocus() {
    dispatch(setOpenTopSearch(true));
    // setOpen(true);
    // setZgHight(() => document.documentElement.scrollHeight)
  }

  const datas = [];
  // for (let i = 0; i < 20; i++) {
  //     const data = {
  //         id: i,
  //         count: i,
  //         imgUrl: 'https://pks01.s3.sa-east-1.amazonaws.com/file/dc9395afef674876a82873615f0faba0.webp',
  //     }
  //     datas.push(data);
  // }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    };
    // 使用intl.formatMessage来获取纯文本
    const searchText = intl.formatMessage({ id: "input_search" });
    setPlaceholderText(searchText);
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // 监听键盘事件,搜索数据
  function handlerKeydown(event: React.KeyboardEvent<HTMLDivElement>) {
    if (event.key === "Enter") {
      searchData();
    }
  }

  async function searchData() {
    if (!searchText || searchText.trim().length < 3) {
      return;
    }
    try {
      setLoading(true);
      const res = (await searchGameData({
        msgID: 400031,
        gameName: searchText, //游戏名字
        platformId: null, //平台id
        language: getCurrentLangData().paramCode
      })) as ResSearchGameDataMessage;
      if (checkResponse(res)) {
        console.log("获取到搜索游戏的数据", res);

        setGameList(res.gameApiInfo);
      }
    } finally {
      setLoading(false);
    }
  }

  // 查询游戏下拉抽屉
  function pretendLogin() {
    dispatch(setOpenTopSearch(true));
  }

  return (
    <div className="w-full">
      <div>
        <div
          className={`${isOpen ? "block" : "hidden"} 
                w-[100%] top-0 left-0 right-0 bottom-0 
                fixed bg-[#15252F]/80`}
          style={{ height: `${zgHight}px`, zIndex: 1000 }}
        ></div>
      </div>
      <div
        ref={containerRef}
        tabIndex={0}
        className="relative"
        // onFocus={handlerFocus}
      >
        <ConfigProvider
          theme={{
            components: {
              Input: {
                activeBorderColor: "var(--botton-bg-green)", // 输入框激活边框颜色
                colorBorder: "#25282C", // 输入框边框颜色
                activeBg: "#25282C"
              }
            }
          }}
        >
          <Input
            onClick={() => pretendLogin()}
            placeholder={placeholderText}
            className={`w-[100%] hover:border-[--botton-bg-green] text-[--wallet-title-text-color] font-bold h-[48px] rounded-xl ${
              isOpen ? "z-1000" : ""
            }`}
            style={{ zIndex: isOpen ? 1000 : 0 }}
            prefix={<img src={searchIcon} className="cursor-pointer" onClick={searchData} alt=""/>}
            onKeyDown={(event) => handlerKeydown(event)}
            onChange={(event) => setSearchText(event.target.value)}
          />
        </ConfigProvider>

        {gameList && gameList.length > 0 ? (
          <div
            className={`${
              isOpen ? "block" : "hidden"
            } w-[100%] min-h-[13rem] mt-5 bg-gray-800 absolute  rounded-md grid grid-cols-3 sm:grid-cols-5 lg:grid-cols-8 py-2 px-4 gap-2`}
            style={{ zIndex: 1000 }}
          >
            {gameList.map((item, index) => (
              <div className="w-full h-full" key={item.gameId}>
                <div className="flex flex-col">
                  <div className="flex">
                    <a>
                      <img data-v-a7aab124="" src={item.fileUrl} className="rounded-lg" alt=""/>
                    </a>
                  </div>
                  <div className="flex items-center justify-start">
                    <div className="cirDiv ml-1"></div>
                    <div className="ml-1">{item.onlineNum}</div>
                    <div className="text-sub-text-d-white font-bold ml-2">
                      <FormattedMessage id="playing" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div
            className={`${
              isOpen ? "block" : "hidden"
            } w-[100%] min-h-[13rem] mt-5 bg-gray-800 absolute  rounded-md flex py-2 px-4 gap-2`}
            style={{ zIndex: 1000 }}
          >
            <div className="text-[--search-game-text-color]  font-bold flex w-[100%] justify-center py-3 relative">
              <ScreenSpin loading={loading} />
              <FormattedMessage id="search_text" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchInput;
