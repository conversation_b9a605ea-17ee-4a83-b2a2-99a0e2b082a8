import { BackStageMessage } from "@/protos/BackStage";
import EventBus from "@/utils/EventBus";
import { ModalType } from "@/utils/enums";
import { Modal } from "antd";
import { useEffect } from "react";
import "swiper/css"; // 引入 Swiper 的 CSS 文件
import "swiper/css/autoplay"; // 引入 autoplay 的 CSS
import "swiper/css/pagination";
import { useJump } from "../context/useJumpContext";
import CloseIcon from "./CloseIcon";
import { useCreateOrder } from "./directRecharge/useCreateOrder";

const DirectChargeModal: React.FC<{
  currentPopup: BackStageMessage.IPopupInfo;
}> = ({ showModal, handleCancel}) => {
  if (!showModal) return null;
  const { createOrder, onlyCrpto, rechargeLoading } = useCreateOrder();
  const { setDepostiSource } = useJump() || {};

  useEffect(() => {
    if (!rechargeLoading) {
      if (onlyCrpto.current) {
        setDepostiSource(2);
        EventBus.emit("showModal", ModalType.showWalletModal)
      } else {
        
      }
    }
  }, [rechargeLoading, onlyCrpto])

  return (
    <>
      <Modal
        centered={true}
        styles={{
          header: { backgroundColor: "var(--header-bg-color-basic)", borderRadius: "16px" },
          content: {
            backgroundColor: "transparent",
            padding: 0,
            height: "auto"
          }
        }}
        footer={null}
        title={
          null
        }
        open={showModal}
        onCancel={handleCancel}
        destroyOnHidden={true}
        onOk={handleCancel}
        closeIcon={<CloseIcon />}
        mask={true}
        style={{
          maxWidth: "200px"
        }}
      >
        <div
          className={` flex flex-col  rounded-lg min-h-[150px] justify-center items-center relative`}
        >
          <div
            className={`${
              "w-full" 
            } text-sub-text text-sm text-left justify-center items-center rounded-2xl`}
          >
           
          </div>
        </div>
      </Modal>
    </>
  );
};

export default DirectChargeModal;
