import { useJump } from "../context/useJumpContext";
import { usePersonInfoUtils } from "../usePersonInfoUtils";
import { usePoint } from "../usePoint";
import { useCreateOrder } from "./directRecharge/useCreateOrder";

import React, { memo, useCallback, useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { useNavigate } from "react-router-dom";

import lvIcon from "@/assets/dailyRecharge/lv.svg";
import xingIcon from "@/assets/dailyRecharge/xing.webp";
import CommonModal from "@/components/CommonModal";
import { useAllGame } from "@/components/games/AllGameProvider";
import HintContent from "@/components/hint/HintContent";
import DepositZyModel from "@/components/personInfo/DepositZyModel";
import ScreenSpin from "@/components/ScreenSpin2";
import { useUtils } from "@/components/useUtils";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { BillingMessage } from "@/protos/Billing";
import { ModalType } from "@/utils/enums";
import EventBus from "@/utils/EventBus";

import { Button } from "antd";

const DailyRecharge: React.FC<{ handleCancel: () => void; showModal: boolean }> = memo(
  ({ showModal, handleCancel }) => {
    const handleClose = () => {};
    const intl = useIntl();
    const { Toast } = useToastUtils();
    const { personValue } = usePersonInfoUtils();
    const { isLogin, formatNumberQfw, cronMap, hanldeMask } = useUtils();
    const { showHint, setDepostiSource } = useJump() || {};
    const [oepnDepsitIfreme, setOpenDepsitIfreme] = useState(false);
    const [depositOrderInfo, setDepositOrderInfo] =
      useState<BillingMessage.IResCreateRechargeOrderMessage>(null);
    const { allConfigData } = useAllGame();
    const { createOrder, onlyCrpto, rechargeLoading } = useCreateOrder();
    const { activityPoint, errorMessagePoint } = usePoint();
    const navigate = useNavigate();
    const [isDetailsOpen, setIsDetailsOpen] = useState(false);

    const createMyOrder = useCallback(
      async (index, amount, currencyId) => {
        try {
          if (!isLogin) {
            handleCancel();
            EventBus.emit("showModal", ModalType.showLoginModal);
            return;
          }
          if (onlyCrpto.current) {
            EventBus.emit("showModal", ModalType.showWalletModal);
            return;
          }
          hanldeMask(true);
          const res = await createOrder(amount, 301, location.pathname, currencyId, false);
          console.debug("创建订单成功", res);
          if (res.payAddress) {
            setDepositOrderInfo(res);
            setOpenDepsitIfreme(true);
          }
        } catch (error) {
          const errorMessage = JSON.stringify(error, Object.getOwnPropertyNames(error));
          errorMessagePoint(errorMessage, location.pathname, window.location.host);
          if (error.code === "ECONNABORTED") {
            Toast(intl.formatMessage({ id: "cheannel_exception" }), "error");
          } else if (error.message === "Network Error" || error.message.includes("ERR_NETWORK")) {
            Toast(intl.formatMessage({ id: "newwork_error" }), "error");
          }
        } finally {
          hanldeMask(false);
        }
      },
      [isLogin]
    );

    useEffect(() => {
      if (showModal) {
        activityPoint(15000, 0);
      }
    }, [showModal]);

    const jumpDeposit = useCallback(() => {
      setDepostiSource(208);
      if (window.screen.width > 1024) {
        navigate("/profile/deposit");
      } else {
        navigate("/mobile_deposit");
      }
      handleCancel();
    }, []);

    return (
      <>
        <DepositZyModel
          showModal={oepnDepsitIfreme}
          info={depositOrderInfo}
          handleCancel={() => setOpenDepsitIfreme(false)}
          rechargeType={3}
        ></DepositZyModel>
        <CommonModal
          width={window.screen.width > 1024 ? "20%" : "90%"}
          footer={null}
          open={showModal}
          onCancel={handleCancel}
          afterClose={handleClose}
          maskClosable={false}
        >
          {rechargeLoading ? (
            <div className="text-primary-text flex flex-col w-full h-[250px]  py-4 px-2 bg-[#1B1E23] relative rounded-[16px] overflow-hidden">
              <ScreenSpin loading={rechargeLoading} />
            </div>
          ) : (
            <div className="text-primary-text flex flex-col w-full  py-4 px-2 bg-[#1B1E23] rounded-[16px] relative">
              {showHint && <HintContent popupId={showHint} />}
              <ScreenSpin loading={rechargeLoading} />
              <img
                src={xingIcon}
                alt=""
                className=" absolute left-0 -top-0 object-cover h-[75px]"
              />
              <img src={lvIcon} alt="" className=" absolute top-[50%] -translate-y-[50%]" />
              <span className="mb-[14px] ml-5 font-semibold text-[14px]/[20px] h-[20px]">
                {allConfigData?.dailyRechargePopList?.[0]?.title}
              </span>
              <div className="flex flex-col gap-y-2" style={{ zIndex: 2 }}>
                {allConfigData?.dailyRechargePopList?.map((item, index) => {
                  return (
                    <div key={index} className="flex flex-col gap-y-2">
                      {item?.gearInfo?.map((gear, index2) => {
                        return (
                          <React.Fragment key={index + index2}>
                            {(personValue?.dailyRechargeTimes ?? 0) === index2 && (
                              <div
                                className="bg-[#2E323A] rounded-lg h-[104px] w-full flex items-center pl-[22px] pr-[16px] justify-between relative overflow-hidden"
                                style={{
                                  background:
                                    "linear-gradient(106deg, #2E323A 16.77%, var(--daily-recharge-div-color) 77.65%)",
                                  border: "3px solid var(--daily-recharge-div-border-color)"
                                }}
                              >
                                <div className=" absolute top-0 left-0">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="50"
                                    height="47"
                                    viewBox="0 0 50 47"
                                    fill="none"
                                  >
                                    <path d="M0 0H50L27.5 22.381L0 47V0Z" fill="#4B515D" />
                                  </svg>
                                  <span className=" absolute text-[24px] font-semibold top-0 left-1.5 transform">
                                    <span>{index2 + 1}°</span>
                                  </span>
                                </div>

                                <div className="flex items-center gap-x-[10px]">
                                  <img src={gear?.icon} alt="" className="w-[32px] h-[34px]" />
                                  <div className="flex flex-col ">
                                    <span className="font-semibold text-[20px] text-primary-text flex items-center">
                                      <span>{cronMap[item.currencyId]?.symbol}</span>
                                      <span>{gear?.minRecharge}</span>
                                      <span>-</span>
                                      <span>{gear?.maxRecharge}</span>
                                    </span>
                                    <span className="text-[--main-theme-color] font-medium text-[16px]/[12px] flex items-center gap-x-1">
                                      <span>
                                        {formatNumberQfw((gear?.giftRatio ?? 0) * 100, 0)}%
                                      </span>
                                      <span>
                                        <FormattedMessage id="bonus_2" />
                                      </span>
                                    </span>
                                  </div>
                                </div>

                                <div className="flex flex-col gap-y-3">
                                  <Button
                                    className={`${
                                      (personValue?.dailyRechargeTimes ?? 0) === index2
                                        ? "daily_recharge_btn "
                                        : " opacity-60 bg-[#B6C0B75E]"
                                    } ${
                                      (personValue?.dailyRechargeTimes ?? 0) < index2 && " hidden"
                                    } !h-[24px] !w-[84px] !text-pxs !font-medium gap-x-1`}
                                    onClick={() =>
                                      createMyOrder(index2, gear?.amount || 0, item?.currencyId)
                                    }
                                  >
                                    <span>
                                      <FormattedMessage id="deposit" />
                                    </span>
                                    <span>{gear?.amount}</span>
                                  </Button>
                                  <button
                                    className={`${
                                      (personValue?.dailyRechargeTimes ?? 0) === index2
                                        ? "daily_recharge_btn"
                                        : " opacity-60 bg-[#B6C0B75E]"
                                    } ${
                                      (personValue?.dailyRechargeTimes ?? 0) < index2 && " hidden"
                                    } !h-[24px] !w-[84px] !text-pxs !font-medium gap-x-1`}
                                    onClick={jumpDeposit}
                                  >
                                    <span>
                                      <FormattedMessage id="more_bonus" />
                                    </span>
                                  </button>
                                </div>
                              </div>
                            )}

                            {(personValue?.dailyRechargeTimes ?? 0) != index2 && (
                              <div
                                key={index + index2}
                                className="bg-[#2E323A] rounded-lg h-[48px] w-full flex items-center pl-[22px] pr-[16px] justify-between relative overflow-hidden"
                              >
                                <div className=" absolute top-0 left-0">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="35"
                                    height="33"
                                    viewBox="0 0 35 33"
                                    fill="none"
                                  >
                                    <path d="M0 0H35L19.25 15.7143L0 33V0Z" fill="#4B515D" />
                                  </svg>
                                  <span className=" absolute text-[10px] font-semibold top-1 left-1.5 transform">
                                    <span>{index2 + 1}°</span>
                                  </span>
                                </div>

                                <div className="flex items-center gap-x-[10px] ">
                                  <img src={gear?.icon} alt="" className="w-[24px] h-[27px]" />
                                  <div className="flex flex-col ">
                                    <span className="font-semibold text-[14px] text-primary-text flex items-center">
                                      <span>{cronMap[item.currencyId]?.symbol}</span>
                                      <span>{gear?.minRecharge}</span>
                                      <span>-</span>
                                      <span>{gear?.maxRecharge}</span>
                                    </span>
                                    <span className="text-[--main-theme-color] font-medium text-[10px]/[12px] flex items-center gap-x-1">
                                      <span>
                                        {formatNumberQfw((gear?.giftRatio ?? 0) * 100, 0)}%
                                      </span>
                                      <span>
                                        <FormattedMessage id="bonus_2" />
                                      </span>
                                    </span>
                                  </div>
                                </div>
                                {(personValue?.dailyRechargeTimes ?? 0) > index2 && (
                                  <Button
                                    disabled={(personValue?.dailyRechargeTimes ?? 0) != index2}
                                    className={`${
                                      (personValue?.dailyRechargeTimes ?? 0) === index2
                                        ? "btn"
                                        : " opacity-60 bg-[#B6C0B75E]"
                                    } ${
                                      (personValue?.dailyRechargeTimes ?? 0) < index2 && " hidden"
                                    } !h-[24px] !w-[84px] !text-pxs !font-medium`}
                                  >
                                    <FormattedMessage id="closed" />
                                  </Button>
                                )}
                              </div>
                            )}
                          </React.Fragment>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
              {allConfigData?.dailyRechargePopList?.[0]?.desc && (
                <div className="mt-2 w-full flex flex-col items-center">
                  <div
                    className="flex items-center gap-x-1 px-4 py-2 cursor-pointer rounded-lg"
                    onClick={() => setIsDetailsOpen(!isDetailsOpen)}
                  >
                    <span
                      className="text-[16px] font-medium "
                      style={{ color: "rgba(255, 255, 255, 0.45)" }}
                    >
                      <FormattedMessage id="details" defaultMessage="详情" />
                    </span>
                    <svg
                      className={`w-[18px] h-[18px] transition-transform ${
                        isDetailsOpen ? "rotate-180" : ""
                      }`}
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="rgba(255, 255, 255, 0.45)"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </div>
                  {isDetailsOpen && (
                    <div
                      className="px-4 text-sm text-[#B6C0B7] transition-all w-full"
                      dangerouslySetInnerHTML={{
                        __html: allConfigData?.dailyRechargePopList?.[0]?.desc
                      }}
                    ></div>
                  )}
                </div>
              )}
            </div>
          )}
        </CommonModal>
      </>
    );
  }
);

export default DailyRecharge;
