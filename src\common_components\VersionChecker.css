.custom-version-modal .ant-modal-content {
  background-color: #20263C;
  border-radius: 8px;
}

.custom-version-modal .ant-modal-header {
  background-color: #78828A;
  border-bottom: 1px solid #e8e8e8;
}

.custom-version-modal .ant-modal-confirm-title {
  color: #FFF;
  font-size: 24px;
  font-weight: 500;
}

.custom-version-modal .ant-modal-confirm-content {
  text-align: center;
  color: #78828A;
}

.custom-ok-button {
  height: 44px !important;
  width: 150px !important;
}

.custom-ok-button:focus-visible{
  outline: none !important;
  outline-offset: none !important;
}

.custom-cancel-button {
  border-radius: var(--radius-md, 8px);
  border: 1px solid rgba(255, 255, 255, 0.20);
  width: 150px;
  height: 44px;
  background-color: transparent;
  color: rgba(255, 255, 255, 0.70);
  font-family: "Inter";
  font-size: 16px;
}

.custom-version-modal .ant-modal-confirm-btns {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.custom-version-modal_brazil .ant-modal-content {
  background: linear-gradient(219deg, #202130 6.45%, #2B2A43 93.63%) !important;
  box-shadow: 0px 4px 4px 0px rgba(8, 1, 52, 0.25), 0px 0px 2px 0px #5158A5 inset, 0px 0px 3px 0px #000 inset;
  padding: 14px !important;
}
.custom-version-modal_brazil .ant-modal-confirm-btns {
  display: flex;
  justify-content: space-between;
}

/* .custom-ok-button_brazil {
  display: flex;
  height: 44px;
  border-radius: 100px;
  background: linear-gradient(180deg, #FAD900 0%, #F2A72D 100%);
  box-shadow: 0px -2px 2px 0px rgba(0, 0, 0, 0.25) inset, 0px 1px 1px 0px #FDE183 inset, 0px 0px 5px 0px rgba(0, 0, 0, 0.39) inset, 0px 2px 5px 0px rgba(26, 26, 17, 0.48);
} */

.custom-ok-button_brazil {
  height: 44px !important;
  width: 150px !important;
  border-radius: 100px;
  border: 1px solid #FFEEA0;
  font-family: "Inter";
  font-size: 16px;
  background: linear-gradient(180deg, #FAD900 0%, #F2A72D 100%);
  box-shadow: 0px -2px 2px 0px rgba(0, 0, 0, 0.25) inset, 0px 1px 1px 0px #FDE183 inset, 0px 0px 5px 0px rgba(0, 0, 0, 0.39) inset, 0px 2px 5px 0px rgba(26, 26, 17, 0.48);
}

.custom-ok-button_brazil:focus-visible{
  outline: none !important;
  outline-offset: none !important;
}

.custom-cancel-button_brazil {
  width: 150px;
  height: 44px;
  background-color: transparent;
  color: rgba(255, 255, 255, 0.70);
  font-size: 16px;
  border-radius: 100px !important;
  border: 1px solid rgba(255, 255, 255, 0.20);
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-cancel-button_brazil:hover {
  background-color: rgba(255, 255, 255, 0.10);
}