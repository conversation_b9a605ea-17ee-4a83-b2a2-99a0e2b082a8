import { FormattedMessage } from "react-intl";
import { useScreenWidth } from "../../utils/MobileDetectionContext";
import CommonModal from "../CommonModal";
import { useToastUtils } from "@/hooks/utils/useToastUtils";

const DailyQuestModal: React.FC = ({
  showModal,
  handleCancel,
}) => {
  if (!showModal) { return null }
  const { screenWidth } = useScreenWidth()  ;
  const { intl } = useToastUtils();
  function handleClose() { }

  return (
    <>
      <CommonModal
        width={screenWidth > 1024 ? "38%" : "100%"}
        footer={null}
        title={
          <div className="bg-content-second-gray p-4 rounded-tl-2xl rounded-tr-2xl">
            <span className="text-white text-lg/6 font-medium">
              <FormattedMessage id="rules" />
            </span>
          </div>
        }
        open={showModal}
        onCancel={handleCancel}
        afterClose={handleClose}
      >
        <div className="hide-scrollbar flex flex-col w-full p-5 overflow-y-auto select-none space-y-1 text-sub-text max-h-[500px]"
         dangerouslySetInnerHTML={{__html: intl.formatMessage({id: "dayliy_quest"})}}>
        </div>
      </CommonModal>
    </>
  );
};

export default DailyQuestModal;
