import popuCloseIcon from "@/assets/popupClose.svg";
import popuRighteArrowIcon from "@/assets/popuRighteArrow.svg";
import { useUtils } from "@/components/useUtils";
import { BackStageMessage } from "@/protos/BackStage";
import { setActivitySource } from "@/store/popup";
import { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { CSSTransition } from "react-transition-group";
import "swiper/css"; // 引入 Swiper 的 CSS 文件
import "swiper/css/autoplay"; // 引入 autoplay 的 CSS
import "swiper/css/grid"; // 引入 autoplay 的 CSS
import { Autoplay } from "swiper/modules"; // 引入 autoplay 模块
import { Swiper, SwiperSlide } from "swiper/react";
import { useJump } from "../context/useJumpContext";
import { useEleDrag } from "@/hooks/useEleDrag";
import "./css/popup.css";

const SiderPopup: React.FC<{
  smallPopup: BackStageMessage.IPopupInfo;
  smallPopupType: number;
  handleClose: (type: number) => void;
  handleJump: (popupData: BackStageMessage.IPopupData) => void;
}> = ({ smallPopup, smallPopupType, handleClose, handleJump }) => {
  
  const [showDiv, setShowDiv] = useState(false);
  const [showLongDivShape, setShowLongDivShape] = useState(false);
  const { delay } = useUtils();
  const mainDivRef = useRef(null);
  const longDivRef = useRef(null);
  const { setDepostiSource } = useJump() || {};
  const dispatch = useDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    console.log("获取到小弹窗数据", smallPopup);
    if (smallPopup) {
      setShowDiv(true);
    }
  }, [smallPopup]);

  const swiperRef = useRef(null);

  const handleSwitchClass = async (type: number) => {
    if (!type) {
      return;
    }
    console.log("切换侧边栏状态", type);

    if (type === 1) {
      await delay(10);
      setShowDiv(false);
      await delay(300);
      setShowLongDivShape(!showLongDivShape);
    } else {
      await delay(10);
      setShowLongDivShape(!showLongDivShape);
      await delay(100);
      setShowDiv(true);
    }
  };

  const handlerMainDiv = async (type: number) => {
    console.log("关闭侧边栏", type);
    setShowDiv(false);
    setShowLongDivShape(false);
    await delay(200);
    handleClose(type);
  };

  const homeButtonRightTopDrag = useEleDrag(
    { x: smallPopup.popupDataList.length === 1 ? 10 : 20, y: 200 },
    { direction: { x: "right-to-left", y: "top-to-bottom" } }
  );

  const homeButtonLeftDrag = useEleDrag(
    { x: 5, y: 200 },
    { direction: { x: "left-to-right", y: "bottom-to-top" } }
  );

  const homeButtonRightDrag = useEleDrag(
    { x: smallPopup.popupDataList.length === 1 ? 10 : 20, y: 200 },
    { direction: { x: "right-to-left", y: "bottom-to-top" } }
  );

  const drag = useMemo(() => {
    if (smallPopupType == 2) {
      return homeButtonLeftDrag
    } else if (smallPopupType == 3) {
      return homeButtonRightTopDrag
    } else if (smallPopupType == 4) {
      return homeButtonRightDrag
    } else {
      return homeButtonLeftDrag
    }
  }, [smallPopupType, homeButtonLeftDrag, homeButtonRightTopDrag, homeButtonRightDrag])

  const getSmallShapeConent = useMemo(() => {
    if (smallPopup && smallPopup.popupDataList && smallPopup.popupDataList.length > 0) {
      const size = smallPopup.popupDataList.length;
      if (size === 1) {
        return (
          <div
            onClick={() => {
              const data = JSON.parse(JSON.stringify(smallPopup.popupDataList[0]));
              const jumpWhere = data?.popupLinks;
              const innerLinks = data?.innerLinks;
              if (innerLinks) {
                setDepostiSource(204);
              } else if (jumpWhere === 14) {
                // 直充
                data.depositSource = 303;
              } else {
                // 充值弹框
                data.depositSource = 111;
              }
              dispatch(setActivitySource(5));
              handleJump(data, navigate);
            }}
            ref={mainDivRef}
            className="fixed cursor-pointer opacity-0"
            style={{
              left: `${drag?.position?.x}px`,
              top: `${drag?.position?.y}px`,
              zIndex: 999
            }}
            {...(drag?.dragHandlers)}
            >
            <img
              onClick={(e) => {
                e.stopPropagation();
                handlerMainDiv(smallPopupType);
              }}
              src={popuCloseIcon}
              alt=""
              className={`w-[16px] h-[16px] absolute -top-[16px] ${smallPopupType === 2 ? "left-1" : "right-1"} cursor-pointer `}
            />
            <img
            src={smallPopup.popupDataList[0].imageUrls}
            alt=""
            style={{height: "52px", width: "auto"}}
            loading="lazy"
            className="flex-shrink-0 flex"
          />
        </div>
        );
      } else {
        return (
          <div
          onClick={() => handleSwitchClass(1)}
          ref={mainDivRef}
          className="fixed cursor-pointer opacity-0"
          style={{
            left: `${drag?.position?.x}px`,
            top: `${drag?.position?.y}px`,
            zIndex: 999
          }}
          {...(drag?.dragHandlers)}
        >
            <img
              onClick={(e) => {
                e.stopPropagation();
                handlerMainDiv(smallPopupType);
              }}
              src={popuCloseIcon}
              alt=""
              className={`w-[16px] h-[16px] absolute -top-[16px] ${smallPopupType === 2 ? "left-1" : "right-1"} cursor-pointer `}
            />
          <div className="small_pop_bg text-primary-text flex items-center justify-center ml-auto">
            <div className="w-full flex items-center justify-center">
              <Swiper
                spaceBetween={0} // 幻灯片之间的间隔
                slidesPerView={1} // 自动计算显示个数
                direction="horizontal" // 水平方向
                loop={true}
                autoplay={{
                  delay: 2000
                }}
                modules={[Autoplay]}
                onSwiper={(swiper) => (swiperRef.current = swiper)}
                className="w-[52px]"
              >
                {smallPopup.popupDataList.map((item, index) => (
                  <SwiperSlide key={index}>
                    <img
                      key={index}
                      src={item.imageUrls}
                      style={{ width: "auto", height: "52px" }}
                      height={52}
                      alt=""
                    ></img>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
            </div>
          </div>
        );
      }
    }
  }, [smallPopup, handleSwitchClass, handlerMainDiv])

  const containerRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartX(e.pageX - containerRef.current.offsetLeft);
    setScrollLeft(containerRef.current.scrollLeft);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    const x = e.pageX - containerRef.current.offsetLeft;
    const scroll = scrollLeft - (x - startX);
    containerRef.current.scrollLeft = scroll;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseLeave = () => {
    if (isDragging) {
      setIsDragging(false);
    }
  };

  return (
    <>
      <CSSTransition nodeRef={mainDivRef} in={showDiv} timeout={200} classNames={smallPopupType == 2 ? "left-in" : "right-in"}>
        {/* 第一步： 未展开 */}
        {getSmallShapeConent} 
      </CSSTransition>

      <CSSTransition
        nodeRef={longDivRef}
        in={showLongDivShape}
        timeout={200}
        classNames={"long-div-fade"}
      >
        <div
          ref={longDivRef}
          className={`fixed invisible`}
          style={{ top: smallPopupType === 3 ? "200px" : "auto",
            bottom: smallPopupType === 2 || smallPopupType === 4 ? "185px" : "auto", left: smallPopupType === 2 ? 2 : "auto", right: smallPopupType === 2 ? "auto" : 2, zIndex: 999 }}
        >
          {
            <>
              <img
                onClick={(e) => {
                  e.stopPropagation();
                  handlerMainDiv(smallPopupType);
                }}
                src={popuCloseIcon}
                alt=""
                className={`w-[16px] h-[16px] absolute -top-[16px] ${smallPopupType === 2 ? "left-1" : "right-1"} cursor-pointer  `}
              />
              <div className={`${smallPopupType == 2 ? "pop_long_left_div justify-end pr-[6px] pl-[2px]" : "pop_long_right_div justify-start pr-[2px] pl-[6px]"} text-primary-text h-[62px] flex items-center  `} >
                <div
                  className="flex items-center gap-x-[9px] overflow-auto"
                  ref={containerRef}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseLeave}
                >
                  {
                  (smallPopupType == 3 || smallPopupType == 4) && (
                    <img
                      className=" cursor-pointer "
                      src={popuRighteArrowIcon}
                      alt=""
                      onClick={() => handleSwitchClass(2)}
                    />
                  )
                }
                  {smallPopup?.popupDataList.map((item, index) => (
                    <img
                      onClick={(e) => {
                        e.stopPropagation();
                        const data = JSON.parse(JSON.stringify(item));
                        const jumpWhere = data?.popupLinks;
                        const innerLinks = data?.innerLinks;
                        if (innerLinks) {
                          setDepostiSource(204);
                        } else if (jumpWhere === 14) {
                          // 直充
                          data.depositSource = 303;
                        } else {
                          // 充值弹框
                          data.depositSource = 111;
                        }
                        dispatch(setActivitySource(5));
                        handleJump(data, navigate);
                      }}
                      key={index}
                      src={item.imageUrls}
                      style={{height: "52px", width: "auto"}}
                      className=" cursor-pointer"
                      alt=""
                    ></img>
                  ))}
                </div>
                {
                  smallPopupType == 2 && (
                <img
                  className=" cursor-pointer rotate-180"
                  src={popuRighteArrowIcon}
                  alt=""
                  onClick={() => handleSwitchClass(2)}
                />
                  )
                }
                
              </div>
            </>
          }
        </div>
      </CSSTransition>
    </>
  );
};

export default SiderPopup;
