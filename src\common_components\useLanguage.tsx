import { changeLanguage } from "@/service/hall";
import { LANG, getCurrentLangData, getIsLogin } from "@/utils/commonUtil";
import EventBus from "@/utils/EventBus";
import { useEffect, useState } from "react";

const useLanguage = () => {
  const [locale, setLocale] = useState();

  useEffect(() => {
    EventBus.on("changeLocale", changeLocale);

    return () => {
      EventBus.off("changeLocale", changeLocale);
    };
  }, []);

  const changeLocale = async (newLocale) => {
    changeLocaleStorage(newLocale);
    if (getIsLogin()) {
      await changeL(getCurrentLangData().paramCode);
    }
    window.location.reload();
  };

  const changeLocaleStorage = (newLocale) => {
    setLocale(newLocale);
    localStorage.setItem('currentLang', newLocale)
    localStorage.setItem(LANG, newLocale);
  };

  const changeL = async (paramCode: number) => {
    const res = await changeLanguage({
      msgID: 400061,
      language: paramCode
    });
    console.log("修改多语言", res);
  };

  return { locale, setLocale, changeLocaleStorage };
};

export default useLanguage;
