import betsIcon from '@/assets/content/bets.png';
import { useScreenWidth } from '@/utils/MobileDetectionContext';
import React from "react";
import { FormattedMessage } from "react-intl";
import { useUtils } from "../useUtils";

const AcceptedNewWorks: React.FC = () => {
    const { cronMap } = useUtils();
    const {screenWidth} = useScreenWidth() 

    console.log('获取到cronMap', cronMap);

    return (
        <div className="flex flex-col w-full">
            <div className="text-primary-text flex gap-x-2">
                <img src={betsIcon} alt="icon-banner-title" className="w-[20px] h-[20px]" />
                <span><FormattedMessage id="accepted_networks" /></span>
            </div>
            {
                screenWidth > 1024 && (
                    <div className="flex space-x-[14px] overflow-auto pt-[14px] w-full">
                        {
                            cronMap && Object.keys(cronMap).filter((key) => key >= 2000).map((key, index) => (
                                <div key={cronMap[key].name} className='min-w-[26px]'>
                                    <img src={cronMap[key].icon} alt="" className="w-[26px]" />
                                </div>
                            ))
                        }
                    </div>
                )
            }
            {
                screenWidth <= 1024 && (
                    <div className="flex space-x-[14px] overflow-auto pt-[14px] w-[100vw] self-center px-4 hide-scrollbar">
                        {
                            cronMap && Object.keys(cronMap).filter((key) => key >= 2000).map((key, index) => (
                                <div key={cronMap[key].name} className='min-w-[26px]'>
                                    <img src={cronMap[key].icon} alt="" className="w-[26px]" />
                                </div>
                            ))
                        }
                    </div>
                )
            }

        </div>
    )
}

export default AcceptedNewWorks;