import CommonRewardModal from "../modal/CommonRewardModal";

import { memo, useCallback, useEffect, useState } from "react";
import { useDispatch } from "react-redux";

import { useUtils } from "@/components/useUtils";
import { useQueryPromotionList } from "@/hooks/queryHooks/useQueryPromotionList";
import { setIschangeUserInfo } from "@/store";
import { PromotionsInfo } from "@/types/activity";
import { DItemShow } from "@/types/common";
import { ResTcpRechargeSuccessMessage } from "@/types/tcp";
import { ModalType, ShareType } from "@/utils/enums";
import EventBus from "@/utils/EventBus";

const DepositSuccess = memo(() => {
  const { isLogin } = useUtils();
  const { refetch } = useQueryPromotionList(isLogin);
  const [showRewardModal, setShowRewardModal] = useState(false);
  const [rewardData, setRewardData] = useState<DItemShow[]>([]);
  const dispatch = useDispatch();
  const [firstReward, setFirstReward] = useState<PromotionsInfo | null>(null);

  const doSuccess = useCallback(async (data: ResTcpRechargeSuccessMessage) => {
    if (data?.extra > 0) {
      const promotions = await refetch();
      EventBus.emit("closeModal", ModalType.showWalletModal);
      const promotion = promotions.data?.promotionsList.find((item) => item.activityId === ShareType.firstDeposit.id);
      if (promotion) {
        setFirstReward(promotion);
      }
      const rewards = []
      rewards.push({
        itemId: data?.currencyId || 0, // 当用于展示时，可能为货币id
        num: data?.extra, // 数量
        rewardType: 2 // 1.cash 2.bonus
      })
      setRewardData(rewards);
      setShowRewardModal(true);
    }
  }, [])

  useEffect(() => {
    EventBus.on("showFirstDepositSuccess", (data: ResTcpRechargeSuccessMessage) => {
      doSuccess(data)
    });
    return () => {
      EventBus.off("showFirstDepositSuccess", (data: ResTcpRechargeSuccessMessage) => {
        
      });
    };
  }, []);

  return (
    <>
      {showRewardModal && (
        <CommonRewardModal
          showModal={showRewardModal}
          handleCancel={() => {
            setShowRewardModal(false);
            refetch();
            dispatch(setIschangeUserInfo(true));
          }}
          rewardData={rewardData || []}
          activityId={firstReward?.activityId || 0}
          activityName={firstReward?.name}
          cId={firstReward?.cid}
        ></CommonRewardModal>
      )}
    </>
  );
});

export default DepositSuccess;
