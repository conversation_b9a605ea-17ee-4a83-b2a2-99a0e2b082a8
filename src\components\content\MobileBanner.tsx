// import facebook from '@/assets/content/facebook.svg';
// import google from '@/assets/content/google.svg';
// import telegram from '@/assets/content/telegram.svg';

import handleIcon from "@/assets/handle.svg";
import mobileBannerIcon from "@/assets/mobileBanner.svg";
import phoneBannerBIcon from "@/assets/phoneBannerB.svg";
import tele_colorBIcon from "@/assets/tele_color.svg";
import { useJump } from "@/common_components/context/useJumpContext";
import { usePerson } from "@/common_components/context/usePersonInfoContext";
import { BackStageMessage } from "@/protos/BackStage";
import { ProtoMessage } from "@/protos/common";
import { LoginMessage } from "@/protos/Login";
import { isPWA } from "@/utils/commonUtil.js";
import { FuntionOpenMapping, ModalType, urlParams } from '@/utils/enums';
import EventBus from "@/utils/EventBus";
import { getOtherChannel } from "@/utils/getChannel";
import { Button } from "antd";
import { useEffect, useRef, useState } from "react";
import { browserName, browserVersion, osName } from "react-device-detect";
import { FormattedMessage } from "react-intl";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import "swiper/css"; // 引入 Swiper 的 CSS 文件
import "swiper/css/autoplay"; // 引入 autoplay 的 CSS
import { Swiper, SwiperSlide } from "swiper/react";
import { useAllGame } from "../games/AllGameProvider";
import { useConfigData } from "../games/providers/ProjectConfigProvider";
import GoogleSignIn from "../GoogleSignIn";
import ScreenSpin2 from "../ScreenSpin2";
import TeleLogin from "../TeleLogin";
import { useUtils } from "../useUtils";

const MobileBanner: React.FC<{
  type: number;
  showText: boolean;
}> = ({ type = 1, showText = true }) => {
  const { allConfigData } = useAllGame() as {
    allConfigData: BackStageMessage.ResConfigDataMessage;
    popuTypeInfo: PopupType;
    setPopuTypeInfo: (type: PopupType) => void;
  };
  const { functionSwitchIds } = useConfigData() as { functionSwitchIds: number[] };
  const { userLogin } = usePerson() || {};
  const { handleJump } = useJump() || {};
  const [loading, setLoading] = useState(true);
  const [bannerInfo, setBannerInfo] = useState<BackStageMessage.IBannerInfo[]>();
  const { getCurrentLangData } = useUtils();
  const navigate = useNavigate();

  useEffect(() => {
    if (allConfigData && allConfigData.bannerList) {
      const currentTime = Date.now(); // 获取当前时间
      const currentLangu = getCurrentLangData().paramCode;
      const bannerInfo = allConfigData?.bannerList.filter(
        (item) =>
          item.index == type &&
          item.language == currentLangu &&
          item.startTime <= currentTime &&
          item.endTime >= currentTime
      );
      console.log("获取到的bannerInfo", bannerInfo);
      setBannerInfo(bannerInfo);
      setLoading(false);
    }
  }, [allConfigData]);

  const handleCustomer = (
    jumpType: number,
    url: string,
    popupLinks: number,
    innerLinks: string,
    notLoginJump: boolean
  ) => {
    console.log("banner点击弹框", jumpType, url, popupLinks);
    const popupInfo = BackStageMessage.PopupInfo.create({
      jumpType: jumpType,
      popupLinks: popupLinks,
      externalLinks: url,
      popupType: 1,
      innerLinks: innerLinks,
      notLoginJump: notLoginJump,
    });
    handleJump(popupInfo, navigate);
  };

  const bannnerBg: React.CSSProperties = {
    backgroundImage: `url(${mobileBannerIcon})`,
    backgroundSize: "cover",
    backgroundPosition: "center"
  };

  function handleClick(type: number) {
    EventBus.emit("showModal", ModalType.showLoginModal)
  }

  const handleTegeLogin = async (data) => {
    console.log("tege登录调用", data);
    const isInstalledPWA = window.matchMedia("(display-mode: standalone)").matches;
    if (data) {
      const referralCode = localStorage.getItem(urlParams.referralCode) || "";
      const al = localStorage.getItem(urlParams.al) || "";
      const kwaiDynamicPixel = localStorage.getItem(urlParams.kwai_dynamic_pixel) || "";
      const kwaiToken = localStorage.getItem(urlParams.kwai_token) || "";
      const clickId = localStorage.getItem(urlParams.click_id) || "";
      const fbPixelId = localStorage.getItem(urlParams.fb_dynamic_pixel) || "";
      const fbToken = localStorage.getItem(urlParams.fb_token) || "";
      const cl = localStorage.getItem(urlParams.cl) || "";

      const rData = LoginMessage.ReqLoginMessage.create({
        account: data.username,
        host: window.location.host,
        referralCode: referralCode || al,
        device: osName + "（" + browserName + " " + browserVersion + "）",
        threePartyId: data.id.toString(),
        threeParty: 3, //1.邮件 2.谷歌 3.Telegram 4.Facebook 5.Twitter
        msgID: ProtoMessage.MID.ReqLogin,
        channel: isPWA() ? 1 : getOtherChannel(),
        cl: cl,
        fbInfo: {
          eventName: "login",
          pixelId: fbPixelId,
          fbToken: fbToken
        },
        feedback: fbPixelId ? 1 : kwaiDynamicPixel ? 2 : 0,
        ...(kwaiDynamicPixel || kwaiToken || clickId ? {
          kWaiInfo: {
            pixelId: kwaiDynamicPixel || "",
            KWaiToken: kwaiToken || "",
            clickId: clickId || ""
          }
        } : {})
      });
      if (userLogin) {
        setLoading(() => true);
        await userLogin(rData);
        setLoading(false);
      }
    }
  };

  const dataList = () => {
    if (bannerInfo) {
      return bannerInfo.map((item, index) => (
        <SwiperSlide key={index} className="w-full">
          <div
            key={index}
            className=" w-full flex relative cursor-pointer h-[54.1vh] flex-col items-center"
            style={bannnerBg}
          >
            <div className=" w-[100%] flex justify-center items-start mt-10 ">
              <div className={divClass}>
                <div className=" mobile_banner_title select-none items-center justify-center font-[900]  uppercase flex space-x-1 overflow-hidden">
                  <div className="text-3xl text-center">{item?.title}</div>
                </div>
                <div className="text-primary-text select-none  text-sm/5 ">{item?.subtitle}</div>
              </div>
            </div>
            <div className="w-[88.8%] mt-6 block mx-auto relative">
              <img src={handleIcon} alt="" className="absolute bottom-12 right-12" />
              <img
                onClick={() =>
                  handleCustomer(
                    item?.jumpType,
                    item?.externalLinks,
                    item?.popupLinks,
                    item?.innerLinks,
                    item?.notLoginJump
                  )
                }
                src={item.fileUrl}
                alt=""
              />
            </div>
          </div>
        </SwiperSlide>
      ));
    }
  };

  const swiperRef = useRef();

  const getDatas = () => {
    if (bannerInfo) {
      if (bannerInfo.length > 0) {
        return (
          <div className="w-full" style={bannnerBg}>
            <Swiper
              spaceBetween={10} // 幻灯片之间的间隔
              slidesPerView={"auto"} // 自动计算显示个数
              direction="horizontal" // 水平方向
              loop={false}
              onSwiper={(swiper) => (swiperRef.current = swiper)}
            >
              {dataList()}
            </Swiper>
          </div>
        );
      }
    }
  };

  const isLogin = useSelector((state: any) => state.systemReducer.isLogin);

  const divClass = isLogin
    ? "flex flex-col w-[100%] space-y-3 slelct-none"
    : "flex flex-col  justify-center items-center select-none  gap-y-1 w-full";

  return (
    <div className="w-full relative ">
      <ScreenSpin2 loading={loading} />
      <div className="min-h-[54.1vh]">{getDatas()}</div>

      <img src={phoneBannerBIcon} alt="" className="w-full" />
      <div className=" flex space-x-4 w-full justify-center -mt-4">
        {!isLogin && showText && (
          <Button
            className="text-black h-[38px] w-[40.813%] text-sm/4 rounded-xl  font-semibold bg-button-bg-green "
            onClick={() => handleClick(2)}
          >
            <FormattedMessage id="register_now" />
          </Button>
        )}
      </div>
      {functionSwitchIds?.includes(FuntionOpenMapping.Three_Party_Login) && (
        <div className="w-full flex justify-center items-center mt-6 gap-x-[18px]">
          {/* <img src={face_colorBIcon} alt="" /> */}

          <GoogleSignIn>
          </GoogleSignIn>
          <TeleLogin callback={(data) => handleTegeLogin(data)}>
            <img src={tele_colorBIcon} alt="" />
          </TeleLogin>
        </div>
      )}
    </div>
  );
};
export default MobileBanner;
