import { usePersonInfoUtils } from "../usePersonInfoUtils";
import useRecaptcha from "../useRecaptcha";

import { createContext, useCallback, useContext, useEffect, useMemo } from "react";
import { useIntl } from "react-intl";
import { useDispatch } from "react-redux";

import PersonFilled from "@/assets/profile/PersonFilled.webp";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { ProtoMessage } from "@/protos/common";
import { LoginMessage } from "@/protos/Login";
import { login, registerAuth } from "@/service/user";
import { setFiatSymbol, setUserToken, setViewFiat } from "@/store";
import {
  setTriggerLoginOrRegisterPopup
} from "@/store/popup";
import { useHandleLogin } from "@/templateThree/components/common/login/modules/useHandleLogin";
import { setHallHost, setToken } from "@/utils/commonUtil";
import { urlParams } from "@/utils/enums";

import { useQueryConfigData } from "@/hooks/queryHooks/useQueryConfigData";
import md5Util from "crypto-js/md5";

const PersonInfoContext = createContext();
const BASE_URL = import.meta.env.VITE_PLATFORM_URLS;
import EventBus from "@/utils/EventBus";
import { ModalType } from "@/utils/enums";

const PersonProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  console.log("PersonProvider执行了============================");
  const intl = useIntl();
  const { executeRecaptcha } = useRecaptcha();
  const VITE_REQUEST_GOOGlE = import.meta.env.VITE_REQUEST_GOOGlE;
  const { personValue } = usePersonInfoUtils();
  const { Toast } = useToastUtils();
  const dispatch = useDispatch();
  const { handleLogin } = useHandleLogin();
  const { data: allConfigData } = useQueryConfigData(0);

  const getPersonHeaderIcon = useCallback(() => {
    if (allConfigData && personValue) {
      const head = allConfigData?.headList.find(
        (item) => item.headId == parseInt(personValue.headId)
      );
      return head?.fileUrl;
    }
    return PersonFilled;
  }, [allConfigData, personValue]);

  const initRegion = useMemo(() => {
    if (personValue) {
      return personValue.region;
    }
    return "";
  }, [personValue]);

  useEffect(() => {
    if (personValue && personValue.viewFiat) {
      dispatch(setViewFiat(personValue.viewFiat));
      personValue.cItem.find((item) => {
        if (item.currencyId === personValue.viewFiat) {
          dispatch(setFiatSymbol(item.symbol));
        }
      });
    }
    if (personValue) {
      if (window.Intercom) {
        window.Intercom("update", {
          name: personValue.playerName,
          user_id: personValue.playerId,
          phone: personValue.phone ? String(personValue.phone) : ""
        });
      }
    }
  }, [personValue]);

  useEffect(() => {
    const parsedUrl = new URL(window.location.href);
    const search = decodeURIComponent(parsedUrl.search).replace("-~.", "&").replace(/&amp;/g, "&");
    const searchParams = new URLSearchParams(decodeURIComponent(search));
    const params = {};
    searchParams.forEach((value, key) => {
      localStorage.setItem(key, value);
    });
    console.log("域名为:", parsedUrl.hostname, "参数为:", params);
  }, []);

  const userLogin = async (data) => {
    let invalidCode = false;
    try {
      if (VITE_REQUEST_GOOGlE == "true") {
        if (!executeRecaptcha) {
          Toast("Recaptcha Token is none", "error");
          console.error("Recaptcha is not loaded");
        }

        const token = await executeRecaptcha("login");
        console.log("获取到token", token);

        if (!token) {
          Toast("Recaptcha Token is missing", "error");
          return;
        }

        const urlActivity = localStorage.getItem(urlParams.activity) || "";
        if (urlActivity) {
          data.activity = urlActivity;
        }

        const authResponse = await registerAuth(
          LoginMessage.ReqRegisterAuthMessage.create({
            msgID: ProtoMessage.MID.ReqRegisterAuth,
            googleToken: token
          })
        );
        if (authResponse.error > 0) {
          Toast(
            intl.formatMessage({ id: "error_message_" + authResponse.error }),
            "error",
            authResponse.error
          );
          return;
        }
      }

      const referralCode = localStorage.getItem(urlParams.referralCode) || "";
      const kwaiDynamicPixel = localStorage.getItem(urlParams.kwai_dynamic_pixel) || "";
      const kwaiToken = localStorage.getItem(urlParams.kwai_token) || "";
      const clickId = localStorage.getItem(urlParams.click_id) || "";
      const fbPixelId = localStorage.getItem(urlParams.fb_dynamic_pixel) || "";
      const fbToken = localStorage.getItem(urlParams.fb_token) || "";

      if (referralCode) {
        invalidCode = await checkReferralCode(referralCode);
      }
      if (invalidCode) {
        return false;
      }
      data.fbInfo = {
        eventName: "register_success",
        pixelId: fbPixelId,
        fbToken: fbToken
      };

      data.kWaiInfo = {
        pixelId: kwaiDynamicPixel,
        KWaiToken: kwaiToken,
        clickId: clickId
      };

      const response = (await login(data)) as LoginMessage.ResLoginMessage;
      if (response.error > 0) {
        Toast(
          intl.formatMessage({ id: "error_message_" + response.error }),
          "error",
          response.error
        );
        return false;
      }
      console.log("登录基础信息",response)
      if(response.register) {
        console.log("当前是第三方登录",response)
        setTimeout(() => {
          EventBus.emit("showModal", ModalType.showGuideModal);
        },300)
      }
      console.log("请求进入大厅entryHall----登陆", response);
      data.fb_token = fbToken;
      const host = response.gateAddress;
      setHallHost(host);
      setToken(response.token);
      dispatch(setUserToken(response.token));
      const account = await handleLogin(1);
      // if (
      //   canPopupCenterActivity(account) &&
      //   (webSite?.webSiteInfo?.siteModel == 8 ||
      //     webSite?.webSiteInfo?.siteModel == 6 ||
      //     webSite?.webSiteInfo?.siteModel == 7)
      // ) {
      //   dispatch(
      //     setCenterPopupTitle({
      //       title: intl.formatMessage({ id: "welcome_back" }),
      //       type: 1
      //     })
      //   );
      //   dispatch(setTriggerCenterPopup(true));
      // } else {
        dispatch(setTriggerLoginOrRegisterPopup(true));
      // }
      return true;
    } catch (error) {
      console.log("错误信息-->", error);
      return false;
    }
  };

  const canPopupCenterActivity = (account: string) => {
    const now = new Date().getTime(); // 当前时间
    const storedTimestamp = localStorage.getItem(account + "_centerPopupClearTime");
    if (storedTimestamp) {
      const nextClearTime = parseInt(storedTimestamp, 10);

      // 如果当前时间已经超过了下次清空的时间戳（即零点），则清空数组
      if (now > nextClearTime) {
        // 清空数组
        localStorage.setItem(account + "_centerPopupClearTime", "");
        // 更新下次清空的时间戳为明天零点
        const now = new Date();
        const midnight = new Date(now);
        midnight.setHours(24, 0, 0, 0); // 设置为当天的零点时间
        localStorage.setItem(account + "_centerPopupClearTime", midnight.getTime().toString());
        return true;
      }
    } else {
      const now = new Date();
      const midnight = new Date(now);
      midnight.setHours(24, 0, 0, 0); // 设置为当天的零点时间
      localStorage.setItem(account + "_centerPopupClearTime", midnight.getTime().toString());
      return true;
    }
    return false;
  };

  function isAllDigits(str) {
    return /^\d+$/.test(str);
  }

  const getActionUrl = () => {
    return BASE_URL + "/gateway/promotion/check";
  };

  function md5(str) {
    return md5Util(str).toString();
  }

  const checkReferralCode = async (referralCode) => {
    let invalidCode = false;
    if (isAllDigits(referralCode)) {
      await fetch(getActionUrl(), {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          referralCode: referralCode,
          sign: md5("wgs_referral_code_check")
        })
      })
        .then((response) => {
          if (response.ok) {
            return response.json();
          } else {
            throw new Error("Network response was not ok");
          }
        })
        .then((data) => {
          if (data.code === 0) {
            console.log("校验邀请码返回数据", data, data.code);
            Toast(intl.formatMessage({ id: "invalid_invitation_code" }), "error");
            invalidCode = true;
            return;
          }
        })
        .catch((error) => {
          console.log("校验邀请码失败", error);
          Toast(error.message, "error");
        });
    }
    return invalidCode;
  };

  return (
    <PersonInfoContext.Provider
      value={{
        userLogin,
        initRegion,
        getPersonHeaderIcon
      }}
    >
      {children}
    </PersonInfoContext.Provider>
  );
};
export const usePerson = () => useContext(PersonInfoContext);
export default PersonProvider;
