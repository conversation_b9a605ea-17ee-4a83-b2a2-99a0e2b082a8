import { useGameData } from "@/components/games/providers/GameDataProvider";
import { useConfigData } from "@/components/games/providers/ProjectConfigProvider";
import { HallMessage } from "@/protos/Hall";
import { setShowMobileHeader } from "@/store";
import { getIsLogin } from "@/utils/commonUtil";
import { FuntionOpenMapping, ModalType } from '@/utils/enums';
import EventBus from "@/utils/EventBus";
import { Image, Skeleton } from "antd";
import { SetStateAction, useCallback, useEffect, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { useJump } from "../context/useJumpContext";

const CommonMobileFooter: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(1);
  const [loading, setLoading] = useState(true);
  const { channelList } = useGameData() as {
    channelList: HallMessage.IGameChannelInfo[];
  };
  const { setCommonJumpto, setDepostiSource } = useJump() || {};
  const [channelInfo, setChannelInfo] = useState<HallMessage.IGameChannelInfo | undefined>(
    undefined
  );
  const [count, setCount] = useState(4);
  const { functionSwitchIds } = useConfigData() as { functionSwitchIds: number[] };

  useEffect(() => {
    // 获取第一个大类
    console.log("移动端footer，channelList更新", channelList);
    if (channelList && channelList.length > 0) {
      setChannelInfo(channelList[1]);
      setCount(5);
      setLoading(false);
    }
  }, [channelList]);
  const disPatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (location.pathname === "/") {
      handleJunmp(1);
    } else if (location.pathname.includes("/promotion")) {
      handleJunmp(4);
    } else if (location.pathname.includes("/mobile_personal_center")) {
      handleJunmp(5);
    }
  }, [location]);

  const redirectMapping = {
    1: "/",
    2: "/betby",
    3: "/mobile_deposit",
    4: "/promotion",
    5: "/mobile_personal_center"
  };

  const handleJunmp = (index: number) => {
    setActiveIndex(index);
    if (index === 5) {
      disPatch(setShowMobileHeader(false));
    } else {
      disPatch(setShowMobileHeader(true));
    }
  };

  const handlerClick = useCallback(
    (_index: SetStateAction<number>) => {
      if ((_index === 5 || _index === 3) && !getIsLogin()) {
        EventBus.emit("showModal", ModalType.showLoginModal)
        setCommonJumpto(redirectMapping[_index]);
        return;
      }
      handleJunmp(_index);
      if (location.pathname !== redirectMapping[_index]) {
        setDepostiSource(201);
        navigate(redirectMapping[_index]);
      }
    },
    [location]
  );

  return (
    <>
      {loading ? (
        <>
          <div
            className={`text-sm text-sub-text rounded-md flex flex-col items-center justify-center h-[55px] 
                    ${activeIndex === 1 ? "bg-[--mobile-footer-bg-color]" : ""}`}
          >
            <Skeleton.Node
              active
              style={{ height: "54px", width: "100%", backgroundColor: "#1b1e23" }}
            />
          </div>
        </>
      ) : (
        <>
          <div className="w-full flex flex-col">
            <div
              className="w-full flex justify-center items-center h-full text-sm"

            >
              <div
                className={`text-sm text-sub-text rounded-md flex flex-col items-center justify-center h-[55px] flex-1
                    ${activeIndex === 1 ? "bg-[--mobile-footer-bg-color]" : ""}`}
                onClick={() => handlerClick(1)}
              >
                <div className="flex flex-col items-center justify-center gap-y-1">
                  <svg
                    width="21"
                    height="20"
                    viewBox="0 0 21 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M17.4833 6.97498L11.7083 2.35831C10.8167 1.64164 9.375 1.64164 8.49166 2.34998L2.71666 6.97498C2.06666 7.49164 1.65 8.58331 1.79166 9.39998L2.9 16.0333C3.1 17.2166 4.23333 18.175 5.43333 18.175H14.7667C15.9583 18.175 17.1 17.2083 17.3 16.0333L18.4083 9.39998C18.5417 8.58331 18.125 7.49164 17.4833 6.97498ZM10.1 12.9166C8.95 12.9166 8.01666 11.9833 8.01666 10.8333C8.01666 9.68331 8.95 8.74998 10.1 8.74998C11.25 8.74998 12.1833 9.68331 12.1833 10.8333C12.1833 11.9833 11.25 12.9166 10.1 12.9166Z"
                      fill={`${activeIndex === 1 ? "var(--main-theme-color)" : "#737880"}`}
                    />
                  </svg>
                  <div
                    className={`text-sub-text  ${
                      activeIndex === 1 ? "text-[--mobile-nav-text-color]" : ""
                    }`}
                  >
                    <FormattedMessage id="home" />
                  </div>
                </div>
              </div>
              <div
                className={`  rounded-md flex flex-col items-center justify-center h-[55px] gap-y-1 flex-1 ${
                  activeIndex === 2 ? "bg-[--mobile-footer-bg-color]" : ""
                }`}
                onClick={() => handlerClick(2)}
              >
                {activeIndex === 2 ? (
                  <Image
                    width={20}
                    height={20}
                    preview={false}
                    src={channelInfo?.channelIcon1}
                    alt="slots"
                  />
                ) : (
                  <Image
                    width={20}
                    height={20}
                    preview={false}
                    src={channelInfo?.channelIcon}
                    alt="game1"
                  />
                )}
                <div
                  className={`text-sub-text  ${
                    activeIndex === 2 ? "text-[--mobile-nav-text-color]" : ""
                  }`}
                >
                  {channelInfo?.channelName}
                </div>
              </div>
              {functionSwitchIds?.includes(FuntionOpenMapping.show_deposit) && (
                <div
                  className={`  rounded-md flex flex-col items-center justify-center h-[55px] gap-y-1 relative flex-1`}
                  onClick={() => handlerClick(3)}
                >
                  <div className="bg-[--main-theme-color] w-[45px] h-[45px] rounded-full absolute -top-[40%] flex items-center justify-center">
                    <svg
                      width="35"
                      height="35"
                      viewBox="0 0 35 35"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g id="Iconly/Regular/Outline/Arrow - Up">
                        <path
                          id="Vector"
                          d="M31.0416 27.301C31.0416 27.6767 30.977 28.0276 30.8477 28.3539C30.7185 28.6801 30.5345 28.9669 30.2959 29.214C30.0573 29.4612 29.7789 29.654 29.4607 29.7924C29.1426 29.9308 28.7946 30 28.4168 30H6.76203C6.38422 30 6.02629 29.9308 5.68824 29.7924C5.3502 29.654 5.05193 29.4612 4.79342 29.214C4.53492 28.9669 4.3311 28.6801 4.18196 28.3539C4.03282 28.0276 3.95825 27.6767 3.95825 27.301V12.5008C3.95825 11.7494 4.22173 11.1117 4.74868 10.5877C5.27563 10.0637 5.91692 9.80173 6.67255 9.80173H28.3273C29.0829 9.80173 29.7242 10.0637 30.2512 10.5877C30.7781 11.1117 31.0416 11.7494 31.0416 12.5008V16.5345H24.2707C23.5151 16.5345 22.8738 16.7915 22.3469 17.3056C21.8199 17.8197 21.5564 18.4525 21.5564 19.2039C21.5763 19.718 21.6857 20.1727 21.8846 20.5682C22.0436 20.9044 22.3071 21.2108 22.675 21.4877C23.0429 21.7645 23.5748 21.9029 24.2707 21.9029H31.0416V27.301ZM26.985 8.43738H13.4434C14.5172 7.88374 15.5313 7.34986 16.4858 6.83576C17.321 6.40075 18.1462 5.96574 18.9615 5.53073C19.7767 5.09572 20.4131 4.75958 20.8704 4.5223C21.5664 4.14661 22.1878 3.9736 22.7346 4.00326C23.2815 4.03292 23.7438 4.12684 24.1216 4.28502C24.5591 4.50253 24.9369 4.78924 25.2551 5.14515L26.985 8.43738ZM22.9285 19.2039C22.9285 18.8282 23.0578 18.5118 23.3163 18.2548C23.5748 17.9977 23.8929 17.8692 24.2707 17.8692C24.6486 17.8692 24.9667 17.9977 25.2252 18.2548C25.4837 18.5118 25.613 18.8282 25.613 19.2039C25.613 19.5796 25.4837 19.9009 25.2252 20.1678C24.9667 20.4347 24.6486 20.5682 24.2707 20.5682C23.8929 20.5682 23.5748 20.4347 23.3163 20.1678C23.0578 19.9009 22.9285 19.5796 22.9285 19.2039Z"
                          fill="black"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
              )}
              {functionSwitchIds?.includes(FuntionOpenMapping.show_promotions) && (
                <div
                  className={` rounded-md flex flex-col items-center justify-center h-[55px] gap-y-1 flex-1 ${
                    activeIndex === 4 ? "bg-[--mobile-footer-bg-color]" : ""
                  }`}
                  onClick={() => handlerClick(4)}
                >
                  <svg
                    width="21"
                    height="20"
                    viewBox="0 0 21 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clipPath="url(#clip0_109_15125)">
                      <path
                        d="M17.7832 18.3331V8.33307H3.61658V18.3331H17.7832Z"
                        stroke={`${activeIndex === 4 ? "var(--main-theme-color)" : "#737880"}`}
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M10.7 18.3331V8.33307"
                        stroke={`${activeIndex === 4 ? "var(--main-theme-color)" : "#737880"}`}
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M17.7832 18.3331H3.61658"
                        stroke={`${activeIndex === 4 ? "var(--main-theme-color)" : "#737880"}`}
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M19.0332 5H2.36658V8.33333H19.0332V5Z"
                        stroke={`${activeIndex === 4 ? "var(--main-theme-color)" : "#737880"}`}
                        strokeWidth="2"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M7.36658 1.66693L10.6999 5.00026L14.0332 1.66693"
                        stroke={`${activeIndex === 4 ? "var(--main-theme-color)" : "#737880"}`}
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_109_15125">
                        <rect width="20" height="20" fill="white" transform="translate(0.699951)" />
                      </clipPath>
                    </defs>
                  </svg>

                  <div
                    className={`text-sub-text  ${
                      activeIndex === 4 ? "text-[--mobile-nav-text-color]" : ""
                    }`}
                  >
                    <FormattedMessage id="promotions" />
                  </div>
                </div>
              )}

              <div
                className={` rounded-md flex flex-col items-center justify-center h-[55px] gap-y-1 flex-1 ${
                  activeIndex === 5 ? "bg-[--mobile-footer-bg-color]" : ""
                }`}
                onClick={() => handlerClick(5)}
              >
                <svg
                  width="21"
                  height="20"
                  viewBox="0 0 21 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M11 10.65C10.9416 10.6417 10.8666 10.6417 10.8 10.65C9.33329 10.6 8.16663 9.40005 8.16663 7.92505C8.16663 6.41671 9.38329 5.19171 10.9 5.19171C12.4083 5.19171 13.6333 6.41671 13.6333 7.92505C13.625 9.40005 12.4666 10.6 11 10.65Z"
                    stroke={`${activeIndex === 5 ? "var(--main-theme-color)" : "#737880"}`}
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M16.5167 16.1499C15.0333 17.5083 13.0667 18.3333 10.9 18.3333C8.73333 18.3333 6.76666 17.5083 5.28333 16.1499C5.36666 15.3666 5.86666 14.5999 6.75833 13.9999C9.04166 12.4833 12.775 12.4833 15.0417 13.9999C15.9333 14.5999 16.4333 15.3666 16.5167 16.1499Z"
                    stroke={`${activeIndex === 5 ? "var(--main-theme-color)" : "#737880"}`}
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M10.9 18.3334C15.5024 18.3334 19.2333 14.6024 19.2333 10C19.2333 5.39765 15.5024 1.66669 10.9 1.66669C6.29761 1.66669 2.56665 5.39765 2.56665 10C2.56665 14.6024 6.29761 18.3334 10.9 18.3334Z"
                    stroke={`${activeIndex === 5 ? "var(--main-theme-color)" : "#737880"}`}
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>

                <div
                  className={`text-sub-text  ${
                    activeIndex === 5 ? "text-[--mobile-nav-text-color]" : ""
                  }`}
                >
                  <FormattedMessage id="my" />
                </div>
              </div>
            </div>
            <div className="h-[5px]"></div>
          </div>
        </>
      )}
    </>
  );
};

export default CommonMobileFooter;
