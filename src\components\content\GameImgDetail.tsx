import { useScreenWidth } from "@/utils/MobileDetectionContext";
import { Button, Progress } from "antd";
import React, { useEffect, useState } from "react";
import { FormattedMessage } from "react-intl";
import SlotsSwipe from "./SlotsSwipe";
import { GameChannel } from '@/utils/enums';

const GameImgDetail: React.FC = React.memo(({ gameType, providerids, selectedType, loading, datas, showText = true, hoveOp = true, setPageSize, pageSize, setPage, page, hoverTrans = true }) => {
    const [total, setTotal] = useState(0)
    const [totalDatas, setTotalDatas] = useState([])
    const { screenWidth } = useScreenWidth() 
    const [dataWidth, setDataWidth] = useState(0)

    useEffect((() => {
        setTotalDatas([])
        setTotal(0)
        setPage(1)
    }), [selectedType, providerids])

    useEffect(() => {
        if (datas) {
            setTotal(datas.total)
            if (datas.total < total) {
                console.log('没有数据', datas.total, total);
                if (datas.total === 0) {
                    setTotalDatas([])
                } else {
                    setCurrentData(datas, true)
                }
            } else {
                setCurrentData(datas, false)
            }

        }
    }, [datas])

    const setCurrentData = (datas, isNew) => {
        const GameProviders = datas?.GameProviders
        const gameApiInfo = datas?.gameApiInfo
        const finallyData = gameType == GameChannel.Casino_GameProviders ? GameProviders : gameApiInfo
        const type = gameApiInfo.length > 0 ? 1 : 2
        // console.log("finallyData=========", finallyData);

        // 添加到现有数据中，同样避免重复
        if(!isNew) {
            const updatedTotalData = totalDatas.concat(finallyData).reduce((acc, curr) => {
                if (type === 1) {
                    if (!acc.some(item => item.gameId === curr.gameId)) {
                        acc.push(curr);
                    }
                } else {
                    if (!acc.some(item => item.platformName === curr.platformName)) {
                        acc.push(curr);
                    }
                }
                return acc;
            }, []);
    
            setTotalDatas(updatedTotalData)
        } else {
            setTotalDatas(finallyData)
        }
        
    }

    useEffect(() => {
        if (screenWidth <= 1024) {
            setDataWidth(105)
            if (gameType == GameChannel.Casino_GameProviders) {
                if (location.pathname.includes('gameList')) {
                    setDataWidth(75)
                } else {
                    setDataWidth(150)
                }

            }
        } else {
            setDataWidth(150)
            if (gameType == GameChannel.Casino_GameProviders) {
                setDataWidth(184)
            }
        }
    }, [screenWidth, gameType])

    return (
        <div className="flex flex-col w-full">
            <div style={{
                gridTemplateColumns: `repeat(auto-fill, minmax(${dataWidth + 'px'}, 1fr))`
            }} className={`grid gap-x-1 w-full`}>
                <SlotsSwipe gameType={gameType} useControl={false} useSlider={false} datas={totalDatas} showText={showText} sctionId={selectedType} />
            </div>
            {
                showText && <div className="flex mt-[12px] items-center justify-center space-x-2">
                    <span className="text-[--game-single-style-page-text-color]">
                        {Math.min(page * pageSize, total)} / {total}
                    </span>
                    <span className="w-[100px] myProgress">
                        <Progress strokeColor="var(--game-single-load-progress-bg-color)" style={{ color: "white" }} size={"small"} percent={Math.ceil((page * pageSize) / total * 100)} showInfo={true} />
                    </span>
                </div>
            }

            {showText &&
                <div className={`flex justify-center items-center `}>
                    <Button loading={loading} onClick={() => setPage(page + 1)} className={` mt-[12px] btn !py-5 `} disabled={totalDatas.length - total >= 0 || loading}>
                        <span className={`${loading && 'hidden'}`}>
                            {
                                totalDatas.length - total >= 0 ? <FormattedMessage id="no_more_data"></FormattedMessage>
                                    : <FormattedMessage id="load_more"></FormattedMessage>
                            }
                        </span>
                    </Button>

                </div>
            }


        </div>

    )
})

export default GameImgDetail