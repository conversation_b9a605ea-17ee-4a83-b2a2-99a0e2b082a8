// import affiliateIcon from '@/assets/quickJump/affiliate.svg'
// import bounsIcon from '@/assets/quickJump/Bouns.svg'
// import casinoIcon from '@/assets/quickJump/casino.svg'
// import weeklyIcon from '@/assets/quickJump/weekly.svg'
import rightArrowWhiteIcon from "@/assets/rightArrowWhite_two.svg";
import { useJump } from "@/common_components/context/useJumpContext";
import { BackStageMessage } from "@/protos/BackStage";
import { setActivitySource } from "@/store/popup";
import { useScreenWidth } from '@/utils/MobileDetectionContext';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useAllGame } from '@/components/games/AllGameProvider';

const QuickJump: React.FC = () => {
  const navigate = useNavigate();
  const { screenWidth } = useScreenWidth();
  const { allConfigData } = useAllGame() as {
    allConfigData: BackStageMessage.ResConfigDataMessage;
  };
  const [bigEnternance, setBigEnternance] = useState<BackStageMessage.IQuickAccessInfo[] | null>(
    null
  );
  const [smallEnternance, setSmallEnternance] = useState<
    BackStageMessage.IQuickAccessInfo[] | null
  >(null);
  const dispatch = useDispatch();

  const { handleJump } = useJump() || {};
  useEffect(() => {
    if (allConfigData?.quickAccessList?.length > 0) {
      console.log("快速跳转个数", allConfigData?.quickAccessList);
      const quickAccessList = allConfigData.quickAccessList.sort((a, b) => a.sort - b.sort);
      setBigEnternance(quickAccessList.filter((item) => item.entranceType === 1));
      setSmallEnternance(quickAccessList.filter((item) => item.entranceType === 2));
    }
  }, [allConfigData]);

  const handleCustomer = (item: BackStageMessage.BannerInfo, e: React.MouseEvent) => {
    console.log("banner点击弹框", item);
    e.stopPropagation();
    const jumpType = item?.jumpType;
    const url = item?.externalLinks;
    const popupLinks = item?.popupLinks;
    const innerLinks = item?.innerLinks;
    const isJump = item.isJump;
    if (isJump && isJump > 0) {
      const popupInfo = BackStageMessage.PopupInfo.create({
        jumpType: jumpType,
        popupLinks: popupLinks,
        externalLinks: url,
        popupType: 1,
        innerLinks: innerLinks,
        notLoginJump: 1
      });
      dispatch(setActivitySource(4));
      handleJump(popupInfo, navigate);
    }
  };

  return (
    <>
      {screenWidth > 1024 ? (
        <div className="w-full flex gap-x-6 flex-1">
          {bigEnternance?.length > 0 && (
            <>
              {bigEnternance.map((item, index) => (
                <div
                  key={item.quickAccessId}
                  onClick={(e) => handleCustomer(item, e)}
                  className={`${
                    item.isJump > 0 ? "cursor-pointer" : ""
                  } flex relative rounded-xl aspect-[284/268] flex-1`}
                  style={{
                    backgroundImage: `url(${item.imageUrl})`,
                    backgroundSize: "cover",
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "center"
                  }}
                >
                  <div className="absolute font-bold text-lg text-primary-text top-[8px] left-0 flex justify-between items-center w-full px-2">
                    <span>{item.entranceName}</span>
                    <img src={rightArrowWhiteIcon} alt="" />
                  </div>
                </div>
              ))}
            </>
          )}
          {smallEnternance && smallEnternance.length > 1 && (
            <div className="flex flex-col gap-y-3 flex-1 ">
              {smallEnternance.slice(0, 2).map((item, index) => (
                <div key={item.quickAccessId} className="flex-1 aspect-[280/128]">
                  <div
                    onClick={(e) => handleCustomer(item, e)}
                    className={`${
                      item.isJump > 0 ? "cursor-pointer" : ""
                    } flex relative rounded-xl h-full`}
                    style={{
                      backgroundImage: `url(${item.imageUrl})`,
                      backgroundSize: "cover",
                      backgroundRepeat: "no-repeat",
                      backgroundPosition: "center"
                    }}
                  >
                    <div className="absolute font-bold text-lg text-primary-text top-[8px] left-0 flex justify-between items-center w-full px-2">
                      <span>{item.entranceName}</span>
                      <img src={rightArrowWhiteIcon} alt="" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          {smallEnternance && smallEnternance.length > 3 && (
            <div className="flex flex-col gap-y-3 flex-1 ">
              {smallEnternance.slice(2).map((item, index) => (
                <div key={item.quickAccessId} className="flex-1 aspect-[280/128]">
                  <div
                    onClick={(e) => handleCustomer(item, e)}
                    className={`${
                      item.isJump > 0 ? "cursor-pointer" : ""
                    } relative rounded-xl h-full`}
                    style={{
                      backgroundImage: `url(${item.imageUrl})`,
                      backgroundSize: "cover",
                      backgroundRepeat: "no-repeat",
                      backgroundPosition: "center"
                    }}
                  >
                    <div className="absolute font-bold text-lg text-primary-text top-[8px] left-0 flex justify-between items-center w-full px-2">
                      <span>{item.entranceName}</span>
                      <img src={rightArrowWhiteIcon} alt="" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      ) : (
        <div className="w-full flex flex-col  gap-y-[12px] flex-1 justify-between ">
          <div className="w-full flex justify-between items-center flex-wrap gap-y-4">
            {bigEnternance &&
              bigEnternance.map((item, index) => (
                <div
                  key={item.quickAccessId}
                  onClick={(e) => handleCustomer(item, e)}
                  className={`${
                    item.isJump > 0 ? "cursor-pointer" : ""
                  } relative h-[186px] w-[48%] rounded-xl`}
                  style={{
                    backgroundImage: `url(${item.imageUrl})`,
                    backgroundSize: "cover",
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "center"
                  }}
                >
                  <div className="absolute font-bold text-lg text-primary-text top-[8px] left-0 flex justify-between items-center w-full px-2">
                    <span>{item.entranceName}</span>
                    <img src={rightArrowWhiteIcon} alt="" />
                  </div>
                </div>
              ))}
          </div>
          {smallEnternance && smallEnternance.length > 0 && (
            <div className="flex w-full justify-between gap-y-3 flex-wrap flex-1 overflow-hidden">
              {smallEnternance.map((item, index) => (
                <div
                  key={item.quickAccessId}
                  onClick={(e) => handleCustomer(item, e)}
                  className={`${
                    item.isJump > 0 ? "cursor-pointer" : ""
                  } relative h-[108px] rounded-xl w-[48%]`}
                  style={{
                    backgroundImage: `url(${item.imageUrl})`,
                    backgroundSize: "cover",
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "center"
                  }}
                >
                  <div className="absolute font-bold text-lg text-primary-text top-[8px] left-0 flex justify-between items-center w-full px-2">
                    <span>{item.entranceName}</span>
                    <img src={rightArrowWhiteIcon} alt="" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default QuickJump;
