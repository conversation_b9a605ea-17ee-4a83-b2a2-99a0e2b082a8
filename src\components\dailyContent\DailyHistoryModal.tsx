import { ActivityMessage } from "@/protos/Activity";
import { ProtoMessage } from "@/protos/common";
import { getDailyContestHistoryData } from "@/service/activity";
import { useEffect, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useScreenWidth } from "../../utils/MobileDetectionContext";
import CommonModal from "../CommonModal";
import { useUtils } from "../useUtils";
import DailyContentTable from "./DailyContentTable";
import { useToastUtils } from "@/hooks/utils/useToastUtils";

const DailyHistoryModal: React.FC = ({ showModal, handleCancel }) => {
  if (!showModal) {
    return null;
  }
  const { screenWidth } = useScreenWidth()  ;
  const {  formatDate2 } = useUtils();
  const { checkResponse } = useToastUtils();
  const [loading, setLoading] = useState(false);
  const [historyData, setHistoryData] = useState<ActivityMessage.ResDailyContestHistoryDataMessage>();
  function handleClose() {}

  useEffect(() => {
    if (showModal) {
      getHistoryData();
    }
  }, [showModal]);

  const getHistoryData = async () => {
    try {
      setLoading(true);
      const res = (await getDailyContestHistoryData(
        ActivityMessage.ReqDailyContestHistoryDataMessage.create({
          msgID: ProtoMessage.MID.ReqDailyContestData
        })
      )) as ActivityMessage.ResDailyContestHistoryDataMessage;
      if (checkResponse(res)) {
        console.log("获取到daily历史数据为：", res);
        setHistoryData(res);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <CommonModal
        width={screenWidth > 1024 ? "56%" : "100%"}
        footer={null}
        title={
          <div className="bg-content-second-gray p-4 flex gap-x-5 items-center rounded-tl-2xl rounded-tr-2xl">
            <span className="text-white text-lg/6 font-medium">
              <FormattedMessage id="history" />
            </span>
            <span className="flex text-sub-text items-center">
              {formatDate2(historyData?.startDate)} ~ {formatDate2(historyData?.endDate)}
            </span>
          </div>
        }
        open={showModal}
        onCancel={handleCancel}
        afterClose={handleClose}
      >
        <div className="hide-scrollbar flex flex-col w-full p-5 overflow-y-auto select-none space-y-1 text-sub-text h-[600px]">
          <DailyContentTable loading={loading} datas={historyData?.rankInfo} key={3} />
        </div>
      </CommonModal>
    </>
  );
};

export default DailyHistoryModal;
