import { setContentPercentage } from "@/store";
import { useScreenWidth } from "@/utils/MobileDetectionContext";
import { createContext, useContext, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

const ConfigContext = createContext(null);
export const ProjectWidthProvider = ({children}) => {
  const { screenWidth } = useScreenWidth();
    const dispatch = useDispatch();
    const [leftSideWith, setLeftSideWith] = useState("270px");
    const openNotifi = useSelector((state: InboxInfo) => state.systemReducer.openNotifi);
  
    useEffect(() => {
        const timer = setTimeout(() => {
          if (screenWidth <= 1024) {
            setLeftSideWith("0px");
            dispatch(setContentPercentage("97%"));
          } else {
            setLeftSideWith("270px");
            dispatch(setContentPercentage("72.51%"));
          }
        }, 500);
        return () => {
          clearTimeout(timer);
        };
      }, [screenWidth]);

    const value = useMemo(() => ({
        leftSideWith:leftSideWith
    }), [leftSideWith]);

    useEffect(() => {
      if (screenWidth <= 1024) {
        dispatch(setContentPercentage("98%"));
      } else {
        dispatch(setContentPercentage(openNotifi ? "98%" : "72.51%"));
      }
      // dispatch(setContentPercentage("68vw"))
    }, [openNotifi]);


    return (
        <ConfigContext.Provider value={value}>
            {children}
        </ConfigContext.Provider>
    );
}
export const useProjectWidth = () => useContext(ConfigContext);