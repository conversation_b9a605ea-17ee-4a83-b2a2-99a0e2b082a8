import huobi1 from '@/assets/content/huobi1.webp';
import marquee from '@/assets/content/marquee.webp';
import marquee2 from '@/assets/content/marquee2.webp';
import marquee3 from '@/assets/content/marquee3.webp';
import { useState } from 'react';
import Marquee from 'react-fast-marquee';
import { FormattedMessage } from 'react-intl';
const LiveSwipe: React.FC = () => {
    const [selectIndex, setSelectIndex] = useState(1)
    function changeDataImage(index) {
        console.log('点击', index);
    }

    const datas = (
        Array.from({ length: 22 }).map((item, index) => (
            <div key={index} className=' w-[72px] flex-col justify-center items-center flex select-none'>
                <img src={index % 3 == 0 ? marquee2 : index % 3 == 2 ? marquee : marquee3} className='w-[64px]  cursor-pointer'
                    onClick={() => changeDataImage(index)} alt=""></img>
                <div className='flex mt-2 items-center justify-center space-x-1'>
                    <img src={huobi1} className='w-[12px]' alt=""/>
                    <span className='text-xs w-[40px] truncate'>User1sdfsadfasdf</span>
                </div>
                <div className='text-[#31B554] flex mt-1 items-center justify-center'>
                    $26.40
                </div>
            </div>
        ))
    )

    const outDiv = (
        <div className='flex'>
            {datas}
        </div>
    )
    function changeData(index: number) {
        setSelectIndex(index)
    }

    return (
        <div className="w-full bg-content-basic-gray flex justify-center items-center mb-2 relative">
            <div className="flex  w-[100%] overflow-hidden">
                <Marquee play={true} pauseOnHover gradient={false}>
                    {
                        outDiv
                    }
                </Marquee>

            </div>
            <div className='absolute px-2 bg-[--popver-xl-bg-color] gap-x-2 h-[30px] top-0 left-0  items-center justify-center flex rounded-md' style={{ zIndex: 999 }}>
                <div className='cirDiv_red'></div>
                <div className='font-bold text-[12px] antialiased '><FormattedMessage id='live_wins' /></div>
                <div className='space-x-2 text-xs font-bold flex '>
                    <button onClick={() => changeData(1)} className={`${selectIndex === 1 ? 'text-[--wallet-title-text-color] bg-[#273954]' : 'text-[--main-text-color] '} p-[2px] rounded-[4px]`}>M</button>
                    <button onClick={() => changeData(2)} className={`${selectIndex === 2 ? 'text-[--wallet-title-text-color] bg-[#273954]' : 'text-[--main-text-color] '} p-[2px] rounded-[4px]`}>W</button>
                    <button onClick={() => changeData(3)} className={`${selectIndex === 3 ? 'text-[--wallet-title-text-color] bg-[#273954]' : 'text-[--main-text-color]'} p-[2px] rounded-[4px]`}>D</button>
                </div>
            </div>
        </div>
    )
}

export default LiveSwipe;