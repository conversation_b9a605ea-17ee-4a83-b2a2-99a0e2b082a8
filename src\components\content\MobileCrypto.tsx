
// import IconBanner from "@/assets/content/icon-banner-title.svg";
import IconBanner from "@/assets/content/crypto_future.png";
import { useEffect, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useAllGame } from "../games/AllGameProvider";
import { useUtils } from "../useUtils";

const MobileCrypto: React.FC = () => {
    const [cryptoList, setCryptoList] = useState<ExchangeRate[]>([])
    const { allConfigData } = useAllGame() as {
        allConfigData: ResConfigDataMessage
    }
    const { cronMap, formatNumberQfw } = useUtils()

    useEffect(() => {
        if (allConfigData && allConfigData.exchangeRateList) {
            const exchangeRateList = allConfigData.exchangeRateList.filter(item => Math.floor(item.currencyId / 1000) === 2)
            setCryptoList(exchangeRateList)
        }
    }, [allConfigData])


    return (
        <div className="flex flex-col w-full relative select-none">
            <div className='flex items-center gap-x-2 '>
                <img src={IconBanner} className='h-[24px]' alt=""/>
                <div className="lg:text-base text-sm lg:font-semibold font-bold select-none tracking-wider lg:uppercase">
                    <FormattedMessage id={'Crypto Futures'} />
                </div>
            </div>

            <div className="flex flex-col w-full gap-y-2 mt-4 h-[270px] overflow-auto">
                {
                    cryptoList.length > 0 && cryptoList.map((item, index) => (
                        <div key={index} className="flex w-full items-center justify-between h-[32px]">
                            <div className="flex gap-x-[10px] items-center">
                                <img className="w-[24px] h-[24px]" src={cronMap[item.currencyId]?.icon} alt=""/>
                                <span className=" text-pxs font-bold">{cronMap[item.currencyId]?.name}</span>
                            </div>
                            <span className="text-pxs font-medium">${formatNumberQfw(6666666, 2)}</span>
                            <span className=" text-pxs text-[#3AD69E] font-medium">0.00%</span>
                        </div>
                    ))
                }
            </div>




        </div>
    )
}

export default MobileCrypto;