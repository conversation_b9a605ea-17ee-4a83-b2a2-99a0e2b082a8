import { Modal } from "antd";
import { useIntl } from "react-intl";
import { useEffect, useRef } from "react";
import "./VersionChecker.css";
import Version_Icon from "@/assets/fixedTemplateBrazil/Version_Icon.webp";
import { Button as ButtonSelf } from "@/templateBrazil/components/button/button";

const VERSION_KEY = 'app_version';
const CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟检查一次

export const BrazilVersionChecker = () => {
  const intl = useIntl();
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const isModalVisible = useRef<boolean>(false);

  const checkVersion = async () => {
    try {
      // 通过请求获取最新版本号
      const response = await fetch('/version.json?' + new Date().getTime());
      const data = await response.json();
      return data.version;
    } catch (error) {
      console.error('Failed to fetch version:', error);
      return null;
    }
  };

  const handleNewVersion = (newVersion: string) => {
    isModalVisible.current = true;
    Modal.confirm({
      centered: true,
      icon: null,
      closable:true,
      // title: intl.formatMessage({ id: 'new_version_prompt' }),
      title: <>
        <span className="!text-[18px] text-[#FFF]">{intl.formatMessage({ id: 'version_tile' })}</span>
          
      </>,
      closeIcon:<>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
          onClick={() => {
            isModalVisible.current = false;
            // 重新开始检查
            if (!timerRef.current) {
              timerRef.current = setInterval(checkAndUpdateVersion, CHECK_INTERVAL);
            }
          }}
        >
          <path d="M5 5L19 19" stroke="#E8E9E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M5 19L19 5" stroke="#E8E9E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </>,
      content: <>
        <div className="flex flex-col items-center justify-center gap-y-[14px] mt-[28px] mb-[24px]">
          <img src={Version_Icon} alt="" className="object-cover"/>
          <span className="text-[#78828A] text-center">{intl.formatMessage({ id: 'version_content' })}</span>
        </div>
      </>,
      okText: intl.formatMessage({ id: 'okText' }),
      cancelText: intl.formatMessage({ id: 'cancelText' }),
      className: 'custom-version-modal_brazil',
      maskStyle: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
      },
      okButtonProps: {
        className: 'custom-ok-button_brazil'
      },
      cancelButtonProps: {
        className: 'custom-cancel-button_brazil'
      },
      onOk: () => {
        isModalVisible.current = false;
        if (newVersion) {
          localStorage.setItem(VERSION_KEY, newVersion);
        }
        localStorage.removeItem('gameChannelMapping');
        localStorage.removeItem('persist:root');
        localStorage.removeItem('queryList');
        localStorage.removeItem('gameChannelData');
        localStorage.removeItem('locale');
        window.location.reload();
      },
      onCancel: () => {
        isModalVisible.current = false;
        // 重新开始检查
        if (!timerRef.current) {
          timerRef.current = setInterval(checkAndUpdateVersion, CHECK_INTERVAL);
        }
      }
    });
  };

  const checkAndUpdateVersion = async () => {
    // 如果弹框正在显示，不进行检查
    if (isModalVisible.current) return;
    
    const newVersion = await checkVersion();
    if (!newVersion) return;
    
    const storedVersion = localStorage.getItem(VERSION_KEY);
    if (!storedVersion) {
        localStorage.setItem(VERSION_KEY, newVersion);
    } else if (storedVersion !== newVersion) {
        handleNewVersion(newVersion);
    }
  };

  useEffect(() => {
    // 初始化检查
    checkAndUpdateVersion();

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // 设置定时检查
    timerRef.current = setInterval(checkAndUpdateVersion, CHECK_INTERVAL);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  return null;
};