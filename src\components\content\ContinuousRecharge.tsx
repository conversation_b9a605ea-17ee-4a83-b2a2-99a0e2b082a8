// import IconBanner from "@/assets/content/icon-banner-title.svg";
import { useJump } from "@/common_components/context/useJumpContext";
import { ActivityMessage } from "@/protos/Activity";
import { setContinousCount } from "@/store";
import { setActivitySource } from "@/store/popup";
import { Col, Row } from "antd";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useScreenWidth } from "../../utils/MobileDetectionContext";
import { useUtils } from "../useUtils";
import { useQueryActivityData } from "@/hooks/queryHooks/useQueryActivityData";

const ContinuousRecharge: React.FC = memo(() => {
  console.log("ContinuousRecharge执行了============================");

  const { screenWidth } = useScreenWidth();
  const [loading, setLoading] = useState(true);
  const { formatNumberQfw } = useUtils();
  const navigate = useNavigate();
  const { handleJump } = useJump() || {};
  const [maxRate, setMaxRate] = useState(0);
  const [continuousList, setContinuousList] = useState<
    ActivityMessage.IContinuousDepositInfo[] | null
  >();
  const dispatch = useDispatch();

  const { data: activitData } = useQueryActivityData(1000 * 10);

  const getActivityData = useCallback(async () => {
    try {
      if (activitData) {
        console.debug("activitData 连充活动", activitData);

        setContinuousList(activitData.continuousDepositInfo);
        const totalRate = activitData.continuousDepositInfo?.depositList?.reduce((acc, cur) => {
          acc += cur.giveawayRate;
          return acc;
        }, 0);
        setMaxRate(totalRate);
        dispatch(setContinousCount(activitData.continuousDepositInfo?.depositList?.length));
      }
    } finally {
      setLoading(false);
    }
  }, [activitData]);

  useEffect(() => {
    getActivityData();
  }, [activitData]);

  const containerRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartX(e.pageX - containerRef.current.offsetLeft);
    setScrollLeft(containerRef.current.scrollLeft);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    const x = e.pageX - containerRef.current.offsetLeft;
    const scroll = scrollLeft - (x - startX);
    containerRef.current.scrollLeft = scroll;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseLeave = () => {
    if (isDragging) {
      setIsDragging(false);
    }
  };

  return (
    <>
      {screenWidth > 1024 && (
        <div
          className=" w-full rounded-xl h-[140px] flex px-[38px] justify-between overflow-auto relative"
          style={{ background: "linear-gradient(270deg, #1B1E23 0%, #25282F 100%)" }}
        >
          <Row className="w-full ">
            <Col span={6} className="">
              <div className="flex flex-col h-full justify-center">
                <span className="text-primary-text text-[16px] font-medium">
                  <FormattedMessage id="great_bonus_for_every_deposit" />
                </span>
                <span className="flex gap-x-1 items-center text-[#FAE654] text-[32px] font-semibold">
                  <span>
                    <FormattedMessage id="up_to" />
                  </span>
                  <span>{formatNumberQfw((maxRate ?? 0) * 100, 0)}%</span>
                </span>
              </div>
            </Col>
            <Col span={13}>
              <div
                className=" flex items-center gap-x-[24px] overflow-auto h-full flex-shrink-0 justify-around "
                ref={containerRef}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseLeave}
              >
                {continuousList?.depositList?.map((item, index) => (
                  <img
                    alt=""
                    key={index}
                    src={item.icon}
                    className="w-[97px] h-[114px] object-cover flex-shrink-0"
                  />
                ))}
              </div>
            </Col>
            <Col span={5} className="">
              <div className="flex flex-col gap-y-[15px] justify-center w-full items-center h-full">
                {continuousList?.buttonList?.map((item, index) => (
                  <button
                    key={index}
                    style={{ background: index === 0 ? "var(--main-theme-liner)" : "#2E323A" }}
                    className={`${
                      index === 0
                        ? "custom_button_shadow text-[#090A0C]"
                        : "text-[rgba(255, 255, 255, 0.70)]"
                    } 
                                            w-[166px] h-[36px] flex items-center text-sm font-semibold rounded-lg justify-center`}
                    onClick={() => {
                      const data = JSON.parse(JSON.stringify(item));
                      data.depositSource = 102;
                      dispatch(setActivitySource(6));
                      handleJump(data, navigate);
                    }}
                  >
                    <span>{item.name}</span>
                  </button>
                ))}
              </div>
            </Col>
          </Row>
        </div>
      )}
      {screenWidth <= 1024 && (
        <div
          className=" rounded-xl w-full flex px-[9px] flex-col py-[8px] gap-y-[10px] relative"
          style={{ background: "linear-gradient(270deg, #1B1E23 0%, #25282F 100%)" }}
        >
          <div className="flex w-full justify-between items-center">
            <span className="text-primary-text text-pxs font-medium">
              <FormattedMessage id="great_bonus_for_every_deposit" />
            </span>
            <span className="flex gap-x-1 items-center text-[#FAE654] text-[16px] font-medium">
              <span>
                <FormattedMessage id="up_to" />
              </span>
              <span>{formatNumberQfw((maxRate ?? 0) * 100, 0)}%</span>
            </span>
          </div>
          <div className="flex w-full gap-x-[3px] overflow-auto items-center justify-between ">
            {continuousList?.depositList?.map((item, index) => (
              <img
                alt=""
                key={index}
                src={item.icon}
                className="w-[74px] h-[86px] rounded-[4px] flex flex-shrink-0"
              />
            ))}
          </div>
          <div className="flex justify-between gap-x-[10px]">
            {continuousList?.buttonList?.map((item, index) => (
              <button
                key={index}
                style={{ background: index === 0 ? "var(--main-theme-liner)" : "#2E323A" }}
                className={`${
                  index === 0
                    ? "custom_button_shadow text-[#090A0C]"
                    : "text-[rgba(255, 255, 255, 0.70)]"
                } w-[48%] h-[36px] flex items-center text-sm font-semibold rounded-lg justify-center `}
                onClick={() => {
                  const data = JSON.parse(JSON.stringify(item));
                  data.depositSource = 102;
                  dispatch(setActivitySource(6));
                  handleJump(data, navigate);
                }}
              >
                {item.name}
              </button>
            ))}
          </div>
        </div>
      )}
    </>
  );
});

export default ContinuousRecharge;
