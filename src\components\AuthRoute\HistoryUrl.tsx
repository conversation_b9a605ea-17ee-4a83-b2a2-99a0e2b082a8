import { useJump } from "@/common_components/context/useJumpContext";
import { PopuTriggerType } from "@/utils/enums";
import { memo, useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

const HistoryUrl = memo(() => {
  const location = useLocation();
  const [prevLocation, setPrevLocation] = useState<string | null>(null);
  const { refreshPopupList } = useJump() || {};

  useEffect(() => {
    // 记录前一个 URL
    console.log("AuthRoute=====", location, prevLocation);
    
    if (prevLocation && prevLocation.includes("gameDetail") && location.pathname == "/") {
      console.log("AuthRoute=====由游戏页面跳转首页");
      localStorage.setItem("preGame", prevLocation);
      if (refreshPopupList) {
        console.log("AuthRoute=====刷新弹窗");
        refreshPopupList(PopuTriggerType.RETURN_LOBBY);
      }
    }
    setPrevLocation(location.pathname);
  }, [location]);
});

export default HistoryUrl;
