import { useJump } from "@/common_components/context/useJumpContext";
import { BackStageMessage } from "@/protos/BackStage";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAllGame } from "../games/AllGameProvider";

const ContentJump: React.FC = () => {
    const [groupMenu, setGroupMenu] = useState<{ [key: string]: BackStageMessage.IBottomMenuInfo[] }>({})
    const { allConfigData } = useAllGame() as {
        allConfigData: BackStageMessage.ResConfigDataMessage
    }
    const {handleJump} = useJump() || {}

    useEffect(() => {
        if (allConfigData && allConfigData.bottomMenuList) {
            allConfigData.bottomMenuList.sort((a, b) => (a.menuSort || 0) - (b.menuSort || 0)) // 根据menuOrder排序
            const gMenu = allConfigData.bottomMenuList.reduce((acc, curr) => {
                const menuId = curr.menuId
                const menuName = curr.menuName
                const key = `${menuId}_${menuName}`
                if (!acc[key]) {
                    acc[key] = []
                }
                acc[key].push(curr)
                return acc
            }, {})

            Object.keys(gMenu).forEach(key => {
                gMenu[key].sort((a, b) => (a.subMenuSort || 0) - (b.subMenuSort || 0)) // 根据menuOrder排序
            })
            setGroupMenu(gMenu)
            console.log('获取到底部菜单', allConfigData.bottomMenuList, gMenu);
        }
    }, [allConfigData])

    const navigate = useNavigate()

    const handleCustomer = (item: BackStageMessage.BannerInfo) => {
        console.log('处理跳转', item);
        const popupInfo = BackStageMessage.PopupInfo.create({
            jumpType: item.jumpType,
            popupLinks: item.popupLinks,
            externalLinks: item.externalLinks,
            popupType: 2,
            innerLinks: item.innerLinks,
            notLoginJump: item.notLoginJump,
        })
        handleJump(popupInfo, navigate)
    }

    return (
        <>
            <div className="flex flex-col w-full overflow-auto">
                <div className="flex justify-between">
                    {
                        Object.keys(groupMenu).map((key, pIndex) => (

                            <div className="flex flex-col gap-y-4 text-sub-text text-base" key={key}>
                                <span className="text-base font-semibold pb-3 text-primary-text">
                                    {/* <FormattedMessage id={key.split('_')[1]?.toLowerCase()} /> */}
                                    {key.split('_')[1]}
                                </span>
                                {
                                    groupMenu[key].map((item, index) => (
                                        <div key={pIndex + '_' + index}>
                                            {
                                                item.type == 1 && (
                                                    <div key={item.subMenuName} className=" cursor-pointer text-sm/6 font-normal"
                                                        onClick={() => handleCustomer(item)}>
                                                        {item.subMenuName}
                                                    </div>
                                                )
                                            }
                                            {
                                                item.type == 2 && (
                                                    <div className="flex flex-wrap gap-x-4 w-[90%] items-center gap-y-4">
                                                        {item.iconList?.map((iconInfo, index) => (
                                                            <img onClick={() => handleCustomer(iconInfo)}
                                                                key={iconInfo.id} src={iconInfo.icon} alt="" className="w-[25px] align-middle cursor-pointer" />
                                                        ))}
                                                    </div>

                                                )
                                            }
                                        </div>

                                    ))
                                }
                            </div>
                        ))
                    }
                </div>
            </div>
        </>

    )
}

export default ContentJump;