import { setIsStoreLogin, setQuestHost, setUserToken } from "@/store";
import { getHallHost, getIsLogin, getToken } from "@/utils/commonUtil";
import { useEffect } from "react";
import { useDispatch } from "react-redux";

const ReduxInitializer = () => {
  const dispatch = useDispatch();

  // 只在客户端执行的代码
  useEffect(() => {
    // 客户端相关的副作用
    dispatch(setIsStoreLogin(getIsLogin() ? true : false));
    dispatch(setQuestHost(getHallHost()));
    dispatch(setUserToken(getToken()));

    // 禁止右键菜单
    const handleContextMenu = (e) => e.preventDefault();
    document.addEventListener("contextmenu", handleContextMenu);

    return () => {
      document.removeEventListener("contextmenu", handleContextMenu);
    };
  }, [dispatch]);

  return null; // 组件不需要渲染任何 UI
};

export default ReduxInitializer;
