
import { updatePersonIctem, updatePersonInfo } from "@/store";
import { useDispatch, useSelector } from "react-redux";

export function usePersonInfoUtils() {
  const dispatch = useDispatch()
  const personValue = useSelector((state: any) => state.systemReducer.personInfo);

  const updateInfo = (obj: {}) => {
    dispatch(updatePersonInfo(obj));
  };

  const updateIctem = (obj: {}) => {
    dispatch(updatePersonIctem(obj));
  };

  return {
    personValue,
    updateInfo,
    updateIctem
  };
}
