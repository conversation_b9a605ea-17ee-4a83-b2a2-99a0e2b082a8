import { ProtoMessage } from "@/protos/common";
import { LoginMessage } from "@/protos/Login";
import { getWebSiteModel } from "@/service/user";
import { ResWebSiteModelMessage } from "@/types/login";
import { QueryKey } from "@/utils/QueryKey";
import { Router } from "@remix-run/router";
import { useQuery } from "@tanstack/react-query";
import { Suspense, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RouterProvider } from "react-router-dom";
import initWebSite from "../useWebSite";
import BrazilTemplateLoading from "./BrazilTemplateLoading";
const HOST_URL = import.meta.env.VITE_HOST;
type WebsiteConfig = {
  currentTemplate: Router | null;
};

// 预加载网站数据
export const prefetchWebSiteData = async (queryClient: any) => {
  await queryClient.prefetchQuery({
    queryKey: [QueryKey.WebSiteData],
    queryFn: async () => {
      const webSite = await getWebSiteModel(
        LoginMessage.ReqWebSiteModelMessage.create({
          msgID: ProtoMessage.MID.ReqWebSiteModel
        })
      );
      if (webSite.error > 0) {
        window.alert("站点信息获取失败，请联系管理员");
        return null;
      }
      return webSite;
    },
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
  });
};

const RouterComponent = ({ router }: { router: Router | null }) => {
  if (!router) {
    throw new Promise((resolve) => {
      setTimeout(resolve, 100);
    });
  }

  return <RouterProvider router={router} />;
};

export const WebsiteConfig: React.FC = () => {
  const dispatch = useDispatch();
  const isInit = useRef(false);
  const [currentTemplate, setCurrentTemplate] = useState<Router | null>(null);
  const Logo = useSelector((state: any) => state.systemReducer.pcLogo);

  const {
    data: webSite
  } = useQuery({
    queryKey: [QueryKey.WebSiteData],
    queryFn: async () => {
      let webSite = null;
      try {
        webSite = await getWebSiteModel(
          LoginMessage.ReqWebSiteModelMessage.create({
            msgID: ProtoMessage.MID.ReqWebSiteModel
          })
        ) as ResWebSiteModelMessage;
        debugger
        if (webSite.error > 0) {
          window.alert("站点信息获取失败，请联系管理员");
          return null;
        }
      } catch (error) {
        window.alert("站点信息获取失败，请联系管理员");
        return null;
      }
      return webSite;
    },
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
  });

  useEffect(() => {
    if (webSite && !isInit.current) {
      isInit.current = true;
      console.time('initWebSite');
      initWebSite(webSite, dispatch).then((res) => {
        console.timeEnd('initWebSite');
        setCurrentTemplate(res);
      });
    }
  }, [webSite]);
  console.log("当前地址访问"+window.location.origin+'对应地址'+HOST_URL)
  return (
    <>
      <Suspense fallback={
        <div className="loader_contioner">
          {
            HOST_URL.indexOf(window.location.origin) != -1 ? <BrazilTemplateLoading logo={webSite?.webSiteInfo?.siteLogo}/> 
              :
            <div className="loader ">Lading...</div>
          }
        </div>        
      } >
        <RouterComponent router={currentTemplate} />
      </Suspense>
    </>
  );
};

export default WebsiteConfig;