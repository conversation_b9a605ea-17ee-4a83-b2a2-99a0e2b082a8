import head1000Icon from "@/assets/headerImg/1000.webp";
import head1001Icon from "@/assets/headerImg/1001.webp";
import head1002Icon from "@/assets/headerImg/1002.webp";
import head1003Icon from "@/assets/headerImg/1003.webp";
import head1004Icon from "@/assets/headerImg/1004.webp";
import head1005Icon from "@/assets/headerImg/1005.webp";
import head1006Icon from "@/assets/headerImg/1006.webp";
import head1007Icon from "@/assets/headerImg/1007.webp";
import head1008Icon from "@/assets/headerImg/1008.webp";
import head1009Icon from "@/assets/headerImg/1009.webp";
import head1010Icon from "@/assets/headerImg/1010.webp";
import head1011Icon from "@/assets/headerImg/1011.webp";
import head1012Icon from "@/assets/headerImg/1012.webp";
import head1013Icon from "@/assets/headerImg/1013.webp";
import head1014Icon from "@/assets/headerImg/1014.webp";
import head1015Icon from "@/assets/headerImg/1015.webp";
import { useMemo } from "react";

const useHeadImg = () => {
  
    const HeadImgMapping = useMemo(() => ({
      1000: head1000Icon,
      1001: head1001Icon,
      1002: head1002Icon,
      1003: head1003Icon,
      1004: head1004Icon,
      1005: head1005Icon,
      1006: head1006Icon,
      1007: head1007Icon,
      1008: head1008Icon,
      1009: head1009Icon,
      1010: head1010Icon,
      1011: head1011Icon,
      1012: head1012Icon,
      1013: head1013Icon,
      1014: head1014Icon,
      1015: head1015Icon,
    }), []);
  
    return { HeadImgMapping };
  };
  export default useHeadImg;