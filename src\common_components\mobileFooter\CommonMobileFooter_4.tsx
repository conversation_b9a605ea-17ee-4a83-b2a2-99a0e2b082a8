import mobileFooterDIcon from "@/assets/mobile/deposit_4.svg";
import mobileFooterDHoverIcon from "@/assets/mobile/deposit_4hover.svg";
import homeIcon from "@/assets/mobile/home_4.svg";
import homeHoverIcon from "@/assets/mobile/home_4hover.svg";
import myIcon from "@/assets/mobile/my_4.svg";
import myHoverIcon from "@/assets/mobile/my_4hover.svg";
import promotionIcon from "@/assets/mobile/promotion.svg";
import promotionHoverIcon from "@/assets/mobile/promotion_4.svg";
import { useGameData } from "@/components/games/providers/GameDataProvider";
import { useUtils } from "@/components/useUtils";
import { HallMessage } from "@/protos/Hall";
import { setShowMobileHeader } from "@/store";
import { Image, Skeleton } from "antd";
import { SetStateAction, useEffect, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { useJump } from "../context/useJumpContext";
import EventBus from "@/utils/EventBus";
import { ModalType } from "@/utils/enums";

const CommonMobileFooter_4: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(1);
  const { channelList } = useGameData() as {
    channelList: HallMessage.IGameChannelInfo[];
  };
  const { setCommonJumpto, setDepostiSource } = useJump() || {};
  const [channelInfo, setChannelInfo] = useState<HallMessage.IGameChannelInfo | undefined>(
      undefined
  );
  const [count, setCount] = useState(4);
  const {isLogin} = useUtils();
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    // 获取第一个大类
    console.log("移动端footer，channelList更新", channelList);
    if (channelList && channelList.length > 0) {
      setChannelInfo(channelList[0]);
      setCount(5);
      setLoading(false);
    }
  }, [channelList]);
  const disPatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (location.pathname === "/") {
      handleJunmp(1);
    } else if (location.pathname.includes("/promotion")) {
      handleJunmp(4);
    } else if (location.pathname.includes("/mobile_personal_center")) {
      handleJunmp(5);
    }
  }, [location]);

  const redirectMapping = {
    1: "/",
    2: "/casino",
    3: "/mobile_deposit",
    4: "/promotion",
    5: "/mobile_personal_center"
  };

  const handleJunmp = (index: number) => {
    setActiveIndex(index);
    if (index === 5) {
      disPatch(setShowMobileHeader(false));
    } else {
      disPatch(setShowMobileHeader(true));
    }
  };

  function handlerClick(_index: SetStateAction<number>) {
    if ((_index === 5 || _index === 3) && !isLogin) {
      EventBus.emit("showModal", ModalType.showLoginModal)
      setCommonJumpto(redirectMapping[_index]);
      setDepostiSource(202)
      return;
    }
    handleJunmp(_index);
    navigate(redirectMapping[_index]);
  }

  return (
      <>
      {loading ? (
        <>
          <div
            className={`text-sm text-sub-text rounded-md flex flex-col items-center justify-center h-[55px] 
                    ${activeIndex === 1 ? "bg-[--mobile-footer-bg-color]" : ""}`}
          >
            <Skeleton.Node
              active
              style={{ height: "54px", width: "100%", backgroundColor: "#1b1e23" }}
            />
          </div>
        </>
      ) : (
        <div className="w-full flex flex-col">
          <div
            className="w-full grid justify-center items-center h-full text-sm"
            style={{
              gridTemplateColumns: `repeat(${count}, 1fr)`
            }}
          >
            <div
              className={`text-sm text-sub-text rounded-md flex flex-col items-center justify-center h-[55px] 
                    ${activeIndex === 1 ? "bg-[--mobile-footer-bg-color]" : ""}`}
              onClick={() => handlerClick(1)}
            >
              <div className="flex flex-col items-center justify-center gap-y-1">
                {activeIndex === 1 ? (
                  <Image width={20} height={20} preview={false} src={homeHoverIcon} alt="home" />
                ) : (
                  <Image preview={false} width={20} height={20} src={homeIcon} alt="home4" />
                )}
                <div
                  className={`text-sub-text  ${
                    activeIndex === 1 ? "text-[--mobile-nav-text-color]" : ""
                  }`}
                >
                  <FormattedMessage id="home" />
                </div>
              </div>
            </div>
            <div
              className={`  rounded-md flex flex-col items-center justify-center h-[55px] gap-y-1 ${
                activeIndex === 2 ? "bg-[--mobile-footer-bg-color]" : ""
              }`}
              onClick={() => handlerClick(2)}
            >
              {activeIndex === 2 ? (
                <Image
                  width={20}
                  height={20}
                  preview={false}
                  src={channelInfo?.channelIcon1}
                  alt="slots"
                />
              ) : (
                <Image
                  width={20}
                  height={20}
                  preview={false}
                  src={channelInfo?.channelIcon}
                  alt="game1"
                />
              )}
              <div
                className={`text-sub-text  ${
                  activeIndex === 2 ? "text-[--mobile-nav-text-color]" : ""
                }`}
              >
                {channelInfo?.channelName}
              </div>
            </div>
            <div
              className={`  rounded-md flex flex-col items-center justify-center h-[55px] gap-y-1 ${
                activeIndex === 3 ? "bg-[--mobile-footer-bg-color]" : ""
              }`}
              onClick={() => handlerClick(3)}
            >
              {activeIndex === 3 ? (
                <Image
                  width={20}
                  height={20}
                  preview={false}
                  src={mobileFooterDHoverIcon}
                  alt="live"
                />
              ) : (
                <Image width={20} height={20} preview={false} src={mobileFooterDIcon} alt="game1" />
              )}
              <div
                className={`text-sub-text  ${
                  activeIndex === 3 ? "text-[--mobile-nav-text-color]" : ""
                }`}
              >
                <FormattedMessage id="deposit" />
              </div>
            </div>
            <div
              className={` rounded-md flex flex-col items-center justify-center h-[55px] gap-y-1 ${
                activeIndex === 4 ? "bg-[--mobile-footer-bg-color]" : ""
              }`}
              onClick={() => handlerClick(4)}
            >
              {activeIndex === 4 ? (
                <Image
                  width={20}
                  height={20}
                  preview={false}
                  src={promotionHoverIcon}
                  alt="promotions"
                />
              ) : (
                <Image width={20} height={20} preview={false} src={promotionIcon} alt="game1" />
              )}
              <div
                className={`text-sub-text  ${
                  activeIndex === 4 ? "text-[--mobile-nav-text-color]" : ""
                }`}
              >
                <FormattedMessage id="promotions" />
              </div>
            </div>

            <div
              className={` rounded-md flex flex-col items-center justify-center h-[55px] gap-y-1 ${
                activeIndex === 5 ? "bg-[--mobile-footer-bg-color]" : ""
              }`}
              onClick={() => handlerClick(5)}
            >
              {activeIndex === 5 ? (
                <Image width={20} height={20} preview={false} src={myHoverIcon} alt="my" />
              ) : (
                <Image width={20} height={20} preview={false} src={myIcon} alt="game1" />
              )}
              <div
                className={`text-sub-text  ${
                  activeIndex === 5 ? "text-[--mobile-nav-text-color]" : ""
                }`}
              >
                <FormattedMessage id="my" />
              </div>
            </div>
          </div>
          <div className="h-[5px]"></div>
        </div>
      )}
      </>
  );
};

export default CommonMobileFooter_4;
