import inputClearIcon from "@/assets/header/inputClear.svg";
import searchIcon from '@/assets/search.svg';
import {ConfigProvider} from "antd";
import {Input} from "antd";

const CommonInput: React.FC<
    {
        callBack: (e: React.ChangeEvent<HTMLInputElement>) => void;
        placeholderText?: string;
        bgColor?: string;
        searchFu?: () => void;
        keyEvent?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
        className?: string;
    }
> = ({ callBack, placeholderText = "Search", bgColor = "var(--button-bg-color-light-gray)", searchFu , 
    keyEvent, className 
}) => {
    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Input: {
                            hoverBorderColor: "var(--header-deposit-input-border-color)",
                            colorBorder: "var(--header-deposit-input-border-color)", // 输入框边框颜色
                            activeBorderColor: "var(--header-deposit-input-border-color)", // 输入框激活边框颜色
                            colorBgContainer: `${bgColor}`, // 输入框背景色
                            activeBg: `${bgColor}`,// 输入框激活背景颜色 
                        }
                    }
                }}
            >
                <Input allowClear={{
                    clearIcon:
                        <img src={inputClearIcon} alt=""/>
                    }} 
                    onChange={callBack} 
                    placeholder={placeholderText}
                    onKeyDown={keyEvent}
                    className={`rounded-xl h-[44px] ${className}`}
                    prefix={<img src={searchIcon} className="cursor-pointer " onClick={searchFu} alt=""/>}
                />
            </ConfigProvider>
        </>
    )
}

export default CommonInput;