import {
  setContentPercentage,
  setMobileLogo,
  setPcLogo,
  setQuestHost,
  setRegion,
  setUserToken,
  setWebSiteInfo,
  setWebsitId
} from "@/store";
import {
  getHallHost,
  getIsLogin,
  getLangById,
  getToken,
  LANG,
  setHallHost,
  setToken
} from "@/utils/commonUtil";
import { TemplateType } from "@/utils/enums";

interface WebSiteConfig {
  token: string;
  host: string;
  region: string;
  webSiteInfo?: {
    language: string;
    siteLogo1?: string;
    siteLogo?: string;
    siteLogo2?: string;
    siteModel?: string;
  };
  error?: number;
}

const initWebSite = async (webSite: WebSiteConfig, dispatch: any) => {
  console.log("initWebSite被调用");
  if (webSite.error > 0) {
    window.alert("站点信息获取失败，请联系管理员");
    return null;
  }

  dispatch(setWebSiteInfo(JSON.parse(JSON.stringify(webSite))));

  if (getToken() && getHallHost() && getIsLogin()) {
    dispatch(setQuestHost(getHallHost()));
    dispatch(setUserToken(getToken()));
  } else {
    setToken(webSite.token);
    setHallHost(webSite.host);
    dispatch(setQuestHost(webSite.host));
    dispatch(setUserToken(webSite.token));
    dispatch(setRegion(webSite.region));
  }
  const cacheLang = localStorage.getItem("locale")
  const lang = getLangById(webSite.webSiteInfo?.language);
  let currentLang = lang.code
  if (cacheLang && cacheLang != lang.code) {
    currentLang = cacheLang
  }
  console.log("Setting default language to:", currentLang);
  localStorage.setItem(LANG, currentLang);

  const link = document.querySelector("link[rel='icon']") as HTMLLinkElement;
  if (link) {
    link.href = webSite.webSiteInfo?.siteLogo1 || "/logo.svg";
  }
  const lappLeink = document.querySelector("link[rel='apple-touch-icon']") as HTMLLinkElement;
  if (lappLeink) {
    lappLeink.href = webSite.webSiteInfo?.siteLogo1 || "/logo.svg";
  }
  dispatch(setPcLogo(webSite.webSiteInfo?.siteLogo));
  dispatch(setMobileLogo(webSite.webSiteInfo?.siteLogo2));

  const templateId = Number(webSite.webSiteInfo?.siteModel) || 1;
  console.log("模板信息", webSite.webSiteInfo);
  
  
  // const templateId = 6;
  console.log("Setting template to:", templateId);
  console.time('loadRouter');
  loadStyles(templateId, dispatch)
  

  const [routerModule] = await Promise.all([
    loadComponent(templateId),
    loadMainfest(templateId)
  ]);
  console.timeEnd('loadRouter');

  dispatch(setWebsitId(templateId));
  return routerModule.default;
};

const loadComponent = async (templateId: number) => {
  switch (templateId) {
    case 2:
      return import("@/router/TemplateTwo");
    case 3:
      return import("@/router/TemplateThree");
    case 4:
      return import("@/router/TemplateFour");
    case 5:
      return import("@/router/TemplateFour");
    case 6:
      return import("@/router/TemplateFixedBr");
    case 7:
      return import("@/router/TemplateFixed");
    case TemplateType.brazilId:
      return import("@/router/TemplateBrazil");
    default:
      return import("@/router/index");
  }
};

const loadMainfest = async (templateId: number) => {
  const linkElement = document.createElement("link");
  linkElement.id = "custom_mainfest";
  linkElement.rel = "manifest";
  if (location.href.includes("be4win")) {
      linkElement.href = "/be4win/manifest.json"
  } else if (location.href.includes("wingamemx")) {
    linkElement.href = "/wingameMX/manifest.json"
  } else if (location.href.includes("luck7.win")) {
    linkElement.href = "/luck7win/manifest.json"
  } else if (templateId === 4 || templateId === 7) {
    linkElement.href = "/manifest1.json";
  } else {
    linkElement.href = "/manifest.json";
  }
  document.head.appendChild(linkElement);
};

const loadStyles = async (templateId: number, dispatch: any) => {
  console.log("加载样式", templateId, typeof templateId);
  switch (templateId) {
    case 2:
      await import("@/TemplateTwo.css");
      dispatch(setContentPercentage("73rem"));
      break;
    case 4:
      await import("@/App_4.css");
      break;
    case 5:
      await import("@/App_4.css");
      break;
    case 6:
      await import("@/App_fiexd_template_6.css");
      break;
    case 7:
      await import("@/App_fiexd_template_7.css");
      break;
    case TemplateType.brazilId:
      await import("@/App_fiexd_template_brazil.css");
      break;
    default:
      await import("@/App.css");
      console.log("当前屏幕宽度", screen.width);
  }
};

export default initWebSite;
