import { memo, useMemo } from "react";
import AllianceBadge from '@/assets/fixedTemplateBrazil/home/<USER>'
import badge_alliance from '@/assets/fixedTemplateBrazil/home/<USER>'
import { Button as ButtonSelf } from "@/templateBrazil/components/button/button";
import { Button } from "antd";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import './adminStyle.css'
const AllianceDynamics: React.FC = memo(({}) => {

  // 当前用户未登录或者当前用户未加入联盟状态
  const UseUnAllianceMember = useMemo(() => {
    return(
      <div className="w-full flex flex-col justify-between gap-y-[10px]">
        <div className="w-full flex gap-x-[6px] items-center">
          <img src={badge_alliance} alt="" className="w-[37px]"/>
          <p className="flex flex-col">
            <span className="text-[12px] text-[#FFF] font-[600] break-all">Hasn't there been an alliance yet?</span>
            <span className="text-[10px] text-[#FFF] font-[400] break-all">Create your own alliance, develop more players and obtain substantial profits.</span>
          </p>
        </div>
        <div className="w-full flex justify-between items-center h-[26px] gap-x-[5px]">
          <Button className="w-[50%] h-full bg-transparent border border-[#364B6F] rounded-[55px]">
            Join alliance
          </Button>
          <ButtonSelf className="!w-[50%] !h-full !rounded-[55px]">
            Create alliance
          </ButtonSelf>
        </div>
      </div>
    )
  },[])

  // 当前用户登录和加入联盟状态
  const UseAllianceMember = useMemo(() => {
    return(
      <div className="w-full flex flex-col justify-between gap-y-[10px]">
        <div className="w-full flex justify-between items-center">
          <p className="flex gap-x-[5px] items-center">
            <span className="text-[#FFF] text-[12px] font-[600]">MY  Alliance </span>
            <span className="text-[#FDDE39] text-[10px] font-[600]">GROUP NAME</span>
          </p>
          <p className="text-[10px] flex">
            <span className="text-[#1ACCB4]">11</span>
            <span className="text-[#FFFFFF]">/55</span>
          </p>
        </div>
        <Swiper navigation={true} modules={[Navigation]} className="w-full AllianceSwiper">
          <SwiperSlide>Slide 1</SwiperSlide>
          <SwiperSlide>Slide 2</SwiperSlide>
          <SwiperSlide>Slide 3</SwiperSlide>
          <SwiperSlide>Slide 4</SwiperSlide>
          <SwiperSlide>Slide 5</SwiperSlide>
          <SwiperSlide>Slide 6</SwiperSlide>
          <SwiperSlide>Slide 7</SwiperSlide>
          <SwiperSlide>Slide 8</SwiperSlide>
          <SwiperSlide>Slide 9</SwiperSlide>
        </Swiper>
      </div>
    )
  },[])

  return (
    <div className="flex flex-col gap-y-[11px]">
      <div className="w-full flex justify-between items-center">
        <p className="flex gap-x-[6px] items-center">
          <img src={AllianceBadge} alt="" width={32} height={32}/>
          <span className="text-[#FFF] text-[14px] font-[900]">Friends</span>
        </p>
        <div className="flex gap-x-[6px]">
          <ul>
            {/* 这里到时候写入在线好友图片 */}
          </ul>
          <span className="text-[#fff] text-[20px] font-[700]">...</span>
        </div>
      </div>


      <div className="flex w-full h-full gap-x-[9px] mt-[9px] ">
        <div className="w-[40%] flex flex-col justify-between p-[5px] pb-[11px] bg-[#191C35] rounded-[8px]">
          <p className="flex gap-x-[6px]">
            <img src={badge_alliance} alt="" width={16}/>
            <span className="text-[#FFF] text-[12px] font-[600]">kkd Group</span>
          </p>
          <div className="flex w-full">
            {/* <img src="" alt="" /> */}
            <div className="w-[40%]">
              {/* 这里写入当前热门游戏 */}
            </div>
            <ul className="w-[60%] flex flex-col justify-between">
              <li className="w-full flex justify-between items-center">
                <img src="" alt="" />
                <span className='text-[#1ACCB4] text-[12px] font-[600]'>Toms1111 </span>
              </li>
              <li className="w-full flex justify-between items-center">
                <span>Won</span>
                <span>$11.00</span>
              </li>
              <li className="w-full flex justify-between items-center">
                <p className="text-[10px] text-[#FFF]">in Fortune tiger</p>
              </li>
              <li className="w-full flex justify-between items-center">
                <ButtonSelf >
                  PLAY
                </ButtonSelf>
              </li>
            </ul>
          </div>
        </div>
        <div className="w-[60%] flex flex-col p-[5px] pb-[11px] bg-[#24193A] rounded-[8px]">
          {/*  */}
          {/* {UseUnAllianceMember} */}

          <div className="flex-1 flex flex-col h-full">
            {/* {UseUnAllianceMember} */}
            {UseAllianceMember}
          </div>
        </div>
      </div>

      
    </div>
  )
})

export default AllianceDynamics;