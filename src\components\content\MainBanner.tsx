// import facebook from '@/assets/content/facebook.svg';
// import google from '@/assets/content/google.svg';
// import telegram from '@/assets/content/telegram.svg';

import ThreeLogin from "@/common_components/adminModules/ThreeLogin";
import { useJump } from "@/common_components/context/useJumpContext";
import { BackStageMessage } from "@/protos/BackStage";
import { setActivitySource } from "@/store/popup";
import { useScreenWidth } from "@/utils/MobileDetectionContext";
import { Button, Divider } from "antd";
import { memo, useEffect, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
import { useDispatch, useSelector } from "react-redux";
// import { useActivated, useDeactivated } from "react-route-cache";
import { useNavigate, useSearchParams } from "react-router-dom";
import "swiper/css"; // 引入 Swiper 的 CSS 文件
import "swiper/css/autoplay"; // 引入 autoplay 的 CSS
import "swiper/css/pagination";
import { Autoplay, Pagination } from "swiper/modules"; // 引入 autoplay 模块
import { Swiper, SwiperSlide } from "swiper/react";
import { useAllGame } from "../games/AllGameProvider";
import { useConfigData } from "../games/providers/ProjectConfigProvider";
import ScreenSpin from "../ScreenSpin2";
import { useUtils } from "../useUtils";
import { FuntionOpenMapping } from '@/utils/enums';
import EventBus from "@/utils/EventBus";
import { ModalType } from "@/utils/enums";
// const ThreeLogin = lazy(() => import("@/common_components/adminModules/ThreeLogin"));

const MainBanner: React.FC<{
  type: number;
  showText: boolean;
}> = memo(({ type = 1, showText = true }) => {
  const swiperRef = useRef(null);
  const { allConfigData } = useAllGame() as {
    allConfigData: BackStageMessage.ResConfigDataMessage;
    popuTypeInfo: PopupType;
    setPopuTypeInfo: (type: PopupType) => void;
  };
  const { functionSwitchIds } = useConfigData() as { functionSwitchIds: number[] };
  const { handleJump } = useJump() || {};
  const [loading, setLoading] = useState(true);
  const [bannerInfo, setBannerInfo] = useState<BackStageMessage.IBannerInfo[]>();
  const { getCurrentLangData } = useUtils();
  const navigate = useNavigate();
  const [picHight, setPicHight] = useState("306px");
  const { screenWidth } = useScreenWidth()  ;
  const divRef = useRef(null);
  const dispatch = useDispatch()

  const [searchParams] = useSearchParams();

  // useActivated(() => {
  //     if (swiperRef.current) {
  //       swiperRef.current?.autoplay?.start(); // 启动自动轮播
  //       swiperRef.current?.update(); // 更新 Swiper
  //     }
  //   });
  
  //   useDeactivated(() => {
  //     swiperRef.current?.autoplay?.stop(); // 停止自动轮播
  //   });

  useEffect(() => {
    if (screenWidth > 1024) {
      setPicHight("306px");
    } else {
      setPicHight("139px");
    }
  }, [screenWidth]);

  useEffect(() => {
    if (allConfigData && allConfigData.bannerList) {
      const currentTime = Date.now(); // 获取当前时间
      const currentLangu = getCurrentLangData().paramCode;
      const bannerInfo = allConfigData?.bannerList.filter(
        (item) =>
          item.index == type &&
          item.language == currentLangu &&
          item.startTime <= currentTime &&
          item.endTime >= currentTime
      );
      console.log("获取到的bannerInfo", bannerInfo, allConfigData?.bannerList);
      setBannerInfo(bannerInfo);
      setLoading(false);
    }
  }, [allConfigData]);

  const handleCustomer = (item: BackStageMessage.IBannerInfo, e: React.MouseEvent) => {
    console.log("banner点击弹框", item);
    e.stopPropagation();
    const jumpType = item?.jumpType;
    const url = item?.externalLinks;
    const popupLinks = item?.popupLinks;
    const innerLinks = item?.innerLinks;
    const isJump = item.isJump;
    if (isJump && isJump > 0) {
      const popupInfo = BackStageMessage.PopupInfo.create({
        jumpType: jumpType,
        popupLinks: popupLinks,
        externalLinks: url,
        popupType: 1,
        innerLinks: innerLinks,
        notLoginJump: item.notLoginJump,
      });
      if (location.pathname == "/") {
        dispatch(setActivitySource(1))
      } else {
        dispatch(setActivitySource(8))
      }
      handleJump(popupInfo, navigate);
    }
  };

  function handleClick(type: number, e: React.MouseEvent) {
    console.log("点击注册");
    e.stopPropagation();
    EventBus.emit("showModal", ModalType.showLoginModal)
  }
  const isLogin = useSelector((state: any) => state.systemReducer.isLogin);

  const divClass = isLogin
    ? "flex flex-col w-[100%] justify-center pl-10 lg:gap-y-3 gap-y-1 slelct-none"
    : "flex flex-col  justify-center items-start select-none ml-9 gap-y-5 w-[50%]";

  const isApkEnv = localStorage.getItem("IS_APK_ENV");
  console.log("MainBanner isApkEnv", isApkEnv);

  const dataList = () => {
    if (bannerInfo && bannerInfo.length > 0) {
      return bannerInfo.map((item, index) => (
        <SwiperSlide key={index} style={{ width: "auto" }}>
          <div key={index} style={{ height: picHight, width: "100%" }}>
            <div
              key={index}
              onClick={(e) => handleCustomer(item, e)}
              className={`${
                item.isJump && item.isJump > 0 ? "cursor-pointer" : ""
              } w-full flex rounded-[20px]  relative`}
              style={{
                backgroundImage: `url(${item?.fileUrl})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                height: picHight
              }}
            >
              <div className=" w-[100%] flex justify-start items-center ">
                <div className={divClass}>
                  <div className="select-none items-center justify-start lg:font-semibold font-extrabold text-xl uppercase flex space-x-1 overflow-hidden">
                    <div className="lg:text-3xl text-base">{item?.title}</div>
                  </div>
                  <div className="text-transparent bg-clip-text select-none bg-gradient-to-r font-medium from-[#BEF3FE] to-[#37A6E4] text-pxs/[16px] lg:text-3xl">
                    {item?.subtitle}
                  </div>
                  <div className=" flex gap-x-4 w-full max-[1024px]:mt-2">
                    {!isLogin && showText && (
                      <Button
                        className="text-black h-[44px] min-w-[27.013%] max-w-[50%] text-sm/4 rounded-xl  font-semibold bg-button-bg-green "
                        onClick={(e) => handleClick(2, e)}
                      >
                        <FormattedMessage id="register_now" />
                      </Button>
                    )}
                    {item?.button == 1 && (
                      <Button
                        disabled={!item.isJump}
                        className="text-black lg:h-[44px] h-[30px] min-w-[27.013%] max-w-[50%] text-sm/4 rounded-xl  font-semibold bg-button-bg-green"
                        onClick={(e) => handleCustomer(item, e)}
                      >
                        {item.buttonWord}
                      </Button>
                    )}
                  </div>
                  {!isLogin && showText && (
                    <>
                      <div className=" select-none text-primary-text w-[37.013%] flex justify-between items-center ">
                        <div className="w-[45%]">
                          <Divider className="!p-0 !m-0" />
                        </div>
                        <span className=" text-xs">
                          <FormattedMessage id="sigle_or" />
                        </span>
                        <div className="w-[45%]">
                          <Divider className="!p-0 !m-0" />
                        </div>
                      </div>
                      {functionSwitchIds?.includes(FuntionOpenMapping.Three_Party_Login) && (
                        <div className="flex flex-row gap-x-4 justify-between w-[220px]">
                          {/* <Suspense fallback={null}> */}
                            {!isApkEnv && <ThreeLogin />}
                          {/* </Suspense> */}
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </SwiperSlide>
      ));
    } else {
      return (
        <div className=" text-sub-text text-3xl flex justify-center items-center h-full min-h-[100px]">
          <FormattedMessage id="no_data" />
        </div>
      );
    }
  };

  return (
    <>
      {bannerInfo && bannerInfo.length > 1 && (
        <div className={`w-full relative `} style={{ height: picHight }}>
          <ScreenSpin loading={loading} />
          {bannerInfo && bannerInfo.length > 0 && (
            <div className={`w-full `} ref={divRef} style={{ height: picHight }}>
              <Swiper
                spaceBetween={10} // 幻灯片之间的间隔
                slidesPerView={1} // 自动计算显示个数
                direction="horizontal" // 水平方向
                loop={true}
                autoplay={{
                  delay: 2000
                }}
                modules={[Autoplay, Pagination]}
                onSwiper={(swiper) => (swiperRef.current = swiper)}
                className="w-auto"
                speed={600}
                pagination={{
                  clickable: true,
                  renderBullet: (index, className) => {
                    return `<div class='mobile-custom-dot  ${className}'>
                      </div>`;
                  }
                }}
              >
                {dataList()}
              </Swiper>
            </div>
          )}
        </div>
      )}

      {bannerInfo && bannerInfo.length == 1 && <div className="w-full">{dataList()}</div>}
    </>
  );
});
export default MainBanner;
