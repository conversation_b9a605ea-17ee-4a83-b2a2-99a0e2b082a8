// import IconBanner from "@/assets/content/icon-banner-title.svg";
import IconBanner from "@/assets/content/crypto_future.png";
import { memo, useEffect, useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
// import { useActivated, useDeactivated } from "react-route-cache";
import "swiper/css"; // 引入 Swiper 的 CSS 文件
import "swiper/css/autoplay"; // 引入 autoplay 的 CSS
import { Autoplay } from "swiper/modules"; // 引入 autoplay 模块
import { Swiper, SwiperSlide } from "swiper/react";
import { useAllGame } from "../games/AllGameProvider";
import { useUtils } from "../useUtils";
import SwipeTopControl from "./SwipeTopControl";
import { useActivated, useDeactivated } from "react-route-cache";

const CryptoSwipe: React.FC = memo(() => {
  const [cryptoList, setCryptoList] = useState<ExchangeRate[] | null>(null);
  const { allConfigData } = useAllGame() as {
    allConfigData: ResConfigDataMessage;
  };
  const { cronMap } = useUtils();
  const [isFirstPage, setIsFirstPage] = useState(false);
  const [isLastPage, setIsLastPage] = useState(false);
  const swiperRef = useRef();

  useActivated(() => {
    if (swiperRef.current) {
      swiperRef.current?.autoplay?.start(); // 启动自动轮播
      swiperRef.current?.update(); // 更新 Swiper
    }
  });

  useDeactivated(() => {
    swiperRef.current?.autoplay?.stop(); // 停止自动轮播
  });

  useEffect(() => {
    if (allConfigData && allConfigData.exchangeRateList) {
      const exchangeRateList = allConfigData.exchangeRateList.filter(
        (item) => Math.floor(item.currencyId / 1000) === 2
      );
      setCryptoList(exchangeRateList);
    }
  }, [allConfigData]);

  const next = () => {
    swiperRef.current?.slideNext();
    setIsLastPage(swiperRef.current?.isEnd);
    setIsFirstPage(swiperRef.current?.isBeginning);
  };
  const prev = () => {
    swiperRef.current?.slidePrev();
    setIsLastPage(swiperRef.current?.isEnd);
    setIsFirstPage(swiperRef.current?.isBeginning);
  };

  const styles = [" to-[#333641]", " to-[#342C2B]", " to-[#572F93]", " to-[#303341]"];

  return (
    <div className="flex flex-col w-full relative select-none">
      <SwipeTopControl
        isFirstPage={isFirstPage}
        isLastPage={isLastPage}
        IconBanner={IconBanner}
        prev={prev}
        next={next}
        text="Crypto Futures"
        showAllGames={false}
      />

      <div className="mt-6 min-h-[158px] w-full">
        {cryptoList?.length > 0 ? (
          <Swiper
            spaceBetween={10} // 幻灯片之间的间隔
            slidesPerView={"auto"} // 自动计算显示个数
            direction="horizontal" // 水平方向
            loop={true}
            autoplay={{
              delay: 2000
            }}
            modules={[Autoplay]}
            onSwiper={(swiper) => (swiperRef.current = swiper)}
            className="w-full"
            speed={600}
          >
            {cryptoList.map((item, index) => (
              <SwiperSlide key={index} style={{width: '230px'}}>
                <div
                  className={` h-[158px] w-[230px] rounded-md bg-gradient-to-b from-[#0D0E0F] ${
                    styles[index % 4]
                  }`}
                  key={index}
                >
                  <div className="py-3 px-5 flex flex-col justify-between h-full">
                    <div className="flex flex-col ">
                      <div className="flex items-center">
                        <img className="w-[32px] h-[32px]" src={cronMap[item.currencyId]?.icon} alt=""/>
                        <div className="flex flex-col ml-1">
                          <span className=" text-xs">{cronMap[item.currencyId]?.name}</span>
                          <span className=" text-xs text-[#3AD69E]">0.00%</span>
                        </div>
                      </div>
                      <div className="flex mt-3 font-bold text-sm">{item.usdExchangeRate}</div>
                    </div>
                    <div className="flex flex-col gap-y-1">
                      <div className="flex items-center">
                        <span className="text-[--crypto-swipe-text-color]">
                          <FormattedMessage id="vol" />:
                        </span>
                        <span className="text-sub-text-yellow">$822M</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-[--crypto-swipe-text-color]">
                          <FormattedMessage id="leverage" />:
                        </span>
                        <span className="text-sub-text-yellow">1000x</span>
                      </div>
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        ) : (
          <div className="text-sub-text text-3xl flex justify-center items-center h-full min-h-[100px]">
            <FormattedMessage id="no_data" />
          </div>
        )}
      </div>
    </div>
  );
});

export default CryptoSwipe;
