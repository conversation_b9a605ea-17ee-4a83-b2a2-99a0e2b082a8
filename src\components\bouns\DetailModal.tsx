import emptyIcon from "@/assets/noData.svg";

import depositIcon from "@/assets/bonus/deposit.svg";
import freeGamesIcon from "@/assets/bonus/freeGames.svg";
import levelUpIcon from "@/assets/bonus/levelUp.svg";
import luckySpinIcon from "@/assets/bonus/luckySpin.svg";
import monthlyIcon from "@/assets/bonus/monthly.svg";
import questsIcon from "@/assets/bonus/quests.svg";
import rechargeIcon from "@/assets/bonus/recharge.svg";
import weeklyCashIcon from "@/assets/bonus/weeklyCash.svg";

import { ActivityMessage } from "@/protos/Activity";
import { BackStageMessage } from "@/protos/BackStage";
import { ProtoMessage } from "@/protos/common";
import { getBonusTransactionsData } from "@/service/activity";
import { useScreenWidth } from "@/utils/MobileDetectionContext";

import { useCurrency } from "@/common_components/useCurrency";
import { ConfigProvider, Table, TableProps } from "antd";
import React, { useEffect, useState } from "react";
import { FormattedMessage } from "react-intl";
import CommonModal from "../CommonModal";
import { useAllGame } from "../games/AllGameProvider";
import { useConfigData } from "../games/providers/ProjectConfigProvider";
import SelectStyle from "../SelectStyle";
import { useUtils } from "../useUtils";
import { useConversionUtils } from "@/hooks/utils/useConversionUtils";
import { FuntionOpenMapping } from '@/utils/enums';
import { useToastUtils } from "@/hooks/utils/useToastUtils";

const DetailModal: React.FC<{
  bonusDetails: ResBonusDetailsDataMessage;
  showModal: boolean;
  handleCancel: () => void;
}> = ({ bonusDetails, showModal, handleCancel }) => {
  if (!showModal) return null;
  const { screenWidth } = useScreenWidth()  ;
  const {
    formatDate2,
    intl,
    formatNumberQfw
  } = useUtils();

  const { checkResponse } = useToastUtils();
  const { converterPlus } = useConversionUtils();
  const [typeBonus, setTypeBonus] = useState<any>({});
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [bonusType, setBonusType] = useState(0);
  const [totalPage, setTotalPage] = useState(1);
  const [bonusNote, setBonusNote] = useState<BonusNote[] | undefined>([]);
  const [loading, setLoading] = useState(false);

  const {allConfigData } = useAllGame() as {
    allConfigData: BackStageMessage.ResConfigDataMessage;
  };

  const { functionSwitchIds } = useConfigData() as { functionSwitchIds: number[] };

  const {fiatSymbol, viewFiat} = useCurrency()

  function handleChange(value: number) {
    console.log("下拉框选择", value);
    setBonusType(value);
  }

  const pageChange = (page: number, pageSize: number) => {
    setPage(page);
    setPageSize(pageSize);
  };

  useEffect(() => {
    if (bonusDetails && bonusDetails.bonusDetails) {
      const typeBonus = bonusDetails.bonusDetails.reduce((acc, cur) => {
        const reBonus = cur.bonusDetails ? formatNumberQfw(handleExchange(cur.bonusDetails), 2) : 0;
        acc[cur.bonusType] = reBonus;
        return acc;
      }, {});
      setTypeBonus(typeBonus);
    }
  }, [bonusDetails, viewFiat]);

  const getList = async () => {
    try {
      setLoading(true);
      const res = (await getBonusTransactionsData(
        ActivityMessage.ReqBonusTransactionsDataMessage.create({
          msgID: ProtoMessage.MID.ReqBonusTransactionsData,
          page: page,
          pageSize: pageSize,
          bonusType: bonusType
        })
      )) as ActivityMessage.ResBonusTransactionsDataMessage;
      console.log("获取奖励列表", res);
      if (checkResponse(res)) {
        setTotalPage(res.total);
        setBonusNote(res.bonusNoteList);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (showModal) {
      getList();
    }
  }, [showModal, page, pageSize, bonusType]);

  const handleExchange = (data: DItemShow[]) => {
    const totalBonus = data?.reduce((acc, cur) => {
      // 当前货币id
      const currencyId = cur.itemId;
      // 当前货币金额
      const amount = cur.num;
      // 计算汇率到usd
      const exchangeAmount = converterPlus(
        amount,
        currencyId,
        viewFiat,
        allConfigData?.exchangeRateList,
        false
      );

      return acc + exchangeAmount;
    }, 0);
    return totalBonus;
  };

  //1.quest 2.luckSpin 3.depositBonus 4.FreeSpin 5.LevelUpBonus 6.Recharge 7.WeeklyCashBack 8.MonthlyCashBonus

  const [options, setOptions] = useState([{ value: 0, label: "All bonuses" }]);

  useEffect(() => {
    const options = [{ value: 0, label: "All bonuses" }];
    if (functionSwitchIds.includes(FuntionOpenMapping.Quest)) {
      options.push({ value: 1, label: intl.formatMessage({ id: "quests" }) });
    }
    options.push({ value: 2, label: intl.formatMessage({ id: "lucky_spin" }) });
    options.push({ value: 3, label: intl.formatMessage({ id: "deposit_bonus" }) });
    options.push({ value: 4, label: intl.formatMessage({ id: "free_games" }) });
    if (allConfigData?.upgradeRewardsOpen) {
      options.push({ value: 5, label: intl.formatMessage({ id: "level_up_bonus" }) });
    }
    if (allConfigData?.chargingBenefitsOpen) {
      options.push({ value: 6, label: intl.formatMessage({ id: "recharge" }) });
    }
    if (allConfigData?.weeklyCashBackOpen) {
      options.push({ value: 7, label: intl.formatMessage({ id: "weekly_cashback" }) });
    }
    if (allConfigData?.monthlyCashBackOpen) {
      options.push({ value: 8, label: intl.formatMessage({ id: "monthly_cashback" }) });
    }

    setOptions(options);
  }, [allConfigData]);

  //1.quests 2.luckySpin 3.depositBonus 4.freeSpin 5.levelUpBonus 6.recharge 7.weeklyCashBack 8.monthlyCashBack
  const dataType = {
    1: intl.formatMessage({ id: "quests" }),
    2: intl.formatMessage({ id: "lucky_spin" }),
    3: intl.formatMessage({ id: "deposit_bonus" }),
    4: intl.formatMessage({ id: "free_games" }),
    5: intl.formatMessage({ id: "level_up_bonus" }),
    6: intl.formatMessage({ id: "recharge" }),
    7: intl.formatMessage({ id: "weekly_cashback" }),
    8: intl.formatMessage({ id: "monthly_cashback" })
  };

  function handleCloseModal() {}

  // 定义列
  const columns: TableProps<DataType>["columns"] = [
    {
      title: (
        <span className="text-sm/[16px] font-medium">
          <FormattedMessage id="bonus_type" />
        </span>
      ),
      dataIndex: "bonus_type",
      key: "bonus_type",
      align: "left",
      render: (_, record: BonusNote, index) => (
        <div className={`text-primary-text lg:text-sm text-pxs/[15px]`}>
          {dataType[record.bonusType] || ""}
        </div>
      )
    },
    {
      title: (
        <span className="text-sm/[16px] font-medium">
          <FormattedMessage id="amount_claimed" />
        </span>
      ),
      dataIndex: "amount_claimed",
      key: "amount_claimed",
      align: "center",
      render: (_, record: BonusNote, index) => (
        <div className="text-primary-text lg:text-sm text-pxs/[15px] font-semibold">
          {fiatSymbol}
          {converterPlus(
            record.amount || 0,
            record.currencyId,
            viewFiat,
            allConfigData?.exchangeRateList
          )}
        </div>
      )
    },

    {
      title: (
        <span className="text-sm/[16px] font-medium">
          <FormattedMessage id="time" />
        </span>
      ),
      dataIndex: "time",
      key: "time",
      align: "right",
      render: (_, record: BonusNote, index) => (
        <div className="text-primary-text lg:text-sm text-pxs/[15px]">
          {formatDate2(record.time, 2)}
        </div>
      )
    }
  ];

  return (
    <>
      <CommonModal
        width={screenWidth > 1024 ? "45%" : "100%"}
        footer={null}
        title={
          <div className="bg-content-second-gray p-4  rounded-tl-2xl rounded-tr-2xl">
            <span className="text-primary-text text-lg/6 font-medium">
              <FormattedMessage id="bonus_details" />
            </span>
          </div>
        }
        open={showModal}
        onCancel={handleCancel}
        afterClose={handleCloseModal}
      >
        <div className="flex flex-col p-3 gap-y-3 b">
          <div className="text-primary-text text-base font-medium">
            <FormattedMessage id="bonus_categories" />
          </div>
          <div className="bg-content-third rounded-md p-3 grid lg:grid-cols-4 grid-cols-1 gap-y-5 ">
            {functionSwitchIds?.includes(FuntionOpenMapping.Quest) && (
              <div className="flex lg:flex-col max-[1024px]:justify-between gap-y-1 ">
                <div className="flex gap-x-1 items-center">
                  <img src={questsIcon} alt="" className="w-5 h-5" />
                  <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                    <FormattedMessage id="quests" />
                  </span>
                </div>
                <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                  {fiatSymbol} {typeBonus[1] || 0}
                </span>
              </div>
            )}

            <div className="flex lg:flex-col max-[1024px]:justify-between gap-y-1">
              <div className="flex gap-x-1 items-center">
                <img src={luckySpinIcon} alt="" className="w-5 h-5" />
                <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                  <FormattedMessage id="lucky_spin" />
                </span>
              </div>
              <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                {fiatSymbol} {typeBonus[2] || 0}
              </span>
            </div>
            <div className="flex lg:flex-col max-[1024px]:justify-between gap-y-1">
              <div className="flex gap-x-1 items-center">
                <img src={depositIcon} alt="" className="w-5 h-5" />
                <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                  <FormattedMessage id="deposit_bonus" />
                </span>
              </div>
              <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                {fiatSymbol} {typeBonus[3] || 0}
              </span>
            </div>
            <div className="flex lg:flex-col max-[1024px]:justify-between gap-y-1">
              <div className="flex gap-x-1 items-center">
                <img src={freeGamesIcon} alt="" className="w-5 h-5" />
                <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                  <FormattedMessage id="free_games" />
                </span>
              </div>
              <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                {fiatSymbol} {typeBonus[4] || 0}
              </span>
            </div>
            {allConfigData?.upgradeRewardsOpen && (
              <div className="flex lg:flex-col max-[1024px]:justify-between gap-y-1">
                <div className="flex gap-x-1 items-center">
                  <img src={levelUpIcon} alt="" className="w-5 h-5" />
                  <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                    <FormattedMessage id="level_up_bonus" />
                  </span>
                </div>
                <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                  {fiatSymbol} {typeBonus[5] || 0}
                </span>
              </div>
            )}
            {allConfigData?.chargingBenefitsOpen && (
              <div className="flex lg:flex-col max-[1024px]:justify-between gap-y-1">
                <div className="flex gap-x-1 items-center">
                  <img src={rechargeIcon} alt="" className="w-5 h-5" />
                  <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                    <FormattedMessage id="recharge" />
                  </span>
                </div>
                <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                  {fiatSymbol} {typeBonus[6] || 0}
                </span>
              </div>
            )}

            {allConfigData?.weeklyCashBackOpen && (
              <div className="flex lg:flex-col max-[1024px]:justify-between gap-y-1">
                <div className="flex gap-x-1 items-center">
                  <img src={weeklyCashIcon} alt="" className="w-5 h-5" />
                  <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                    <FormattedMessage id="weekly_cashback" />
                  </span>
                </div>
                <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                  {fiatSymbol} {typeBonus[7] || 0}
                </span>
              </div>
            )}

            {allConfigData?.monthlyCashBackOpen && (
              <div className="flex lg:flex-col max-[1024px]:justify-between gap-y-1">
                <div className="flex gap-x-1 items-center">
                  <img src={monthlyIcon} alt="" className="w-5 h-5" />
                  <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                    <FormattedMessage id="monthly_cashback" />
                  </span>
                </div>
                <span className="lg:text-sm text-pxs/[16px] font-medium text-primary-text">
                  {fiatSymbol} {typeBonus[8] || 0}
                </span>
              </div>
            )}
          </div>
          <div className="text-primary-text text-base font-medium mt-2 flex justify-between items-center">
            <span>
              <FormattedMessage id="bonus_transactions" />
            </span>
            <span>
              <SelectStyle
                onChange={handleChange}
                minWidth={"200px"}
                defaultValue={options[0].value}
                options={options}
              />
            </span>
          </div>
          <div className="text-sub-text-d-white lg:text-base text-pxs/[16px]">
            <FormattedMessage id="bonus_detail_desc" />
          </div>
          <div className="w-full p-3">
            <ConfigProvider
              theme={{
                components: {
                  Table: {
                    // lineWidth: 1, // 表格线条宽度
                    // headerSplitColor: 'var(--button-bg-color)', // 表头分割线颜色
                    // headerBg: 'var(--button-bg-color)', // 表头背景颜色
                    // colorBgContainer: 'var(--button-bg-color)', // 表格背景颜色
                  }
                }
              }}
            >
              <Table
                locale={{
                  emptyText: (
                    <div className="min-h-[260px] flex flex-col items-center justify-center">
                      <span>
                        <img src={emptyIcon} className="w-[200px]" alt=""/>
                      </span>
                      <span className="text-[--node_data_text_color] text-lg ">
                        <FormattedMessage id="no_data" />
                      </span>
                    </div>
                  )
                }}
                loading={loading}
                pagination={{
                  position: ["bottomRight"],
                  hideOnSinglePage: true,
                  total: totalPage,
                  showSizeChanger: false,
                  onChange: (page, pageSize) => pageChange(page, pageSize)
                }}
                size="small"
                columns={columns}
                dataSource={bonusNote}
              />
            </ConfigProvider>
          </div>
        </div>
      </CommonModal>
    </>
  );
};

export default DetailModal;
