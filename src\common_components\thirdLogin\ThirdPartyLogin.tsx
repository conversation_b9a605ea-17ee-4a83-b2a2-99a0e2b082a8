import GoogleSignIn from "@/components/GoogleSignIn";
import TeleLogin from "@/components/TeleLogin";
import { ThirdParty } from "@/utils/enums";
import { FormattedMessage } from "react-intl";

import { useConfigData } from "@/components/games/providers/ProjectConfigProvider";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { HallMessage } from "@/protos/Hall";
import { bindThreeParty } from "@/service/hall";
import { setIschangeUserInfo } from "@/store";
import { useDispatch, useSelector } from "react-redux";

const ThirdPartyLogin: React.FC<{
    icon: string;
    platform: string;
    account: string | undefined;
    isBound: boolean;
    onUnbind: () => void;
    disabled: boolean;
  }> = ({ icon, platform, account, isBound, onUnbind, disabled }) => {
    const googleInfo = useSelector((state: any) => state.systemReducer.googleInfo);

    const { intl, Toast } = useToastUtils();
    const dispatch = useDispatch();
    const { TelegramRobotId } = useConfigData();
    
    if (platform == "Google" && !googleInfo) {
      return <></>;
    } else if (!TelegramRobotId) {
      return <></>;
    }

    const handleThirdPartyBind = async (type: number, account: string, threePartyId: string) => {
        try {
          const res = await bindThreeParty(
            HallMessage.ResBindThreePartyMessage.create({
              msgID: 400055,
              threePartyInfo: {
                threeParty: type,
                account: account,
                threePartyId: threePartyId
              }
            })
          );
    
          if (res.error > 0) {
            Toast(intl.formatMessage({ id: "error_message_" + res.error }), "error", res.error);
            return;
          }
    
          Toast(intl.formatMessage({ id: "success" }), "success");
          dispatch(setIschangeUserInfo(true));
        } catch (error) {
          console.error("Error binding third party:", error);
        }
      };

    return (
    <div className="w-full h-[92px] rounded-lg px-4 flex items-start flex-col text-[--account-div-text-color] bg-[--account-div-bg-color]">
      <div className="flex space-x-2 justify-center py-4 text-pxs/[16px]">
        <span>
          <img src={icon} alt={platform} />
        </span>
        <span className="flex flex-col">
          <span className="flex gap-x-1 text-primary-text">{platform}</span>
          <span className="text-primary-text">{account || "None"}</span>
        </span>
      </div>
      {isBound ? (
        <button
          disabled={disabled}
          onClick={onUnbind}
          className="mobile_btn-unbind disabled:opacity-60 !w-full"
        >
          <FormattedMessage id="unbind" />
        </button>
      ) : (
        <>
          {platform == "Google" ? (
            <div className="mobile_btn !w-full ">
              <GoogleSignIn type={3} callBack={(data) => {
                  handleThirdPartyBind(ThirdParty.Google, data.account, data.threePartyId);
                }}></GoogleSignIn>
            </div>
          ) : (
            <TeleLogin
              width="w-full"
              callback={(data: TeleLoginData) => {
                handleThirdPartyBind(ThirdParty.Telegram, data.username, data.id.toString());
              }}
            >
              <button className="mobile_btn !w-full">
                <FormattedMessage id="bind" />
              </button>
            </TeleLogin>
          )}
        </>
      )}
    </div>
  )
};

export default ThirdPartyLogin;