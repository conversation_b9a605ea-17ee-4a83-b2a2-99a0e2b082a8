import { useConfigData } from "@/components/games/providers/ProjectConfigProvider";
import { useUtils } from "@/components/useUtils";
import { BackStageMessage } from "@/protos/BackStage";
import { ProtoMessage } from "@/protos/common";
import { setRefreshPopupType, setShowLiveSupport } from "@/store";
import { isPWA } from "@/utils/commonUtil";
import { useScreenWidth } from "@/utils/MobileDetectionContext";
import { createContext, useCallback, useContext, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useCreateOrder } from "../modal/directRecharge/useCreateOrder";
import { useHint } from "../useHint";
import { usePersonInfoUtils } from "../usePersonInfoUtils";
import { useDeferred } from "./useDeferredContext";
import { useQueryConfigData } from "@/hooks/queryHooks/useQueryConfigData";
import { useQueryPromotionData } from "@/hooks/queryHooks/useQueryPromotionData";
import { useQueryActivityData } from "@/hooks/queryHooks/useQueryActivityData";
import { useConversionUtils } from "@/hooks/utils/useConversionUtils";
import { PopuTriggerType } from "@/utils/enums";
import EventBus from "@/utils/EventBus";
import { ModalType } from "@/utils/enums";
import { useToastUtils } from "@/hooks/utils/useToastUtils";
import { BannerInfo } from "@/types/backStage";
import { PopupInfo, ResConfigDataMessage } from "@/types/backStage";
import { DCurrencyItem, InboxInfo, PlayerInfo } from "@/types/common";
import { getDayOfWeek } from "@/utils/timeConversion";

const JumpContext = createContext();

const JumpProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const dispatch = useDispatch();
  const [commonJumpto, setCommonJumpto] = useState();
  const [notOpen, setNotOpen] = useState(true);
  const [centerPopup, setCenterPopup] = useState<BackStageMessage.IPopupInfo[]>();
  const [rightTopPopup, setRightTopPopup] = useState<BackStageMessage.IPopupInfo[]>();
  const [rightDownPopup, setRightDownPopup] = useState<BackStageMessage.IPopupInfo[]>();
  const [leftDownPopup, setLeftDownPopup] = useState<BackStageMessage.IPopupInfo[]>();
  const refreshPopupType = useSelector((state: InboxInfo) => state.systemReducer.refreshPopupType);
  const { screenWidth } = useScreenWidth();
  const [widthdrwaGoogleAuthSuccess, setWidthdrwaGoogleAuthSuccess] = useState();
  const [widthdrwaGoogleAuthSuccess_virtual, setWidthdrwaGoogleAuthSuccess_virtual] = useState();
  const [withdrawCheckSuccessType, setWithdrawCheckSuccessType] = useState<number>(0); //提现前验证类型
  const [widthdrwaType, setWidthdrwaType] = useState();
  const [show2FA_showGoogleTip, setShow2FA_showGoogleTip] = useState();
  const [showHint, setShowHint] = useState<number | null>(null);
  const { personValue } = usePersonInfoUtils() as {
    personValue: ProtoMessage.PlayerInfo;
  };
  const { installPwa } = useDeferred();
  const { createOrder, onlyCrpto, rechargeLoading } = useCreateOrder();
  const showDirectModal = useRef(false);
  const showDirectModalPopup = useRef();

  console.log("personValue数据刷新-------useJump", personValue);

  const { showCurrencyId } = useConfigData();
  const [depositSource, setDepostiSource] = useState<number | null>(null);
  const { isNotShow } = useHint();
  // 记录是否之前是限时弹出进行的充值拉起
  const [isLimitRecharge, setIsLimitRecharge] = useState(false);
  console.log("useJump执行了============================");
  console.log("获取用户信息============================useJump", personValue);

  const {
    getCurrentLangData,
    isLogin
  } = useUtils();

  const { checkResponse} = useToastUtils()
  const { converterPlus } = useConversionUtils();

  const {data , refetch:refetchActivityData} = useQueryActivityData()
  const {refetch:refetchPopupList} = useQueryConfigData(9)
  const {data:allConfigData} = useQueryConfigData(0,1000 * 60 * 60 * 24)

  const refreshPopupListMultiple = useCallback(async (userInfo: PlayerInfo, triggerTypes: number[]) => {
    const data = await refetchPopupList();
    const allPopupData = data?.data?.PopupList;
    if (allPopupData && allPopupData) {
      const center = [];
      const rightDown = [];
      const rightTop = [];
      const leftDown = [];
      const cItem =
        userInfo &&
        (userInfo.cItem.find(
          (item: DCurrencyItem) => item.currencyId === showCurrencyId
        ) as DCurrencyItem);
      for (let i = 0; i < triggerTypes.length; i++) {
        const date = new Date();
        const triggerType = triggerTypes[i];
        let visitorPopups = allPopupData.filter(
          (item) =>
            item.language == getCurrentLangData().paramCode &&
            item.triggerType === triggerType &&
            item.startTime <= date.getTime() &&
            item.endTime >= date.getTime() &&
            ((screenWidth > 1024 && item.popupPlatform?.includes(1)) ||
              ((!screenWidth || screenWidth <= 1024) && item.popupPlatform?.includes(2)))
        );
        console.log("注册获取到弹窗", visitorPopups, triggerType);
        if (visitorPopups) {
          visitorPopups = await filterPopups(
            visitorPopups,
            cItem,
            allConfigData,
            triggerType,
            userInfo
          );
          if (visitorPopups?.length === 0) {
            continue;
          }

          const groupedPopups = visitorPopups.reduce((acc, item) => {
            let popupWay = item.popupWay || 1;
            const popupType = item.popupType;
            if (popupType === 1) {
              popupWay = 1;
            }
            if (!acc[popupWay]) {
              acc[popupWay] = [];
            }
            // 将当前 item 加入对应的 popupWay 分组
            acc[popupWay].push(item);
            return acc;
          }, {} as Record<string, BackStageMessage.IPopupInfo[]>);

          Object.keys(groupedPopups).forEach((key) => {
            groupedPopups[key].sort(
              (a: BackStageMessage.IPopupInfo, b: BackStageMessage.IPopupInfo) => {
                const sortDiff = (a.sort || 0) - (b.sort || 0);
                if (sortDiff === 0) {
                  return new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime();
                }
                return sortDiff;
              }
            );
          });
          if (groupedPopups[1]) {
            // setCenterPopup(pre => [...(pre || []), ...groupedPopups[1]] )
            center.push(...groupedPopups[1]);
          }
          if (groupedPopups[2]) {
            leftDown.push(...groupedPopups[2]);
          }
          if (groupedPopups[3]) {
            // setRightTopPopup(pre => [...(pre || []), ...groupedPopups[3]])
            rightTop.push(...groupedPopups[3]);
          }
          if (groupedPopups[4]) {
            rightDown.push(...groupedPopups[4]);
          }
        }
      }
      if (center.length > 0) {
        // setCenterPopup(pre => [...(pre || []), ...groupedPopups[1]] )
        setCenterPopup(center);
      } else {
        setCenterPopup([]);
      }
      if (rightDown.length > 0) {
        // setRightDownPopup(pre => [...(pre || []), ...groupedPopups[2]])
        setRightDownPopup(rightDown);
      } else {
        setRightDownPopup([]);
      }
      if (rightTop) {
        // setRightTopPopup(pre => [...(pre || []), ...groupedPopups[3]])
        setRightTopPopup(rightTop);
      } else {
        setRightTopPopup([]);
      }
      if (leftDown.length > 0) {
        setLeftDownPopup(leftDown);
      } else {
        setLeftDownPopup([]);
      }
      console.log("刷新弹窗", center, rightDown, rightTop);
    }
  }, []);

  const refreshPopupList = useCallback(
    async (
      triggerType: number,
      cItem: DCurrencyItem,
      configData: ResConfigDataMessage | null,
      userInfo: PlayerInfo
    ) => {
      const data = await refetchPopupList();
      const allPopupData = data?.data?.PopupList;
      if (allConfigData && allPopupData) {
        const date = new Date();
        let visitorPopups = allPopupData.filter(
          (item) =>
            item.language == getCurrentLangData().paramCode &&
            item.triggerType === triggerType &&
            item.startTime <= date.getTime() &&
            item.endTime >= date.getTime() &&
            ((screenWidth > 1024 && item.popupPlatform?.includes(1)) ||
              ((!screenWidth || screenWidth <= 1024) && item.popupPlatform?.includes(2)))
        ) as BackStageMessage.IPopupInfo[];
        visitorPopups = await filterPopups(
          visitorPopups,
          cItem,
          allConfigData,
          triggerType,
          userInfo
        );

        if (visitorPopups) {
          const groupedPopups = visitorPopups.reduce((acc, item) => {
            let popupWay = item.popupWay || 1;
            const popupType = item.popupType;

            if (popupType === 1) {
              popupWay = 1;
            }
            if (!acc[popupWay]) {
              acc[popupWay] = [];
            }
            // 将当前 item 加入对应的 popupWay 分组
            acc[popupWay].push(item);
            return acc;
          }, {} as Record<string, BackStageMessage.IPopupInfo[]>);

          Object.keys(groupedPopups).forEach((key) => {
            groupedPopups[key].sort(
              (a: BackStageMessage.IPopupInfo, b: BackStageMessage.IPopupInfo) => {
                const sortDiff = (a.sort || 0) - (b.sort || 0);
                if (sortDiff === 0) {
                  return new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime();
                }
                return sortDiff;
              }
            );
          });
          console.log("弹窗groupedPopups", groupedPopups, personValue);
          if (groupedPopups[1]) {
            // setCenterPopup(pre => [...(pre || []), ...groupedPopups[1]] )
            console.log("刷新弹窗-设置弹窗1", ...groupedPopups[1]);
            setCenterPopup([...groupedPopups[1]]);
          } else {
            setCenterPopup([]);
          }
          if (groupedPopups[2]) {
            setLeftDownPopup([...groupedPopups[2]]);
          } else {
            setLeftDownPopup([]);
          }
          if (groupedPopups[3]) {
            // setRightTopPopup(pre => [...(pre || []), ...groupedPopups[3]])
            setRightTopPopup([...groupedPopups[3]]);
          } else {
            setRightTopPopup([]);
          }
          if (groupedPopups[4]) {
            // setRightDownPopup(pre => [...(pre || []), ...groupedPopups[2]])
            setRightDownPopup([...groupedPopups[4]]);
          } else {
            setRightDownPopup([]);
          }
          console.log("刷新弹窗", triggerType, groupedPopups, groupedPopups[1]);
        }
        return visitorPopups;
      }
      return null;
    },
    [personValue, isLimitRecharge, showCurrencyId]
  );

  const filterPopups = useCallback(
    async (visitorPopups: PopupInfo[], cItem: DCurrencyItem, allConfigData: ResConfigDataMessage, triggerType : number, userInfo: PlayerInfo) => {

      visitorPopups = visitorPopups.filter((item) => {
       const weeklys = item.weeklys || [];
       const timeZone = item.timeZone;
       // 如果周几不配置，则都弹窗
       if (weeklys.length == 0) {
          return true;
       }
       const dayOfWeek = getDayOfWeek(Date.now(), timeZone);
       return weeklys.includes(dayOfWeek)
      });


      if (triggerType === PopuTriggerType.BALANCE_NOT_ENOUGH && cItem) {
        visitorPopups = handleBalance(visitorPopups, cItem, allConfigData);
      }

      if (triggerType === PopuTriggerType.ENTER_GAME) {
        visitorPopups = handleAmount(visitorPopups, allConfigData);
      }

      if (triggerType === PopuTriggerType.RETURN_LOBBY) {
        console.log("从游戏回到首页弹窗触发");
        const cItem =
          personValue &&
          (personValue.cItem.find(
            (item: DCurrencyItem) => item.currencyId === showCurrencyId
          ) as DCurrencyItem);
        // 判断余额是否都满足,先排除底部pwa安装提示
        const popups = handleBalance(
          visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup != 12),
          cItem,
          allConfigData
        );
        if (popups.length > 0) {
          visitorPopups = popups;
        } else {
          visitorPopups = visitorPopups.filter(
            (item) => item.popupDataList[0]?.systemPopup == 12 && !isPWA()
          );
        }
      }

      if (triggerType === PopuTriggerType.RECHARGE_SUCCESS) {
        console.log("充值成功弹窗调起", isLimitRecharge);

        // 如果是限时充值拉起的充值，并且配置了立即游戏弹框。那么就不需要pwa安装底部弹窗， 否者其他情况的充值，不弹立即游戏
        if (
          isLimitRecharge &&
          visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup == 11)?.length > 0
        ) {
          visitorPopups = visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup != 12);
          setIsLimitRecharge(false);
        } else {
          visitorPopups = visitorPopups.filter(
            (item) =>
              item.popupDataList[0]?.systemPopup != 11 ||
              (item.popupDataList[0]?.systemPopup == 12 && !isPWA())
          );
        }
      }

      // 每日弹窗配置次数,如果用户充值次数大于配置的每日弹窗个数，则不弹
      const dailyCount = allConfigData?.dailyRechargePopList?.[0]?.gearInfo?.length || 0;
      const userRecharge = userInfo?.dailyRechargeTimes ?? personValue?.dailyRechargeTimes ?? 0;
      if (userRecharge >= dailyCount) {
        visitorPopups = visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup != 9);
      }

      // 判断显示充值弹窗
      visitorPopups = await fliterLimitRecharge(personValue || userInfo, visitorPopups);

      // 判断首充活动弹窗
      visitorPopups = await fliterFirstDepositRecharge(visitorPopups, personValue || userInfo);

      visitorPopups = visitorPopups.filter((item) => {
        if (item.popupDataList[0]?.systemPopup == 12 && isPWA()) {
          return false;
        }
        return true;
      });

      return visitorPopups;
    },
    [showCurrencyId, personValue, isLimitRecharge]
  );

  // 判断首充弹窗
  const fliterFirstDepositRecharge = useCallback(async (visitorPopups: PopupInfo[], personValue: PlayerInfo) => {
    // 如果未登录，则不弹
    if (!personValue) {
      return visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup != 13);
    }
    // 如果有充值活动弹窗
    if (visitorPopups.find((item) => item.popupDataList[0]?.systemPopup == 13)) {
      const data = await refetchActivityData();
      const firstChargeSignInInfo = data?.data?.firstChargeSignInInfo;
      // 未配置首充弹窗，则不弹
      if (!firstChargeSignInInfo) {
        return visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup != 13);
      }
      // 当前应该领取天数
      const currDay = firstChargeSignInInfo?.currDay || 0;
      const receiveDay = firstChargeSignInInfo.receiveDay || [];
      // 如果已领取，不弹
      if (
        receiveDay.includes(currDay) ||
        receiveDay.length == firstChargeSignInInfo?.firstChargeRewards?.length ||
        currDay > firstChargeSignInInfo.firstChargeRewards.length
      ) {
        return visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup != 13);
      }
      // 如果没有充值时间，则代表未激活，不弹
      if (!firstChargeSignInInfo?.rechargeTime) {
        return visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup != 13);
      }
      // 间隔时间
      const resetDay = firstChargeSignInInfo?.resetDay || 0;
      //  最后一次领取时间
      const lastReceiveTime = receiveDay.length > 0 ? receiveDay[receiveDay.length - 1] : 0;

      // 如果距离上次领取时间大于领取间隔，则不弹窗
      if (currDay - lastReceiveTime > resetDay) {
        return visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup != 13);
      }
    }
    return visitorPopups;
  }, []);

  const fliterLimitRecharge = useCallback(async (personValue: PlayerInfo, visitorPopups: PopupInfo[]) => {
    // 如果有限时弹窗
    if (visitorPopups.find((item) => item.popupDataList[0]?.systemPopup == 10)) {
      if (personValue) {
        // 用户历史充值次数
        const rechargeTimes = (personValue.rechargeTimes ?? 0) + 1;
        const data = await refetch();
        let datas = data?.data;
        if (checkResponse(datas)) {
          const filteredDatas = datas.promotionsList.filter((item) => {
            return item.type == 4;
          });
          datas = filteredDatas;
        }

        if (datas?.length == 0) {
          return visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup != 10);
        }
        const now = Date.now();
        const filtedData = datas.filter(
          (item) =>
            rechargeTimes == item.subType &&
            now < item.expirationTime &&
            (now < item.registerExpired || !item.registerExpired)
        );
        if (filtedData?.length == 0) {
          return visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup != 10);
        }
      } else {
        // 如果未登录，则不弹
        return visitorPopups.filter((item) => item.popupDataList[0]?.systemPopup != 10);
      }
    }
    return visitorPopups;
  }, []);

  const { refetch } = useQueryPromotionData(1000 * 5, false)

  const handleBalance = (visitorPopups: PopupInfo[], cItem: DCurrencyItem, allConfigData: ResConfigDataMessage) => {
    return visitorPopups
      .filter(
        (item) =>
          item.rechargePopup === (personValue?.rechargeTimes || 0) + 1 ||
          (personValue?.rechargeTimes > 3 && item.rechargePopup > 3)
      )
      .filter((item) => {
        if (
          item.currencyId === cItem?.currencyId &&
          (item.insufficientBalance || 0) > (cItem.value || 0) + (cItem.bonusValue || 0)
        ) {
          return true;
        }
        if (item.currencyId === showCurrencyId) {
          if ((cItem.value || 0) + (cItem.bonusValue || 0) <= 0) {
            return true;
          }
          const exchangeValue = converterPlus(
            (cItem.value || 0) + (cItem.bonusValue || 0),
            cItem.currencyId || 0,
            showCurrencyId,
            allConfigData?.exchangeRateList,
            false
          );
          if (exchangeValue < (item.insufficientBalance || 0)) {
            return true;
          }
        }
        return false;
      });
  };

  const handleAmount = (visitorPopups: PopupInfo[], allConfigData: ResConfigDataMessage) => {
    return visitorPopups.filter((item) => {
      // 充值类型 0.不限制 1.< 2.≤ 3.> 4.≥ 5.=
      const rechargeTimeType = item.rechargeTimeType || 0;
      const configRechargeTimes = item.rechargeTimes || 0;
      const configCurrency = item.currencyId || 0;
      const configRechargeAmountType = item.rechargeAmountType || 0; //充值金额类型
      const configRechargeAmount = item.rechargeAmount || 0;
      const personCurrency = personValue?.viewFiat || 0;
      const personRechargeTimes = personValue?.rechargeTimes || 0;
      const personRechargeAmount = personValue?.rechargeAmount || 0;
      const ye = personValue?.cItem.reduce((acc: number, item: DCurrencyItem) => {
        acc += item.value;
        return acc;
      }, 0);
      console.log(
        "进入游戏弹窗=>",
        "配置充值次数、金额、货币,次数类型, 金额类型:",
        configRechargeTimes,
        configRechargeAmount,
        configCurrency,
        rechargeTimeType,
        configRechargeAmountType,
        "用户充值次数、金额、货币:",
        personRechargeTimes,
        personRechargeAmount,
        personCurrency
      );
      let timeSatisfy = false;
      let amountSatisfy = false;
      if (configRechargeAmountType >= 0) {
        switch (configRechargeAmountType) {
          case 0:
            amountSatisfy = ye <= 0;
            break;
          case 1:
            amountSatisfy =
              converterPlus(
                personRechargeAmount,
                personCurrency,
                configCurrency,
                allConfigData?.exchangeRateList,
                false
              ) < configRechargeAmount;
            break;
          case 2:
            amountSatisfy =
              converterPlus(
                personRechargeAmount,
                personCurrency,
                configCurrency,
                allConfigData?.exchangeRateList,
                false
              ) <= configRechargeAmount;
            break;
          case 3:
            amountSatisfy =
              converterPlus(
                personRechargeAmount,
                personCurrency,
                configCurrency,
                allConfigData?.exchangeRateList,
                false
              ) > configRechargeAmount;
            break;
          case 4:
            amountSatisfy =
              converterPlus(
                personRechargeAmount,
                personCurrency,
                configCurrency,
                allConfigData?.exchangeRateList,
                false
              ) >= configRechargeAmount;
            break;
          default:
            amountSatisfy =
              converterPlus(
                personRechargeAmount,
                personCurrency,
                configCurrency,
                allConfigData?.exchangeRateList,
                false
              ) == configRechargeAmount;
        }
      }
      if (configRechargeAmountType == 0) {
        if (personRechargeTimes > 0 && amountSatisfy) {
          amountSatisfy = false;
        } else {
          timeSatisfy = true;
        }
      } else if (rechargeTimeType > 0) {
        switch (rechargeTimeType) {
          case 1:
            timeSatisfy = personRechargeTimes < configRechargeTimes;
            break;
          case 2:
            timeSatisfy = personRechargeTimes <= configRechargeTimes;
            break;
          case 3:
            timeSatisfy = personRechargeTimes > configRechargeTimes;
            break;
          case 4:
            timeSatisfy = personRechargeTimes >= configRechargeTimes;
            break;
          default:
            timeSatisfy = personRechargeTimes === configRechargeTimes;
        }
      }
      return timeSatisfy && amountSatisfy;
    });
  };

  // QUEST弹窗 1
  // SPIN弹窗 2
  // 充值弹窗 3
  // 客服弹窗 4
  // 登录弹窗 5
  // 注册弹窗 6
  // 安装PWA 7
  // 注册挽留 8
  // 每日充值 9
  // 限时充值 10
  //立即游戏弹窗 - 11
  //PWA安装底部弹窗. 12
  //13-首充签到弹窗
  //14-直充弹窗
  const handleInnerJum = useCallback((jumpWhere: number, popupInfo: PopupInfo) => {
    console.log("内部跳转------", jumpWhere, popupInfo);
    if (isNotShow(popupInfo?.popupId)) {
      setNotOpen(true);
      return;
    }
    if (popupInfo && popupInfo?.showHint) {
      setShowHint(popupInfo?.popupId);
    } else {
      setShowHint(null);
    }
    if (jumpWhere === 4) {
      if (window.Intercom) {
        window.Intercom("show");
      } else {
        dispatch(setShowLiveSupport());
      }
    } else if (jumpWhere === 1) {
      EventBus.emit("showModal", ModalType.showQuestModal)
    } else if (jumpWhere === 2) {
      EventBus.emit("showModal", ModalType.showSpinModal)
    } else if (jumpWhere === 3) {
      if (popupInfo?.depositSource) {
        setDepostiSource(popupInfo?.depositSource);
      } else {
        setDepostiSource(110);
      }
      EventBus.emit("showModal", ModalType.showWalletModal)
    } else if (jumpWhere === 5) {
      EventBus.emit("showModal", ModalType.showLoginModal)
    } else if (jumpWhere === 6) {
      EventBus.emit("showModal", ModalType.showSignUpModal)
    } else if (jumpWhere === 7) {
      installPwa();
    } else if (jumpWhere === 8) {
      EventBus.emit("showModal", ModalType.showRetentionModal)
    } else if (jumpWhere === 9) {
      EventBus.emit("showModal", ModalType.showDailyRechargeModal)
    } else if (jumpWhere === 10) {
      EventBus.emit("showModal", ModalType.showLimitRechargeModal)
    } else if (jumpWhere === 11) {
      EventBus.emit("showModal", ModalType.showPlayNowModal)
    } else if (jumpWhere === 12) {
      EventBus.emit("showModal", ModalType.showPwaInstallModal)
    } else if (jumpWhere === 13) {
      EventBus.emit("showModal", ModalType.showFirstDepositModal)
    } else if (jumpWhere === 14) {
      // handlerOrder(popupInfo);
      if (popupInfo?.depositSource) {
        setDepostiSource(popupInfo?.depositSource);
      } else {
        setDepostiSource(305);
        popupInfo.depositSource = 305;
      }
      showDirectModal.current = true;
      showDirectModalPopup.current = popupInfo;
    }
    setNotOpen(false);
  }, []);

  useEffect(() => {
    if (showDirectModal.current && showDirectModalPopup.current && !rechargeLoading) {
      handlerOrder(showDirectModalPopup.current);
    }
  }, [
    showDirectModal.current,
    onlyCrpto,
    createOrder,
    rechargeLoading,
    showDirectModalPopup.current
  ]);

  const handlerOrder = useCallback(
    async (popupInfo: PopupInfo) => {
      if (onlyCrpto.current && !rechargeLoading) {
        setDepostiSource(103);
        EventBus.emit("showModal", ModalType.showWalletModal)
      } else {
        await createOrder(
          popupInfo?.firstRechargeAmount,
          popupInfo?.depositSource || 103,
          window.location.pathname,
          popupInfo?.firstRechargeCurrencyId,
          true
        );
        showDirectModal.current = false;
      }
    },
    [onlyCrpto, createOrder, rechargeLoading]
  );

  useEffect(() => {
    if (refreshPopupType && refreshPopupType?.length > 0) {
      console.log(
        "WebSocket 金额刷新返回-用户相关--调用刷新弹窗-监听到刷新类型变化",
        refreshPopupType
      );
      refreshPopupListMultiple(personValue, refreshPopupType);
      dispatch(setRefreshPopupType([]));
    }
  }, [refreshPopupType, personValue]);

  const handleJump = (popupInfo: BannerInfo, navigate) => {
    // 未登录跳转
    if (!popupInfo.notLoginJump && !isLogin) {
      EventBus.emit("showModal", ModalType.showSignUpModal)
      return;
    }
    console.log("跳转到内部链接", popupInfo);
    // 1.内部跳转 2.外部跳转
    const jumpType = popupInfo.jumpType;
    // 内部跳转链接
    const innerLinks = popupInfo.innerLinks;
    if (jumpType === 2) {
      window.open(popupInfo?.externalLinks, "_blank");
    } else if (innerLinks) {
      navigate(innerLinks);
    } else {
      handleInnerJum(popupInfo.popupLinks, popupInfo);
    }
  };

  return (
    <JumpContext.Provider
      value={{
        commonJumpto,
        setCommonJumpto,
        notOpen,
        setNotOpen,
        handleInnerJum,
        centerPopup,
        setCenterPopup,
        rightTopPopup,
        setRightTopPopup,
        rightDownPopup,
        setRightDownPopup,
        leftDownPopup, 
        setLeftDownPopup,
        refreshPopupListMultiple,
        refreshPopupList,
        widthdrwaGoogleAuthSuccess,
        setWidthdrwaGoogleAuthSuccess,
        show2FA_showGoogleTip,
        setShow2FA_showGoogleTip,
        widthdrwaType,
        setWidthdrwaType,
        widthdrwaGoogleAuthSuccess_virtual,
        setWidthdrwaGoogleAuthSuccess_virtual,
        handleJump,
        isLimitRecharge,
        setIsLimitRecharge,
        depositSource,
        setDepostiSource,
        setShowHint,
        showHint,
        withdrawCheckSuccessType, 
        setWithdrawCheckSuccessType
      }}
    >
      {children}
    </JumpContext.Provider>
  );
};

export const useJump = () => useContext(JumpContext);
export default JumpProvider;
