import signle_closeIcon from "@/assets/header/signle_close.svg";
import { useAllGame } from "@/components/games/AllGameProvider";
import MobilePwa from "@/components/mobile/MobilePwa";
import { BackStageMessage } from "@/protos/BackStage";
import { setIsIOSPwa } from "@/store/iosPwa";
import { setPupupIndex } from "@/store/popup";
import { useScreenWidth } from "@/utils/MobileDetectionContext";
import { memo, useEffect, useState } from "react";
import { isIOS } from "react-device-detect";
import { useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import { useDeferred } from "../context/useDeferredContext";
import { useConfigData } from "@/components/games/providers/ProjectConfigProvider";
import { FuntionOpenMapping } from "@/utils/enums";

const PwaDialog: React.FC = memo(() => {
  console.log("PwaDialog渲染了=========>");
  const [pwaTop, setPwaTop] = useState("-50vh");
  const [showPwa, setShowPwa] = useState(false);
  const { screenWidth } = useScreenWidth();
  const { allConfigData } = useAllGame() as {
    allConfigData: BackStageMessage.ResConfigDataMessage;
  };
  const { deferredPromptEvent } = useDeferred() || {};
  const location = useLocation();
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const dispatch = useDispatch();
  const { functionSwitchIds } = useConfigData() as { functionSwitchIds: number[] };

  useEffect(() => {
    let popiTimer = null;
    let timer = null;
    if (functionSwitchIds) {
      if (
        functionSwitchIds &&
        functionSwitchIds.length > 0 &&
        !functionSwitchIds?.includes(FuntionOpenMapping.show_pwa)
      ) {
        dispatch(setPupupIndex(1));
        return;
      }
      console.log("用户可以安装-监听到数据", deferredPromptEvent, location.pathname);
      if (deferredPromptEvent && location.pathname == "/") {
        setDeferredPrompt(deferredPromptEvent);
        popiTimer = setTimeout(() => {
          if (screenWidth <= 1024) {
            setShowPwa(true);
          } else {
            setPwaTop("10px");
          }
        }, 500);
      }
      timer = setTimeout(() => {
        if (!deferredPromptEvent && location.pathname == "/") {
          dispatch(setPupupIndex(1));
        }
      }, 8000);
    }
    //  避免一直拿不到事件

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
      if (popiTimer) {
        clearTimeout(popiTimer);
      }
    };
  }, [deferredPromptEvent, functionSwitchIds]);

  const handleInstallClick = async () => {
    if (isIOS) {
      dispatch(setIsIOSPwa(true));
      return;
    }

    console.log("用户点击安装按钮", deferredPrompt);
    if (deferredPrompt) {
      // 显示安装提示
      deferredPrompt.prompt();
      // 等待用户选择
      const { outcome } = await deferredPrompt.userChoice;
      if (outcome === "accepted") {
        console.log("用户接受了安装");
        localStorage.setItem("pwa_install", "true");
        setPwaTop("-100%");
        setShowPwa(false);
      } else {
        console.log("用户取消了安装");
      }

      // 安装后清空 `deferredPrompt`
      setDeferredPrompt(null);
      setShowPwa(false);
    } else {
      localStorage.setItem("pwa_install", "true");
    }
  };

  const hidePwa = () => {
    setShowPwa(false);
    setPwaTop("-50vh");
    dispatch(setPupupIndex(1));
  };

  return (
    <>
      {screenWidth > 1024 && (
        <div
          className=" px-4 flex items-center justify-center h-[56px] w-[26%] rounded-lg pwa_div gap-x-1 fixed left-[50%] translate-x-[-50%] z-[9999] transition-all duration-200 ease-in-out "
          style={{ top: pwaTop }}
        >
          <div className="absolute top-1 right-1 cursor-pointer" onClick={() => hidePwa()}>
            {/* <CloseOutlined className='text-[10px]/[10px] text-primary-text' /> */}
            <img src={signle_closeIcon} alt="" className="w-[10px] h-[10px]" />
          </div>
          <span
            className="text-sm/4 font-semibold text-primary-text w-[78%] overflow-hidden"
            dangerouslySetInnerHTML={{ __html: allConfigData?.pwaInfo?.pwaInfo }}
          ></span>
          <div
            onClick={() => handleInstallClick()}
            className="pwa_button cursor-pointer gap-x-2 h-[44px] rounded-[34px] flex border-2 border-[var(--pwa-border-color)] w-[29.5%] items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                d="M2.5 10.0035V17.5H17.5V10"
                stroke={`var(--pwa-text-color)`}
                stroke-width="1.66667"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M13.75 9.58337L10 13.3334L6.25 9.58337"
                stroke={`var(--pwa-text-color)`}
                stroke-width="1.66667"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M9.99658 2.5V13.3333"
                stroke={`var(--pwa-text-color)`}
                stroke-width="1.66667"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <span className="text-sm/4 font-semibold text-[--pwa-text-color] ">PWA</span>
          </div>
        </div>
      )}

      <MobilePwa
        showModal={showPwa}
        handleCancel={() => hidePwa()}
        callBack={handleInstallClick}
        pwaInfo={allConfigData?.pwaInfo}
      />
    </>
  );
});

export default PwaDialog;
