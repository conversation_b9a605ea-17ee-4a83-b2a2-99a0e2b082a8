
import { useAllGame } from "@/components/games/AllGameProvider";
import { message } from "antd";
import { useCallback, useEffect } from "react";
import { FormattedMessage } from "react-intl";
import "./Winning.css";

const Winning: React.FC = () => {
  const { binggoValue } = useAllGame();
  const [messageApi, contextHolder] = message.useMessage();
  const success = useCallback(() => {
    messageApi.open({
      content: (
        <>
          <FormattedMessage id="congratulations" /> {binggoValue}
        </>
      ),
      className: "custom_style",
      style: {
        marginTop: "20vh"
      },
    });
  }, [binggoValue]);

  useEffect(() => {
    if (binggoValue) {
      success();
    }
  }, [binggoValue]);

  return (
    <>
      <div >{contextHolder}</div>

      {/* {binggoValue && (
        <div className="binggo">
          <div
            style={{
              backgroundImage: `url(${bingouIcon})`,
              backgroundRepeat: "no-repeat",
              backgroundSize: "cover",
              backgroundPosition: "center"
            }}
            className="  flex items-center justify-center relative h-[73%] w-[100%] lg:w-[927px] lg:h-[835px] left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
          >
            <div
              style={{
                backgroundImage: `url(${jinbIcon})`,
                backgroundSize: "contain",
                backgroundRepeat: "no-repeat",
                backgroundPosition: "center"
              }}
              className="lg:w-[600px] w-[80%] h-[500px] absolute -translate-y-8"
            ></div>
            <div className=" relative flex flex-col items-center lg:w-[432px] w-[269px] h-[238px] lg:h-[375px] bg-gradient-to-b from-[#e0618c] to-[#7653d0] rounded-2xl">
              <span className="mt-10 text-2xl text-primary-text">
                <FormattedMessage id="congratulations" />
              </span>
              <span className="relative top-[35%] text-sub-text-yellow text-4xl font-semibold">
                {binggoValue}
              </span>
              <img
                onClick={() => setBinggoValue(null)}
                src={closeIcon}
                alt=""
                className={`${
                  screenWidth <= 1024 && "w-[25px]"
                } cursor-pointer absolute lg:-bottom-16 -bottom-10`}
              />
            </div>
          </div>
        </div>
      )} */}
    </>
  );
};

export default Winning;
